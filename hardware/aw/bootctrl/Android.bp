cc_library_shared {
    name: "bootctrl.aw",
    relative_install_path: "hw",
    vendor: true,
    recovery_available: true,
    stem: "bootctrl.default",
    srcs: ["legacy_boot_control.cpp"],
    header_libs: ["libhardware_headers"],

    static_libs: [
        "libbootloader_message_vendor",
        "libfstab",
        "libboot_control",
    ],
    shared_libs: [
        "libbase",
        "libcutils",
        "liblog",
        "libhardware",
    ],
    owner: "allwinner",
    cflags: [
        "-Wall",
        "-Werror",
        "-Wno-unused-parameter",
    ],
}

cc_defaults {
    name: "libboot_control_defaults",
    vendor: true,
    relative_install_path: "hw",

    cflags: [
        "-D_FILE_OFFSET_BITS=64",
        "-Werror",
        "-Wall",
        "-Wextra",
    ],

    shared_libs: [
        "libbase",
        "liblog",
    ],
    static_libs: [
        "libbootloader_message_vendor",
        "libfstab",
    ],
}

cc_library_static {
    name: "libboot_control",
    recovery_available: true,
    defaults: ["libboot_control_defaults"],
    export_include_dirs: ["include"],

    srcs: ["libboot_control.cpp"],
}
