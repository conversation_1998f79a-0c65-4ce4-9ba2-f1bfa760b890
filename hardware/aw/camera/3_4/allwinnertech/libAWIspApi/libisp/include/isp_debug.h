
/*
 ******************************************************************************
 *
 * isp_debug.h
 *
 * Hawkview ISP - isp_debug.h module
 *
 * Copyright (c) 2016 by Allwinnertech Co., Ltd.  http://www.allwinnertech.com
 *
 * Version		  Author         Date		    Description
 *
 *   3.0		  Yang Feng   	2016/03/16	VIDEO INPUT
 *
 *****************************************************************************
 */

#ifndef _DEBUG_H_
#define _DEBUG_H_

#include <log/log.h>
#include <stdio.h>
#include <errno.h>
//#define ISP_DGB_FL

//#define ISP_ERR(x,arg...) printf("[ISP_ERR] func: %s, line: %d,"x, __FUNCTION__, __LINE__, ##arg)
//#define ISP_WARN(x,arg...) printf("[ISP_WARN]"x, ##arg)
//#define ISP_PRINT(x,arg...) printf("[ISP]"x, ##arg)
#define ISP_WARN(fmt,arg...) ALOGW(fmt, ##arg)
#define ISP_PRINT(fmt,arg...) ALOGV(fmt, ##arg)
#define ISP_ERR(fmt, arg...) ALOGE(fmt, ##arg)

#ifdef ISP_DGB_FL
#define  FUNCTION_LOG do { printf("%s, line: %d\n", __FUNCTION__, __LINE__); } while(0)
#else
#define  FUNCTION_LOG do { } while(0)
#endif

#define ISP_LOG_AE				(1 << 0)	//0x1
#define ISP_LOG_AWB				(1 << 1)	//0x2
#define ISP_LOG_AF				(1 << 2)	//0x4
#define ISP_LOG_DENOISE_SHARP			(1 << 3)	//0x8
#define ISP_LOG_BRI_CON				(1 << 4)	//0x10
#define ISP_LOG_COLOR_MATRIX			(1 << 5)	//0x20
#define ISP_LOG_AFS				(1 << 6)	//0x40
#define ISP_LOG_MOTION_DETECT			(1 << 7)	//0x80
#define ISP_LOG_GAIN_OFFSET			(1 << 8)	//0x100
#define ISP_LOG_DEFOG				(1 << 9)	//0x200
#define ISP_LOG_LSC				(1 << 10)	//0x400
#define ISP_LOG_COLOR_DENOISE			(1 << 11)	//0x800
#define ISP_LOG_HIST_EQ				(1 << 12)	//0x1000
#define ISP_LOG_GAMMA				(1 << 13)	//0x2000
#define ISP_LOG_DRC				(1 << 14)	//0x4000

#define ISP_LOG_EVENTS				(1 << 15)	//0x8000
#define ISP_LOG_CTRL				(1 << 16)	//0x10000
#define ISP_LOG_MEDIA				(1 << 17)	//0x20000
#define ISP_LOG_STAT				(1 << 18)	//0x40000
#define ISP_LOG_V4L2				(1 << 19)	//0x80000
#define ISP_LOG_SUBDEV				(1 << 20)	//0x100000
#define ISP_LOG_CFG				(1 << 21)	//0x200000
#define ISP_LOG_ISO				(1 << 22)	//0x400000
#define ISP_LOG_GTM				(1 << 23)	//0x800000
#define ISP_LOG_VIDEO				(1 << 24)	//0x1000000
#define ISP_LOG_CSI				(1 << 25)	//0x2000000
#define ISP_LOG_ISP				(1 << 26)	//0x4000000
#define ISP_LOG_PLTM				(1 << 27)	//0x8000000
#define ISP_LOG_ALL				((ISP_LOG_PLTM << 1) -1)

#define ISP_DEV_LOG(flag, msg...)
#define ISP_LIB_LOG(flag, msg...)

#define ISP_CFG_LOG(flag, msg...)
#define ISP_TMP_DBG \
	do { \
		printf("%s, line: %d\n",\
		__FUNCTION__, __LINE__);\
	} while(0);\


#endif /*_DEBUG_H_*/


