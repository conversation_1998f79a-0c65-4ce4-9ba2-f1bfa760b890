/*
 *****************************************************************************
 * gc5024
 * 2592x1936@1fps, wdr: 0
 * Hawkview ISP - gc5024 config module
 * Copyright (c) 2018 by Allwinnertech Co., Ltd. http://www.allwinnertech.com
 *  Version  |     Author      |     Date     |      Description
 *    2.0    |  Hawkview Tool  |  2018/05/11  |  Automatic generation.
 *
 *****************************************************************************
 */

#ifndef _GC5024_H_V100_
#define _GC5024_H_V100_

#include "../../include/isp_ini_parse.h"

struct isp_test_param gc5024_isp_test_settings = {
	.isp_test_mode = 0,
	.isp_test_exptime = 0,
	.exp_line_start = 1000,
	.exp_line_step = 1000,
	.exp_line_end = 32000,
	.exp_change_interval = 5,
	.isp_test_gain = 0,
	.gain_start = 256,
	.gain_step = 64,
	.gain_end = 4096,
	.gain_change_interval = 5,
	.isp_test_focus = 0,
	.focus_start = 10,
	.focus_step = 10,
	.focus_end = 800,
	.focus_change_interval = 5,
	.isp_log_param = 0,
	.isp_gain = 256,
	.isp_exp_line = 20000,
	.isp_color_temp = 6500,
	.ae_forced = 0,
	.lum_forced = 80,
	.manual_en = 1,
	.afs_en = 1,
	.sharp_en = 1,
	.contrast_en = 1,
	.denoise_en = 1,
	.drc_en = 1,
	.cem_en = 0,
	.lsc_en = 1,
	.gamma_en = 1,
	.cm_en = 1,
	.ae_en = 1,
	.af_en = 1,
	.awb_en = 1,
	.hist_en = 1,
	.blc_en = 0,
	.so_en = 0,
	.wb_en = 1,
	.otf_dpc_en = 1,
	.cfa_en = 1,
	.tdf_en = 0,
	.cnr_en = 1,
	.satur_en = 1,
	.defog_en = 0,
	.linear_en = 0,
	.gtm_en = 0,
	.dig_gain_en = 1,
	.pltm_en = 0,
	.wdr_en = 0,
	.ctc_en = 0
};
struct isp_3a_param gc5024_isp_3a_settings = {
	.define_ae_table = 1,
	.ae_max_lv = 1650,
	.ae_table_preview_length = 2,
	.ae_table_preview = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_capture_length = 2,
	.ae_table_capture = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_video_length = 2,
	.ae_table_video = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_win_weight = {
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     8,     8,     4,     4,     4,
		    4,     4,     6,     8,     8,     6,     4,     4,
		    4,     6,     8,     8,     8,     8,     6,     4,
		    4,     8,     8,     8,     8,     8,     8,     4,
		    4,     4,     4,     4,     4,     4,     4,     4
	},
	.ae_hist_mod_en = 1,
	.ae_hist_sel = 0,
	.ae_stat_sel = 2,
	.ae_ki = 50,
	.ae_ConvDataIndex = 3,
	.ae_blowout_pre_en = 0,
	.ae_blowout_attr = 30,
	.ae_delay_frame = 0,
	.exp_delay_frame = 2,
	.gain_delay_frame = 2,
	.exp_comp_step = 4,
	.ae_touch_dist_ind = 0,
	.ae_iso2gain_ratio = 16,
	.ae_fno_step = {
		  141,   145,   152,   163,   175,   190,   209,   233,
		  266,   311,   379,   487,   657,   971,  1825,  3794
	},
	.wdr_cfg = {
		   16,   128,  1280
	},
	.awb_interval = 2,
	.awb_speed = 16,
	.awb_stat_sel = 0,
	.awb_color_temper_low = 1800,
	.awb_color_temper_high = 8000,
	.awb_base_temper = 6500,
	.awb_green_zone_dist = 63,
	.awb_blue_sky_dist = 63,
	.awb_light_num = 9,
	.awb_light_info = {
		  271,   256,   104,   256,   256,   256,    64,  1900,    32,    90,
		  248,   256,   112,   256,   256,   256,    64,  2500,    32,    90,
		  234,   256,   118,   256,   256,   256,    64,  2800,    32,    90,
		  180,   256,   138,   256,   256,   256,    64,  4000,    96,   100,
		  154,   256,   126,   256,   256,   256,    64,  4100,    64,   100,
		  154,   256,   160,   256,   256,   256,    60,  5000,   100,   100,
		  130,   256,   151,   256,   256,   256,    64,  5300,    64,   100,
		  135,   256,   191,   256,   256,   256,    64,  6500,    64,   100,
		  122,   256,   220,   256,   256,   256,    64,  7500,    64,   100
	},
	.awb_ext_light_num = 0,
	.awb_ext_light_info = {
		0
	},
	.awb_skin_color_num = 0,
	.awb_skin_color_info = {
		0
	},
	.awb_special_color_num = 0,
	.awb_special_color_info = {
		0
	},
	.awb_preset_gain = {
		  256,   256,   256,   256,   151,   405,   210,   340,   210,   340,
		  145,   480,   265,   256,   256,   256,   285,   245,   280,   235,
		  140,   480
	},
	.awb_rgain_favor = 256,
	.awb_bgain_favor = 256,
	.af_use_otp = 0,
	.vcm_min_code = 150,
	.vcm_max_code = 550,
	.af_interval_time = 100,
	.af_speed_ind = 20,
	.af_auto_fine_en = 1,
	.af_single_fine_en = 0,
	.af_fine_step = 10,
	.af_move_cnt = 4,
	.af_still_cnt = 2,
	.af_move_monitor_cnt = 6,
	.af_still_monitor_cnt = 3,
	.af_stable_min = 245,
	.af_stable_max = 265,
	.af_low_light_lv = 10,
	.af_near_tolerance = 15,
	.af_far_tolerance = 25,
	.af_tolerance_off = 0,
	.af_peak_th = 100,
	.af_dir_th = 10,
	.af_change_ratio = 30,
	.af_move_minus = 2,
	.af_still_minus = 1,
	.af_scene_motion_th = 0,
	.af_tolerance_tbl_len = 0,
	.af_std_code_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	},
	.af_tolerance_value_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	}
};
struct isp_dynamic_param gc5024_isp_iso_settings = {
	.triger = {
		.sharp_triger = 1,
		.contrast_triger = 1,
		.denoise_triger = 1,
		.sensor_offset_triger = 1,
		.black_level_triger = 1,
		.dpc_triger = 1,
		.defog_value_triger = 0,
		.pltm_dynamic_triger = 0,
		.brightness_triger = 0,
		.gcontrast_triger = 0,
		.saturation_triger = 1,
		.cem_ratio_triger = 0,
		.tdf_triger = 0,
		.color_denoise_triger = 1,
		.ae_cfg_triger = 0,
		.gtm_cfg_triger = 1
	},
	.isp_lum_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_gain_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_dynamic_cfg[0] = {
		.sharp_cfg = {
			8, 20, 188, 188, 188, 188, 256, 0, 256, 0
		},
		.contrast_cfg = {
			1, 16, 36, 12, 105, 512, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			100, 0, 80, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			16, 16, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 160, 80, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 256, 0, 256, 0, 256, 0, 256, 0
		},
		.color_denoise = 4,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 62, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[1] = {
		.sharp_cfg = {
			8, 24, 177, 177, 177, 177, 256, 0, 256, 0
		},
		.contrast_cfg = {
			2, 16, 36, 12, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			200, 0, 160, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			12, 12, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 160, 80, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 576, 0, 576, 0, 384, 0, 384, 0
		},
		.color_denoise = 16,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 62, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[2] = {
		.sharp_cfg = {
			8, 28, 166, 166, 166, 166, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			300, 0, 240, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			8, 8, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 128, 40, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 768, 0, 768, 0, 512, 0, 512, 0
		},
		.color_denoise = 32,
		.ae_cfg = {
			380, 192, 256, 256, 12, 12, 12, 12, 3, 64, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[3] = {
		.sharp_cfg = {
			8, 32, 155, 155, 155, 155, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			400, 0, 320, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			4, 4, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 64, 20, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 960, 0, 960, 0, 768, 0, 768, 0
		},
		.color_denoise = 50,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 64, 4, 12, 16, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[4] = {
		.sharp_cfg = {
			8, 36, 144, 144, 144, 144, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 36, 12, 105, 256, 64, 256, 0, 256, 0
		},
		.denoise_cfg = {
			500, 0, 400, 0
		},
		.sensor_offset = {
			-240, -240, -240, -240
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			2, 3, 2047, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 32, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1152, 0, 1152, 0, 1152, 0, 1152, 0
		},
		.color_denoise = 80,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 16, 12, 48
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[5] = {
		.sharp_cfg = {
			10, 40, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 34, 12, 105, 256, 80, 256, 0, 256, 0
		},
		.denoise_cfg = {
			600, 0, 480, 0
		},
		.sensor_offset = {
			-240, -240, -240, -240
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1600, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 16, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1344, 0, 1344, 0, 1536, 0, 1536, 0
		},
		.color_denoise = 120,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 24, 8, 32
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[6] = {
		.sharp_cfg = {
			12, 50, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 30, 12, 105, 256, 96, 256, 0, 256, 0
		},
		.denoise_cfg = {
			512, 0, 512, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1536, 0, 1536, 0, 1920, 0, 1920, 0
		},
		.color_denoise = 160,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 6, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[7] = {
		.sharp_cfg = {
			14, 60, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 26, 12, 105, 256, 104, 256, 0, 256, 0
		},
		.denoise_cfg = {
			768, 0, 768, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1728, 0, 1728, 0, 2340, 0, 2340, 0
		},
		.color_denoise = 100,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[8] = {
		.sharp_cfg = {
			16, 70, 33, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 22, 12, 105, 256, 112, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1000, 0, 1280, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1920, 0, 1920, 0, 2560, 0, 2560, 0
		},
		.color_denoise = 122,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[9] = {
		.sharp_cfg = {
			18, 80, 33, 133, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 18, 10, 105, 200, 128, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1200, 0, 1200, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2112, 0, 2112, 0, 2816, 0, 2816, 0
		},
		.color_denoise = 144,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[10] = {
		.sharp_cfg = {
			20, 90, 122, 122, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 14, 8, 105, 199, 160, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2200, 0, 2000, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2304, 0, 2304, 0, 3072, 0, 3072, 0
		},
		.color_denoise = 166,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[11] = {
		.sharp_cfg = {
			22, 100, 88, 88, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 10, 7, 88, 188, 256, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2800, 0, 2240, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2496, 0, 2496, 0, 3238, 0, 3238, 0
		},
		.color_denoise = 188,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[12] = {
		.sharp_cfg = {
			24, 110, 66, 66, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 8, 6, 77, 177, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			3600, 0, 2360, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2688, 0, 2688, 0, 3584, 0, 3584, 0
		},
		.color_denoise = 200,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[13] = {
		.sharp_cfg = {
			26, 120, 44, 44, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 4, 3, 66, 166, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			4400, 0, 2560, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 3072, 0, 3072, 0, 4096, 0, 4096, 0
		},
		.color_denoise = 222,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	}
};
struct isp_tunning_param gc5024_isp_tuning_settings = {
	.flash_gain = 80,
	.flash_delay_frame = 16,
	.flicker_type = 0,
	.flicker_ratio = 15,
	.hor_visual_angle = 60,
	.ver_visual_angle = 40,
	.focus_length = 300,
	.gamma_num = 5,
	.rolloff_ratio = 8,
	.gtm_type = 1,
	.gamma_type = 1,
	.auto_alpha_en = 1,
	.cfa_dir_th = 2047,
	.ctc_th_max = 316,
	.ctc_th_min = 60,
	.ctc_th_slope = 262,
	.ctc_dir_wt = 64,
	.ctc_dir_th = 80,
	.bayer_gain = {
		 1024,  1024,  1024,  1024
	},
	.ff_mod = 1,
	.lsc_center_x = 2048,
	.lsc_center_y = 2048,
	.lsc_trig_cfg = {
		 2200,  2800,  4000,  6500,  5500,  6500
	},
	.gamma_trig_cfg = {
		 1300,  1100,   900,   600,   300
	},
	.color_matrix_ini[0] = {
		.matrix = { { 271, 0, -15 }, { -154, 525, -115 },
				{ -126, -111, 493 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[1] = {
		.matrix = { { 362, 0, -106 }, { -120, 426, -50 },
				{ -69, -91, 416 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[2] = {
		.matrix = { { 407, -46, -105 }, { -101, 411, -54 },
				{ -40, -70, 366 } },
		.offset = { 0, 0, 0 }
	},
	.cm_trig_cfg = {
		 2700,  4000,  6500
	},
	.pltm_cfg = {
		    1,     0,    10,     7,  2048,  2048,     0,    15,
		   15,   210,    32,   255,    23,    31,    34
	},
	.isp_bdnf_th = {
		    7,    18,    25,    28,    39,    66,    94,   116,
		  138,   133,   111,   105,   105,    94,    77,    77,
		   77,    72,    59,    62,    63,    66,    67,    70,
		   71,    74,    76,    78,    80,    83,    84,    87,
		   88
	},
	.isp_tdnf_th = {
		    4,     4,     5,     6,     7,     8,     9,    10,
		   11,    12,    13,    14,    15,    16,    17,    18,
		   19,    20,    21,    22,    23,    24,    25,    26,
		   27,    28,    29,    30,    31,    32,    32,    32,
		   32
	},
	.isp_tdnf_ref_noise = {
		    6,     6,     7,     8,     8,     9,    10,    11,
		   12,    13,    14,    15,    16,    17,    18,    19,
		   20,    21,    22,    23,    24,    25,    26,    27,
		   28,    29,    30,    31,    32,    32,    32,    32,
		   32
	},
	.isp_tdnf_k = {
		    4,     4,     6,     8,    12,    16,    24,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31
	},
	.isp_contrast_val = {
		  103,   160,   160,   160,   160,   160,   176,   192,
		  208,   208,   208,   208,   208,   208,   208,   208,
		  208,   208,   180,   160,   144,   128,   112,    96,
		   80,    72,    64,    56,    48,    32,    32,    32,
		   32
	},
	.isp_contrast_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_sharp_val = {
		  144,   132,   128,   128,   128,   128,   124,   117,
		   96,    96,    80,    80,    64,    64,    48,    48,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32
	},
	.isp_sharp_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_tdnf_diff = {
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   254,   254,   254,   254,   254,   254,   253,
		  253,   253,   253,   253,   252,   252,   252,   252,
		  252,   251,   251,   251,   250,   250,   250,   250,
		  249,   249,   249,   248,   248,   248,   247,   247,
		  247,   246,   246,   245,   245,   245,   244,   244,
		  243,   243,   242,   242,   241,   241,   240,   240,
		  240,   239,   238,   238,   237,   237,   236,   236,
		  235,   235,   234,   234,   233,   232,   232,   231,
		  231,   230,   229,   229,   228,   227,   227,   226,
		  225,   225,   224,   223,   222,   222,   221,   220,
		  220,   219,   218,   217,   216,   216,   215,   214,
		  213,   212,   212,   211,   210,   209,   208,   207,
		  207,   206,   205,   204,   203,   202,   201,   200,
		  199,   198,   197,   196,   195,   194,   193,   192,
		  192,   190,   189,   188,   187,   186,   185,   184,
		  183,   182,   181,   180,   179,   178,   177,   176,
		  175,   173,   172,   171,   170,   169,   168,   166,
		  165,   164,   163,   162,   160,   159,   158,   157,
		  156,   154,   153,   152,   150,   149,   148,   147,
		  145,   144,   143,   141,   140,   139,   137,   136,
		  135,   133,   132,   130,   129,   128,   126,   125,
		  123,   122,   120,   119,   117,   116,   114,   113,
		  112,   110,   108,   107,   105,   104,   102,   101,
		   99,    98,    96,    95,    93,    91,    90,    88,
		   87,    85,    83,    82,    80,    78,    77,    75,
		   73,    72,    70,    68,    66,    65,    63,    61,
		   60,    58,    56,    54,    52,    51,    49,    47,
		   45,    43,    42,    40,    38,    36,    34,    32,
		   31,    29,    27,    25,    23,    21,    19,    17,
		   15,    13,    11,     9,     7,     5,     3,     1
	},
	.isp_contrat_pe = {
		    0,     2,     4,     6,     8,    10,    12,    14,
		   16,    26,    36,    46,    56,    66,    76,    86,
		   96,   100,   104,   108,   112,   116,   120,   124,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   130,   132,   134,   136,   138,   140,   142,
		  144,   146,   148,   150,   152,   154,   156,   158,
		  160,   164,   168,   172,   176,   180,   184,   188,
		  192,   195,   197,   200,   202,   205,   207,   209,
		  212,   209,   207,   205,   202,   200,   197,   195,
		  192,   188,   184,   180,   176,   172,   168,   164,
		  160,   158,   156,   154,   152,   150,   148,   146,
		  144,   142,   140,   138,   136,   134,   132,   130,
		  128,   126,   124,   122,   120,   118,   116,   114,
		  112,   110,   108,   106,   104,   102,   100,    98,
		   96,    96,    96,    96,    96,    96,    96,    96,
		   96,    96,    96,    96,    96,    96,    96,    96
	},
	.gamma_tbl_ini = {
	{
		/* gamma - 0 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 1 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 2 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 3 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 4 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	}
	},
	.lsc_tbl = {
	{
		/* lsc - 0 */
		/* R */
		 1007, 1030, 1049, 1067, 1085, 1104, 1122, 1140,
		 1157, 1174, 1192, 1212, 1231, 1249, 1267, 1284,
		 1302, 1321, 1339, 1358, 1377, 1396, 1416, 1436,
		 1457, 1478, 1500, 1520, 1541, 1561, 1581, 1601,
		 1621, 1641, 1662, 1681, 1701, 1721, 1740, 1759,
		 1780, 1799, 1817, 1835, 1855, 1874, 1890, 1907,
		 1926, 1946, 1965, 1984, 2005, 2024, 2044, 2063,
		 2083, 2103, 2124, 2143, 2162, 2182, 2203, 2223,
		 2243, 2261, 2281, 2300, 2319, 2337, 2357, 2375,
		 2395, 2413, 2432, 2449, 2468, 2486, 2505, 2521,
		 2539, 2558, 2576, 2592, 2611, 2627, 2644, 2659,
		 2677, 2692, 2709, 2724, 2740, 2755, 2771, 2787,
		 2803, 2820, 2835, 2848, 2863, 2882, 2900, 2913,
		 2927, 2942, 2961, 2978, 2994, 3008, 3028, 3045,
		 3060, 3071, 3089, 3104, 3126, 3140, 3158, 3174,
		 3202, 3220, 3235, 3251, 3273, 3294, 3313, 3336,
		 3354, 3371, 3390, 3414, 3433, 3454, 3474, 3493,
		 3508, 3532, 3549, 3559, 3570, 3586, 3599, 3613,
		 3629, 3642, 3653, 3678, 3685, 3703, 3704, 3718,
		 3733, 3751, 3766, 3784, 3790, 3775, 3755, 3730,
		 3732, 3735, 3737, 3739, 3741, 3743, 3746, 3748,
		 3750, 3752, 3754, 3757, 3759, 3761, 3763, 3765,
		 3767, 3769, 3771, 3774, 3776, 3778, 3780, 3782,
		 3784, 3786, 3788, 3790, 3792, 3794, 3796, 3798,
		 3800, 3802, 3804, 3806, 3808, 3810, 3812, 3814,
		 3816, 3818, 3820, 3822, 3824, 3826, 3828, 3830,
		 3832, 3834, 3836, 3838, 3839, 3841, 3843, 3845,
		 3847, 3849, 3851, 3852, 3854, 3856, 3858, 3860,
		 3862, 3863, 3865, 3867, 3869, 3870, 3872, 3874,
		 3876, 3878, 3879, 3881, 3883, 3884, 3886, 3888,
		 3890, 3891, 3893, 3895, 3896, 3898, 3900, 3901,
		 3903, 3905, 3906, 3908, 3909, 3911, 3913, 3914,
		/* G */
		 1015, 1029, 1042, 1055, 1067, 1080, 1091, 1104,
		 1116, 1127, 1138, 1151, 1162, 1173, 1184, 1194,
		 1206, 1217, 1229, 1239, 1251, 1262, 1274, 1285,
		 1298, 1310, 1322, 1333, 1345, 1357, 1369, 1380,
		 1391, 1401, 1412, 1422, 1433, 1444, 1454, 1463,
		 1474, 1484, 1494, 1502, 1512, 1522, 1532, 1541,
		 1550, 1560, 1571, 1581, 1591, 1601, 1612, 1623,
		 1635, 1646, 1655, 1664, 1672, 1682, 1691, 1700,
		 1710, 1720, 1731, 1740, 1750, 1760, 1770, 1778,
		 1788, 1798, 1809, 1819, 1829, 1839, 1848, 1856,
		 1866, 1875, 1886, 1896, 1905, 1913, 1923, 1931,
		 1940, 1950, 1961, 1971, 1981, 1992, 2002, 2013,
		 2024, 2037, 2047, 2058, 2071, 2087, 2097, 2103,
		 2109, 2115, 2124, 2133, 2142, 2148, 2158, 2168,
		 2179, 2187, 2198, 2208, 2220, 2231, 2243, 2254,
		 2269, 2279, 2290, 2301, 2316, 2327, 2337, 2350,
		 2362, 2372, 2384, 2401, 2415, 2426, 2438, 2450,
		 2462, 2478, 2491, 2500, 2511, 2524, 2539, 2552,
		 2569, 2579, 2595, 2615, 2630, 2641, 2656, 2674,
		 2684, 2700, 2719, 2733, 2749, 2766, 2798, 2846,
		 2849, 2852, 2855, 2858, 2860, 2863, 2866, 2869,
		 2872, 2874, 2877, 2880, 2883, 2886, 2888, 2891,
		 2894, 2897, 2899, 2902, 2905, 2907, 2910, 2913,
		 2915, 2918, 2921, 2924, 2926, 2929, 2931, 2934,
		 2937, 2939, 2942, 2945, 2947, 2950, 2952, 2955,
		 2958, 2960, 2963, 2965, 2968, 2970, 2973, 2975,
		 2978, 2981, 2983, 2986, 2988, 2991, 2993, 2996,
		 2998, 3000, 3003, 3005, 3008, 3010, 3013, 3015,
		 3018, 3020, 3022, 3025, 3027, 3030, 3032, 3034,
		 3037, 3039, 3041, 3044, 3046, 3048, 3051, 3053,
		 3055, 3058, 3060, 3062, 3065, 3067, 3069, 3071,
		 3074, 3076, 3078, 3080, 3083, 3085, 3087, 3089,
		/* B */
		 1008, 1031, 1048, 1063, 1078, 1093, 1107, 1120,
		 1130, 1140, 1152, 1166, 1179, 1187, 1195, 1202,
		 1212, 1223, 1235, 1246, 1255, 1265, 1275, 1283,
		 1292, 1302, 1312, 1320, 1328, 1335, 1343, 1349,
		 1358, 1368, 1378, 1386, 1394, 1401, 1406, 1411,
		 1417, 1421, 1424, 1428, 1435, 1442, 1448, 1455,
		 1464, 1473, 1479, 1483, 1487, 1492, 1498, 1502,
		 1507, 1515, 1524, 1533, 1542, 1552, 1561, 1568,
		 1576, 1584, 1590, 1595, 1601, 1606, 1612, 1616,
		 1624, 1630, 1636, 1641, 1648, 1652, 1656, 1663,
		 1673, 1683, 1688, 1694, 1704, 1714, 1724, 1732,
		 1740, 1745, 1754, 1762, 1772, 1779, 1785, 1790,
		 1796, 1802, 1806, 1811, 1818, 1828, 1837, 1842,
		 1846, 1852, 1861, 1868, 1875, 1879, 1887, 1896,
		 1908, 1917, 1928, 1935, 1947, 1957, 1969, 1980,
		 1998, 2012, 2022, 2029, 2044, 2057, 2066, 2072,
		 2081, 2093, 2104, 2118, 2129, 2141, 2146, 2152,
		 2160, 2180, 2191, 2197, 2199, 2206, 2212, 2221,
		 2233, 2242, 2245, 2253, 2258, 2272, 2280, 2289,
		 2290, 2291, 2306, 2319, 2335, 2341, 2365, 2385,
		 2388, 2391, 2394, 2398, 2401, 2404, 2407, 2410,
		 2413, 2416, 2419, 2423, 2426, 2429, 2432, 2435,
		 2438, 2441, 2444, 2447, 2450, 2453, 2456, 2459,
		 2462, 2465, 2468, 2471, 2474, 2477, 2480, 2483,
		 2486, 2489, 2492, 2495, 2498, 2501, 2504, 2506,
		 2509, 2512, 2515, 2518, 2521, 2524, 2527, 2530,
		 2532, 2535, 2538, 2541, 2544, 2546, 2549, 2552,
		 2555, 2558, 2560, 2563, 2566, 2569, 2572, 2574,
		 2577, 2580, 2582, 2585, 2588, 2591, 2593, 2596,
		 2599, 2601, 2604, 2607, 2609, 2612, 2615, 2617,
		 2620, 2623, 2625, 2628, 2630, 2633, 2636, 2638,
		 2641, 2643, 2646, 2648, 2651, 2654, 2656, 2659
	},
	{
		/* lsc - 1 */
		/* R */
		 1014, 1028, 1044, 1060, 1077, 1094, 1111, 1127,
		 1143, 1159, 1176, 1194, 1213, 1231, 1248, 1265,
		 1283, 1302, 1320, 1338, 1356, 1375, 1392, 1410,
		 1428, 1446, 1464, 1483, 1502, 1522, 1542, 1563,
		 1583, 1603, 1624, 1643, 1663, 1683, 1703, 1721,
		 1740, 1758, 1777, 1794, 1813, 1832, 1850, 1870,
		 1890, 1912, 1931, 1949, 1968, 1986, 2004, 2023,
		 2042, 2061, 2081, 2101, 2121, 2142, 2163, 2182,
		 2202, 2221, 2240, 2258, 2277, 2297, 2315, 2333,
		 2351, 2370, 2388, 2405, 2423, 2443, 2462, 2482,
		 2498, 2516, 2536, 2557, 2575, 2594, 2612, 2628,
		 2645, 2663, 2680, 2694, 2708, 2725, 2740, 2754,
		 2768, 2784, 2801, 2816, 2830, 2845, 2861, 2878,
		 2895, 2913, 2932, 2948, 2965, 2981, 2998, 3014,
		 3032, 3051, 3071, 3088, 3104, 3120, 3138, 3154,
		 3168, 3186, 3204, 3222, 3236, 3254, 3270, 3291,
		 3312, 3334, 3351, 3371, 3392, 3416, 3435, 3458,
		 3482, 3508, 3523, 3540, 3554, 3570, 3587, 3606,
		 3625, 3643, 3666, 3680, 3691, 3700, 3708, 3718,
		 3726, 3742, 3746, 3770, 3784, 3802, 3792, 3769,
		 3775, 3781, 3787, 3793, 3799, 3805, 3811, 3817,
		 3823, 3828, 3834, 3840, 3846, 3852, 3858, 3863,
		 3869, 3875, 3881, 3887, 3892, 3898, 3904, 3910,
		 3915, 3921, 3927, 3932, 3938, 3944, 3949, 3955,
		 3960, 3966, 3972, 3977, 3983, 3988, 3994, 3999,
		 4005, 4010, 4016, 4021, 4027, 4032, 4038, 4043,
		 4048, 4054, 4059, 4065, 4070, 4075, 4081, 4086,
		 4091, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		/* G */
		 1017, 1026, 1035, 1045, 1056, 1067, 1079, 1091,
		 1103, 1113, 1124, 1135, 1147, 1158, 1170, 1181,
		 1193, 1203, 1214, 1224, 1235, 1246, 1256, 1267,
		 1278, 1289, 1299, 1308, 1318, 1329, 1339, 1349,
		 1359, 1369, 1380, 1390, 1400, 1409, 1418, 1427,
		 1438, 1448, 1459, 1469, 1478, 1488, 1497, 1507,
		 1518, 1530, 1541, 1554, 1567, 1580, 1593, 1605,
		 1618, 1630, 1640, 1650, 1661, 1671, 1682, 1692,
		 1702, 1712, 1721, 1731, 1740, 1750, 1759, 1769,
		 1779, 1790, 1800, 1811, 1821, 1831, 1842, 1852,
		 1862, 1871, 1881, 1891, 1900, 1909, 1918, 1927,
		 1936, 1946, 1956, 1965, 1975, 1986, 1997, 2008,
		 2019, 2030, 2042, 2055, 2067, 2081, 2092, 2100,
		 2107, 2115, 2125, 2134, 2144, 2153, 2163, 2173,
		 2183, 2192, 2203, 2214, 2224, 2234, 2244, 2255,
		 2264, 2279, 2290, 2302, 2312, 2327, 2340, 2352,
		 2365, 2377, 2392, 2407, 2420, 2433, 2445, 2457,
		 2471, 2486, 2499, 2511, 2523, 2534, 2545, 2558,
		 2574, 2590, 2605, 2619, 2634, 2644, 2654, 2672,
		 2692, 2710, 2726, 2748, 2769, 2784, 2797, 2826,
		 2833, 2839, 2846, 2853, 2859, 2866, 2872, 2879,
		 2885, 2892, 2898, 2905, 2911, 2918, 2924, 2931,
		 2937, 2944, 2950, 2957, 2963, 2969, 2976, 2982,
		 2988, 2995, 3001, 3007, 3014, 3020, 3026, 3033,
		 3039, 3045, 3051, 3058, 3064, 3070, 3076, 3082,
		 3088, 3095, 3101, 3107, 3113, 3119, 3125, 3131,
		 3137, 3143, 3149, 3155, 3162, 3168, 3174, 3179,
		 3185, 3191, 3197, 3203, 3209, 3215, 3221, 3227,
		 3233, 3239, 3245, 3250, 3256, 3262, 3268, 3274,
		 3279, 3285, 3291, 3297, 3302, 3308, 3314, 3320,
		 3325, 3331, 3337, 3342, 3348, 3353, 3359, 3365,
		 3370, 3376, 3381, 3387, 3392, 3398, 3404, 3409,
		/* B */
		 1013, 1029, 1039, 1048, 1057, 1068, 1081, 1095,
		 1108, 1119, 1130, 1140, 1149, 1158, 1170, 1184,
		 1197, 1209, 1218, 1226, 1233, 1240, 1248, 1255,
		 1261, 1269, 1279, 1291, 1304, 1315, 1324, 1331,
		 1337, 1343, 1350, 1356, 1363, 1370, 1381, 1393,
		 1404, 1414, 1423, 1430, 1437, 1442, 1448, 1453,
		 1459, 1465, 1470, 1476, 1482, 1489, 1496, 1504,
		 1514, 1524, 1536, 1548, 1560, 1571, 1580, 1589,
		 1598, 1608, 1617, 1625, 1633, 1642, 1651, 1659,
		 1665, 1674, 1684, 1694, 1702, 1711, 1718, 1728,
		 1736, 1747, 1759, 1771, 1780, 1791, 1804, 1815,
		 1825, 1836, 1846, 1855, 1862, 1869, 1876, 1883,
		 1889, 1895, 1902, 1910, 1918, 1927, 1936, 1942,
		 1947, 1955, 1965, 1973, 1980, 1990, 1998, 2008,
		 2016, 2025, 2035, 2046, 2055, 2065, 2076, 2086,
		 2094, 2110, 2125, 2138, 2150, 2167, 2182, 2196,
		 2212, 2229, 2249, 2266, 2283, 2298, 2310, 2319,
		 2332, 2345, 2357, 2364, 2371, 2375, 2387, 2395,
		 2407, 2421, 2443, 2450, 2456, 2451, 2448, 2456,
		 2466, 2481, 2497, 2514, 2542, 2528, 2518, 2616,
		 2623, 2630, 2637, 2644, 2650, 2657, 2664, 2670,
		 2677, 2684, 2690, 2697, 2704, 2710, 2717, 2724,
		 2730, 2737, 2743, 2750, 2756, 2763, 2770, 2776,
		 2783, 2789, 2795, 2802, 2808, 2815, 2821, 2828,
		 2834, 2840, 2847, 2853, 2860, 2866, 2872, 2879,
		 2885, 2891, 2897, 2904, 2910, 2916, 2922, 2929,
		 2935, 2941, 2947, 2953, 2960, 2966, 2972, 2978,
		 2984, 2990, 2996, 3002, 3009, 3015, 3021, 3027,
		 3033, 3039, 3045, 3051, 3057, 3063, 3069, 3075,
		 3080, 3086, 3092, 3098, 3104, 3110, 3116, 3122,
		 3127, 3133, 3139, 3145, 3151, 3156, 3162, 3168,
		 3174, 3179, 3185, 3191, 3196, 3202, 3208, 3213
	},
	{
		/* lsc - 2 */
		/* R */
		 1017, 1027, 1036, 1045, 1055, 1065, 1076, 1087,
		 1097, 1107, 1117, 1128, 1139, 1150, 1163, 1174,
		 1185, 1195, 1205, 1216, 1227, 1238, 1249, 1261,
		 1273, 1284, 1295, 1305, 1317, 1329, 1341, 1353,
		 1366, 1380, 1392, 1405, 1419, 1433, 1449, 1464,
		 1479, 1494, 1509, 1523, 1538, 1552, 1566, 1578,
		 1591, 1605, 1618, 1632, 1645, 1658, 1673, 1688,
		 1703, 1718, 1733, 1748, 1763, 1778, 1793, 1809,
		 1824, 1839, 1855, 1870, 1885, 1900, 1916, 1933,
		 1950, 1965, 1981, 1996, 2010, 2025, 2039, 2054,
		 2068, 2083, 2097, 2112, 2128, 2142, 2156, 2170,
		 2185, 2202, 2218, 2231, 2245, 2259, 2273, 2285,
		 2298, 2311, 2323, 2334, 2348, 2362, 2376, 2389,
		 2403, 2416, 2430, 2446, 2461, 2474, 2489, 2504,
		 2519, 2534, 2550, 2566, 2582, 2597, 2612, 2627,
		 2643, 2660, 2674, 2689, 2705, 2721, 2736, 2750,
		 2765, 2778, 2792, 2806, 2825, 2838, 2851, 2863,
		 2878, 2891, 2903, 2917, 2929, 2937, 2948, 2965,
		 2982, 2996, 3007, 3016, 3031, 3042, 3057, 3060,
		 3064, 3067, 3078, 3084, 3096, 3105, 3116, 3160,
		 3170, 3181, 3191, 3201, 3212, 3222, 3232, 3243,
		 3253, 3263, 3273, 3284, 3294, 3304, 3314, 3324,
		 3335, 3345, 3355, 3365, 3375, 3385, 3395, 3405,
		 3415, 3425, 3435, 3445, 3455, 3465, 3475, 3485,
		 3494, 3504, 3514, 3524, 3534, 3543, 3553, 3563,
		 3573, 3582, 3592, 3602, 3611, 3621, 3631, 3640,
		 3650, 3659, 3669, 3678, 3688, 3697, 3707, 3716,
		 3726, 3735, 3745, 3754, 3763, 3773, 3782, 3791,
		 3801, 3810, 3819, 3828, 3838, 3847, 3856, 3865,
		 3874, 3884, 3893, 3902, 3911, 3920, 3929, 3938,
		 3947, 3956, 3965, 3974, 3983, 3992, 4001, 4010,
		 4019, 4027, 4036, 4045, 4054, 4063, 4071, 4080,
		/* G */
		 1017, 1027, 1036, 1046, 1056, 1066, 1076, 1086,
		 1096, 1106, 1118, 1128, 1138, 1148, 1159, 1169,
		 1180, 1191, 1201, 1211, 1222, 1232, 1242, 1252,
		 1262, 1272, 1282, 1292, 1302, 1312, 1322, 1332,
		 1342, 1352, 1362, 1372, 1383, 1393, 1402, 1412,
		 1422, 1432, 1442, 1452, 1462, 1471, 1481, 1491,
		 1502, 1512, 1521, 1531, 1542, 1553, 1564, 1575,
		 1587, 1598, 1607, 1615, 1624, 1634, 1644, 1654,
		 1665, 1674, 1684, 1694, 1704, 1714, 1725, 1736,
		 1747, 1757, 1767, 1778, 1789, 1800, 1811, 1823,
		 1834, 1846, 1857, 1868, 1879, 1890, 1900, 1910,
		 1920, 1931, 1943, 1953, 1964, 1975, 1985, 1996,
		 2007, 2020, 2032, 2044, 2057, 2071, 2082, 2091,
		 2097, 2106, 2114, 2125, 2135, 2144, 2153, 2164,
		 2175, 2185, 2195, 2206, 2215, 2227, 2239, 2250,
		 2260, 2273, 2286, 2298, 2308, 2321, 2334, 2348,
		 2361, 2373, 2385, 2401, 2416, 2429, 2444, 2457,
		 2470, 2482, 2497, 2510, 2523, 2534, 2547, 2561,
		 2573, 2583, 2598, 2615, 2630, 2643, 2657, 2667,
		 2680, 2700, 2718, 2731, 2742, 2764, 2787, 2810,
		 2820, 2831, 2842, 2852, 2863, 2873, 2884, 2894,
		 2905, 2915, 2926, 2936, 2947, 2957, 2968, 2978,
		 2988, 2999, 3009, 3020, 3030, 3040, 3050, 3061,
		 3071, 3081, 3091, 3102, 3112, 3122, 3132, 3142,
		 3152, 3162, 3172, 3182, 3192, 3202, 3212, 3222,
		 3232, 3242, 3252, 3262, 3272, 3282, 3292, 3302,
		 3311, 3321, 3331, 3341, 3351, 3360, 3370, 3380,
		 3389, 3399, 3409, 3418, 3428, 3438, 3447, 3457,
		 3466, 3476, 3485, 3495, 3504, 3514, 3523, 3533,
		 3542, 3551, 3561, 3570, 3579, 3589, 3598, 3607,
		 3617, 3626, 3635, 3644, 3653, 3663, 3672, 3681,
		 3690, 3699, 3708, 3717, 3726, 3735, 3744, 3753,
		/* B */
		 1013, 1027, 1039, 1049, 1058, 1069, 1080, 1092,
		 1103, 1114, 1123, 1132, 1141, 1150, 1160, 1170,
		 1179, 1187, 1196, 1207, 1218, 1230, 1240, 1251,
		 1261, 1271, 1280, 1288, 1296, 1305, 1313, 1321,
		 1330, 1340, 1350, 1361, 1371, 1382, 1392, 1400,
		 1408, 1416, 1424, 1431, 1437, 1445, 1453, 1461,
		 1468, 1477, 1486, 1494, 1501, 1510, 1521, 1532,
		 1542, 1551, 1561, 1570, 1579, 1587, 1596, 1604,
		 1613, 1621, 1629, 1637, 1646, 1653, 1661, 1668,
		 1677, 1687, 1697, 1705, 1714, 1724, 1736, 1748,
		 1759, 1769, 1781, 1790, 1799, 1808, 1818, 1826,
		 1834, 1844, 1854, 1863, 1871, 1878, 1884, 1890,
		 1898, 1906, 1912, 1920, 1929, 1938, 1948, 1957,
		 1966, 1973, 1983, 1994, 2004, 2013, 2023, 2034,
		 2049, 2061, 2072, 2082, 2096, 2109, 2121, 2134,
		 2149, 2164, 2178, 2195, 2210, 2224, 2238, 2253,
		 2268, 2281, 2297, 2312, 2330, 2340, 2352, 2364,
		 2379, 2387, 2394, 2408, 2429, 2439, 2440, 2445,
		 2458, 2466, 2473, 2478, 2480, 2483, 2501, 2513,
		 2525, 2532, 2543, 2555, 2564, 2561, 2562, 2612,
		 2623, 2634, 2644, 2655, 2666, 2676, 2687, 2698,
		 2709, 2719, 2730, 2740, 2751, 2762, 2772, 2783,
		 2793, 2804, 2814, 2825, 2835, 2846, 2856, 2866,
		 2877, 2887, 2897, 2908, 2918, 2928, 2939, 2949,
		 2959, 2969, 2980, 2990, 3000, 3010, 3020, 3030,
		 3040, 3050, 3061, 3071, 3081, 3091, 3101, 3111,
		 3121, 3131, 3140, 3150, 3160, 3170, 3180, 3190,
		 3200, 3209, 3219, 3229, 3239, 3248, 3258, 3268,
		 3278, 3287, 3297, 3306, 3316, 3326, 3335, 3345,
		 3354, 3364, 3373, 3383, 3392, 3402, 3411, 3421,
		 3430, 3439, 3449, 3458, 3467, 3477, 3486, 3495,
		 3505, 3514, 3523, 3532, 3541, 3551, 3560, 3569
	},
	{
		/* lsc - 3 */
		/* R */
		 1012, 1029, 1045, 1061, 1076, 1091, 1107, 1120,
		 1133, 1144, 1159, 1174, 1189, 1202, 1218, 1233,
		 1249, 1265, 1279, 1293, 1309, 1324, 1339, 1353,
		 1367, 1382, 1397, 1412, 1426, 1442, 1458, 1473,
		 1488, 1503, 1519, 1535, 1552, 1568, 1584, 1600,
		 1614, 1628, 1641, 1655, 1667, 1680, 1693, 1707,
		 1720, 1734, 1748, 1761, 1773, 1785, 1799, 1813,
		 1826, 1842, 1858, 1874, 1889, 1906, 1923, 1939,
		 1955, 1970, 1987, 2004, 2021, 2035, 2049, 2066,
		 2086, 2106, 2124, 2140, 2158, 2175, 2192, 2208,
		 2223, 2239, 2255, 2273, 2289, 2306, 2319, 2333,
		 2348, 2364, 2378, 2393, 2409, 2424, 2437, 2451,
		 2463, 2474, 2484, 2499, 2512, 2523, 2539, 2556,
		 2570, 2582, 2599, 2617, 2634, 2646, 2660, 2672,
		 2683, 2695, 2714, 2731, 2747, 2760, 2775, 2791,
		 2810, 2825, 2841, 2858, 2870, 2884, 2900, 2921,
		 2933, 2952, 2973, 2996, 3011, 3031, 3049, 3063,
		 3083, 3109, 3132, 3144, 3161, 3173, 3180, 3187,
		 3204, 3220, 3246, 3251, 3255, 3264, 3296, 3315,
		 3319, 3328, 3333, 3342, 3358, 3405, 3434, 3404,
		 3417, 3429, 3442, 3454, 3467, 3479, 3491, 3504,
		 3516, 3528, 3540, 3553, 3565, 3577, 3589, 3601,
		 3613, 3626, 3638, 3650, 3662, 3674, 3686, 3698,
		 3710, 3722, 3734, 3745, 3757, 3769, 3781, 3793,
		 3805, 3816, 3828, 3840, 3852, 3863, 3875, 3887,
		 3898, 3910, 3921, 3933, 3945, 3956, 3968, 3979,
		 3991, 4002, 4013, 4025, 4036, 4048, 4059, 4070,
		 4082, 4093, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		/* G */
		 1016, 1027, 1038, 1049, 1060, 1070, 1081, 1092,
		 1103, 1112, 1123, 1134, 1144, 1154, 1165, 1176,
		 1187, 1197, 1208, 1218, 1229, 1240, 1250, 1261,
		 1272, 1283, 1293, 1303, 1314, 1325, 1335, 1345,
		 1355, 1365, 1374, 1384, 1393, 1403, 1412, 1421,
		 1431, 1440, 1449, 1458, 1468, 1477, 1486, 1496,
		 1506, 1516, 1526, 1537, 1548, 1559, 1571, 1583,
		 1597, 1609, 1619, 1628, 1638, 1648, 1658, 1668,
		 1679, 1688, 1698, 1708, 1718, 1727, 1736, 1746,
		 1757, 1767, 1776, 1786, 1796, 1806, 1814, 1823,
		 1833, 1843, 1852, 1862, 1872, 1881, 1891, 1901,
		 1911, 1921, 1932, 1942, 1953, 1963, 1973, 1984,
		 1994, 2004, 2015, 2027, 2038, 2050, 2060, 2070,
		 2075, 2083, 2093, 2102, 2111, 2120, 2129, 2139,
		 2149, 2159, 2169, 2179, 2190, 2202, 2213, 2225,
		 2237, 2250, 2261, 2273, 2285, 2296, 2307, 2319,
		 2330, 2343, 2356, 2370, 2381, 2394, 2406, 2417,
		 2430, 2446, 2460, 2472, 2486, 2500, 2509, 2520,
		 2535, 2551, 2567, 2583, 2598, 2613, 2626, 2640,
		 2655, 2672, 2692, 2709, 2724, 2736, 2758, 2792,
		 2804, 2817, 2830, 2843, 2856, 2869, 2881, 2894,
		 2907, 2919, 2932, 2945, 2957, 2970, 2983, 2995,
		 3008, 3020, 3033, 3045, 3058, 3070, 3083, 3095,
		 3107, 3120, 3132, 3144, 3157, 3169, 3181, 3194,
		 3206, 3218, 3230, 3242, 3254, 3267, 3279, 3291,
		 3303, 3315, 3327, 3339, 3351, 3363, 3375, 3387,
		 3399, 3410, 3422, 3434, 3446, 3458, 3469, 3481,
		 3493, 3505, 3516, 3528, 3540, 3551, 3563, 3574,
		 3586, 3598, 3609, 3621, 3632, 3644, 3655, 3666,
		 3678, 3689, 3701, 3712, 3723, 3734, 3746, 3757,
		 3768, 3779, 3791, 3802, 3813, 3824, 3835, 3846,
		 3857, 3868, 3879, 3890, 3901, 3912, 3923, 3934,
		/* B */
		 1015, 1028, 1039, 1049, 1059, 1069, 1081, 1092,
		 1102, 1112, 1123, 1134, 1144, 1154, 1166, 1178,
		 1191, 1204, 1216, 1228, 1240, 1252, 1262, 1271,
		 1282, 1293, 1302, 1311, 1320, 1330, 1339, 1348,
		 1357, 1366, 1374, 1383, 1392, 1401, 1409, 1418,
		 1426, 1434, 1443, 1451, 1461, 1468, 1477, 1485,
		 1495, 1504, 1513, 1522, 1532, 1541, 1551, 1561,
		 1570, 1580, 1590, 1601, 1610, 1620, 1629, 1640,
		 1649, 1658, 1665, 1676, 1685, 1694, 1703, 1712,
		 1721, 1729, 1738, 1747, 1757, 1766, 1774, 1783,
		 1791, 1799, 1808, 1819, 1828, 1837, 1847, 1856,
		 1864, 1872, 1881, 1889, 1898, 1907, 1916, 1923,
		 1930, 1937, 1946, 1953, 1962, 1971, 1981, 1991,
		 1999, 2007, 2015, 2026, 2035, 2044, 2052, 2062,
		 2070, 2078, 2087, 2096, 2107, 2120, 2132, 2143,
		 2154, 2164, 2170, 2182, 2191, 2204, 2215, 2230,
		 2238, 2250, 2263, 2275, 2283, 2296, 2309, 2321,
		 2332, 2343, 2354, 2363, 2373, 2381, 2393, 2403,
		 2415, 2421, 2433, 2444, 2457, 2459, 2469, 2481,
		 2492, 2502, 2519, 2533, 2550, 2565, 2585, 2614,
		 2627, 2640, 2653, 2666, 2679, 2692, 2705, 2718,
		 2730, 2743, 2756, 2769, 2781, 2794, 2807, 2820,
		 2832, 2845, 2858, 2870, 2883, 2895, 2908, 2920,
		 2933, 2945, 2958, 2970, 2983, 2995, 3008, 3020,
		 3032, 3045, 3057, 3069, 3081, 3094, 3106, 3118,
		 3130, 3142, 3155, 3167, 3179, 3191, 3203, 3215,
		 3227, 3239, 3251, 3263, 3275, 3287, 3299, 3311,
		 3322, 3334, 3346, 3358, 3370, 3381, 3393, 3405,
		 3416, 3428, 3440, 3451, 3463, 3475, 3486, 3498,
		 3509, 3521, 3532, 3544, 3555, 3567, 3578, 3589,
		 3601, 3612, 3623, 3635, 3646, 3657, 3668, 3680,
		 3691, 3702, 3713, 3724, 3735, 3746, 3757, 3769
	},
	{
		/* lsc - 4 */
		/* R */
		 1007, 1030, 1049, 1067, 1085, 1104, 1122, 1140,
		 1157, 1174, 1192, 1212, 1231, 1249, 1267, 1284,
		 1302, 1321, 1339, 1358, 1377, 1396, 1416, 1436,
		 1457, 1478, 1500, 1520, 1541, 1561, 1581, 1601,
		 1621, 1641, 1662, 1681, 1701, 1721, 1740, 1759,
		 1780, 1799, 1817, 1835, 1855, 1874, 1890, 1907,
		 1926, 1946, 1965, 1984, 2005, 2024, 2044, 2063,
		 2083, 2103, 2124, 2143, 2162, 2182, 2203, 2223,
		 2243, 2261, 2281, 2300, 2319, 2337, 2357, 2375,
		 2395, 2413, 2432, 2449, 2468, 2486, 2505, 2521,
		 2539, 2558, 2576, 2592, 2611, 2627, 2644, 2659,
		 2677, 2692, 2709, 2724, 2740, 2755, 2771, 2787,
		 2803, 2820, 2835, 2848, 2863, 2882, 2900, 2913,
		 2927, 2942, 2961, 2978, 2994, 3008, 3028, 3045,
		 3060, 3071, 3089, 3104, 3126, 3140, 3158, 3174,
		 3202, 3220, 3235, 3251, 3273, 3294, 3313, 3336,
		 3354, 3371, 3390, 3414, 3433, 3454, 3474, 3493,
		 3508, 3532, 3549, 3559, 3570, 3586, 3599, 3613,
		 3629, 3642, 3653, 3678, 3685, 3703, 3704, 3718,
		 3733, 3751, 3766, 3784, 3790, 3775, 3755, 3730,
		 3732, 3735, 3737, 3739, 3741, 3743, 3746, 3748,
		 3750, 3752, 3754, 3757, 3759, 3761, 3763, 3765,
		 3767, 3769, 3771, 3774, 3776, 3778, 3780, 3782,
		 3784, 3786, 3788, 3790, 3792, 3794, 3796, 3798,
		 3800, 3802, 3804, 3806, 3808, 3810, 3812, 3814,
		 3816, 3818, 3820, 3822, 3824, 3826, 3828, 3830,
		 3832, 3834, 3836, 3838, 3839, 3841, 3843, 3845,
		 3847, 3849, 3851, 3852, 3854, 3856, 3858, 3860,
		 3862, 3863, 3865, 3867, 3869, 3870, 3872, 3874,
		 3876, 3878, 3879, 3881, 3883, 3884, 3886, 3888,
		 3890, 3891, 3893, 3895, 3896, 3898, 3900, 3901,
		 3903, 3905, 3906, 3908, 3909, 3911, 3913, 3914,
		/* G */
		 1015, 1029, 1042, 1055, 1067, 1080, 1091, 1104,
		 1116, 1127, 1138, 1151, 1162, 1173, 1184, 1194,
		 1206, 1217, 1229, 1239, 1251, 1262, 1274, 1285,
		 1298, 1310, 1322, 1333, 1345, 1357, 1369, 1380,
		 1391, 1401, 1412, 1422, 1433, 1444, 1454, 1463,
		 1474, 1484, 1494, 1502, 1512, 1522, 1532, 1541,
		 1550, 1560, 1571, 1581, 1591, 1601, 1612, 1623,
		 1635, 1646, 1655, 1664, 1672, 1682, 1691, 1700,
		 1710, 1720, 1731, 1740, 1750, 1760, 1770, 1778,
		 1788, 1798, 1809, 1819, 1829, 1839, 1848, 1856,
		 1866, 1875, 1886, 1896, 1905, 1913, 1923, 1931,
		 1940, 1950, 1961, 1971, 1981, 1992, 2002, 2013,
		 2024, 2037, 2047, 2058, 2071, 2087, 2097, 2103,
		 2109, 2115, 2124, 2133, 2142, 2148, 2158, 2168,
		 2179, 2187, 2198, 2208, 2220, 2231, 2243, 2254,
		 2269, 2279, 2290, 2301, 2316, 2327, 2337, 2350,
		 2362, 2372, 2384, 2401, 2415, 2426, 2438, 2450,
		 2462, 2478, 2491, 2500, 2511, 2524, 2539, 2552,
		 2569, 2579, 2595, 2615, 2630, 2641, 2656, 2674,
		 2684, 2700, 2719, 2733, 2749, 2766, 2798, 2846,
		 2849, 2852, 2855, 2858, 2860, 2863, 2866, 2869,
		 2872, 2874, 2877, 2880, 2883, 2886, 2888, 2891,
		 2894, 2897, 2899, 2902, 2905, 2907, 2910, 2913,
		 2915, 2918, 2921, 2924, 2926, 2929, 2931, 2934,
		 2937, 2939, 2942, 2945, 2947, 2950, 2952, 2955,
		 2958, 2960, 2963, 2965, 2968, 2970, 2973, 2975,
		 2978, 2981, 2983, 2986, 2988, 2991, 2993, 2996,
		 2998, 3000, 3003, 3005, 3008, 3010, 3013, 3015,
		 3018, 3020, 3022, 3025, 3027, 3030, 3032, 3034,
		 3037, 3039, 3041, 3044, 3046, 3048, 3051, 3053,
		 3055, 3058, 3060, 3062, 3065, 3067, 3069, 3071,
		 3074, 3076, 3078, 3080, 3083, 3085, 3087, 3089,
		/* B */
		 1008, 1031, 1048, 1063, 1078, 1093, 1107, 1120,
		 1130, 1140, 1152, 1166, 1179, 1187, 1195, 1202,
		 1212, 1223, 1235, 1246, 1255, 1265, 1275, 1283,
		 1292, 1302, 1312, 1320, 1328, 1335, 1343, 1349,
		 1358, 1368, 1378, 1386, 1394, 1401, 1406, 1411,
		 1417, 1421, 1424, 1428, 1435, 1442, 1448, 1455,
		 1464, 1473, 1479, 1483, 1487, 1492, 1498, 1502,
		 1507, 1515, 1524, 1533, 1542, 1552, 1561, 1568,
		 1576, 1584, 1590, 1595, 1601, 1606, 1612, 1616,
		 1624, 1630, 1636, 1641, 1648, 1652, 1656, 1663,
		 1673, 1683, 1688, 1694, 1704, 1714, 1724, 1732,
		 1740, 1745, 1754, 1762, 1772, 1779, 1785, 1790,
		 1796, 1802, 1806, 1811, 1818, 1828, 1837, 1842,
		 1846, 1852, 1861, 1868, 1875, 1879, 1887, 1896,
		 1908, 1917, 1928, 1935, 1947, 1957, 1969, 1980,
		 1998, 2012, 2022, 2029, 2044, 2057, 2066, 2072,
		 2081, 2093, 2104, 2118, 2129, 2141, 2146, 2152,
		 2160, 2180, 2191, 2197, 2199, 2206, 2212, 2221,
		 2233, 2242, 2245, 2253, 2258, 2272, 2280, 2289,
		 2290, 2291, 2306, 2319, 2335, 2341, 2365, 2385,
		 2388, 2391, 2394, 2398, 2401, 2404, 2407, 2410,
		 2413, 2416, 2419, 2423, 2426, 2429, 2432, 2435,
		 2438, 2441, 2444, 2447, 2450, 2453, 2456, 2459,
		 2462, 2465, 2468, 2471, 2474, 2477, 2480, 2483,
		 2486, 2489, 2492, 2495, 2498, 2501, 2504, 2506,
		 2509, 2512, 2515, 2518, 2521, 2524, 2527, 2530,
		 2532, 2535, 2538, 2541, 2544, 2546, 2549, 2552,
		 2555, 2558, 2560, 2563, 2566, 2569, 2572, 2574,
		 2577, 2580, 2582, 2585, 2588, 2591, 2593, 2596,
		 2599, 2601, 2604, 2607, 2609, 2612, 2615, 2617,
		 2620, 2623, 2625, 2628, 2630, 2633, 2636, 2638,
		 2641, 2643, 2646, 2648, 2651, 2654, 2656, 2659
	},
	{
		/* lsc - 5 */
		/* R */
		 1014, 1028, 1044, 1060, 1077, 1094, 1111, 1127,
		 1143, 1159, 1176, 1194, 1213, 1231, 1248, 1265,
		 1283, 1302, 1320, 1338, 1356, 1375, 1392, 1410,
		 1428, 1446, 1464, 1483, 1502, 1522, 1542, 1563,
		 1583, 1603, 1624, 1643, 1663, 1683, 1703, 1721,
		 1740, 1758, 1777, 1794, 1813, 1832, 1850, 1870,
		 1890, 1912, 1931, 1949, 1968, 1986, 2004, 2023,
		 2042, 2061, 2081, 2101, 2121, 2142, 2163, 2182,
		 2202, 2221, 2240, 2258, 2277, 2297, 2315, 2333,
		 2351, 2370, 2388, 2405, 2423, 2443, 2462, 2482,
		 2498, 2516, 2536, 2557, 2575, 2594, 2612, 2628,
		 2645, 2663, 2680, 2694, 2708, 2725, 2740, 2754,
		 2768, 2784, 2801, 2816, 2830, 2845, 2861, 2878,
		 2895, 2913, 2932, 2948, 2965, 2981, 2998, 3014,
		 3032, 3051, 3071, 3088, 3104, 3120, 3138, 3154,
		 3168, 3186, 3204, 3222, 3236, 3254, 3270, 3291,
		 3312, 3334, 3351, 3371, 3392, 3416, 3435, 3458,
		 3482, 3508, 3523, 3540, 3554, 3570, 3587, 3606,
		 3625, 3643, 3666, 3680, 3691, 3700, 3708, 3718,
		 3726, 3742, 3746, 3770, 3784, 3802, 3792, 3769,
		 3775, 3781, 3787, 3793, 3799, 3805, 3811, 3817,
		 3823, 3828, 3834, 3840, 3846, 3852, 3858, 3863,
		 3869, 3875, 3881, 3887, 3892, 3898, 3904, 3910,
		 3915, 3921, 3927, 3932, 3938, 3944, 3949, 3955,
		 3960, 3966, 3972, 3977, 3983, 3988, 3994, 3999,
		 4005, 4010, 4016, 4021, 4027, 4032, 4038, 4043,
		 4048, 4054, 4059, 4065, 4070, 4075, 4081, 4086,
		 4091, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		/* G */
		 1017, 1026, 1035, 1045, 1056, 1067, 1079, 1091,
		 1103, 1113, 1124, 1135, 1147, 1158, 1170, 1181,
		 1193, 1203, 1214, 1224, 1235, 1246, 1256, 1267,
		 1278, 1289, 1299, 1308, 1318, 1329, 1339, 1349,
		 1359, 1369, 1380, 1390, 1400, 1409, 1418, 1427,
		 1438, 1448, 1459, 1469, 1478, 1488, 1497, 1507,
		 1518, 1530, 1541, 1554, 1567, 1580, 1593, 1605,
		 1618, 1630, 1640, 1650, 1661, 1671, 1682, 1692,
		 1702, 1712, 1721, 1731, 1740, 1750, 1759, 1769,
		 1779, 1790, 1800, 1811, 1821, 1831, 1842, 1852,
		 1862, 1871, 1881, 1891, 1900, 1909, 1918, 1927,
		 1936, 1946, 1956, 1965, 1975, 1986, 1997, 2008,
		 2019, 2030, 2042, 2055, 2067, 2081, 2092, 2100,
		 2107, 2115, 2125, 2134, 2144, 2153, 2163, 2173,
		 2183, 2192, 2203, 2214, 2224, 2234, 2244, 2255,
		 2264, 2279, 2290, 2302, 2312, 2327, 2340, 2352,
		 2365, 2377, 2392, 2407, 2420, 2433, 2445, 2457,
		 2471, 2486, 2499, 2511, 2523, 2534, 2545, 2558,
		 2574, 2590, 2605, 2619, 2634, 2644, 2654, 2672,
		 2692, 2710, 2726, 2748, 2769, 2784, 2797, 2826,
		 2833, 2839, 2846, 2853, 2859, 2866, 2872, 2879,
		 2885, 2892, 2898, 2905, 2911, 2918, 2924, 2931,
		 2937, 2944, 2950, 2957, 2963, 2969, 2976, 2982,
		 2988, 2995, 3001, 3007, 3014, 3020, 3026, 3033,
		 3039, 3045, 3051, 3058, 3064, 3070, 3076, 3082,
		 3088, 3095, 3101, 3107, 3113, 3119, 3125, 3131,
		 3137, 3143, 3149, 3155, 3162, 3168, 3174, 3179,
		 3185, 3191, 3197, 3203, 3209, 3215, 3221, 3227,
		 3233, 3239, 3245, 3250, 3256, 3262, 3268, 3274,
		 3279, 3285, 3291, 3297, 3302, 3308, 3314, 3320,
		 3325, 3331, 3337, 3342, 3348, 3353, 3359, 3365,
		 3370, 3376, 3381, 3387, 3392, 3398, 3404, 3409,
		/* B */
		 1013, 1029, 1039, 1048, 1057, 1068, 1081, 1095,
		 1108, 1119, 1130, 1140, 1149, 1158, 1170, 1184,
		 1197, 1209, 1218, 1226, 1233, 1240, 1248, 1255,
		 1261, 1269, 1279, 1291, 1304, 1315, 1324, 1331,
		 1337, 1343, 1350, 1356, 1363, 1370, 1381, 1393,
		 1404, 1414, 1423, 1430, 1437, 1442, 1448, 1453,
		 1459, 1465, 1470, 1476, 1482, 1489, 1496, 1504,
		 1514, 1524, 1536, 1548, 1560, 1571, 1580, 1589,
		 1598, 1608, 1617, 1625, 1633, 1642, 1651, 1659,
		 1665, 1674, 1684, 1694, 1702, 1711, 1718, 1728,
		 1736, 1747, 1759, 1771, 1780, 1791, 1804, 1815,
		 1825, 1836, 1846, 1855, 1862, 1869, 1876, 1883,
		 1889, 1895, 1902, 1910, 1918, 1927, 1936, 1942,
		 1947, 1955, 1965, 1973, 1980, 1990, 1998, 2008,
		 2016, 2025, 2035, 2046, 2055, 2065, 2076, 2086,
		 2094, 2110, 2125, 2138, 2150, 2167, 2182, 2196,
		 2212, 2229, 2249, 2266, 2283, 2298, 2310, 2319,
		 2332, 2345, 2357, 2364, 2371, 2375, 2387, 2395,
		 2407, 2421, 2443, 2450, 2456, 2451, 2448, 2456,
		 2466, 2481, 2497, 2514, 2542, 2528, 2518, 2616,
		 2623, 2630, 2637, 2644, 2650, 2657, 2664, 2670,
		 2677, 2684, 2690, 2697, 2704, 2710, 2717, 2724,
		 2730, 2737, 2743, 2750, 2756, 2763, 2770, 2776,
		 2783, 2789, 2795, 2802, 2808, 2815, 2821, 2828,
		 2834, 2840, 2847, 2853, 2860, 2866, 2872, 2879,
		 2885, 2891, 2897, 2904, 2910, 2916, 2922, 2929,
		 2935, 2941, 2947, 2953, 2960, 2966, 2972, 2978,
		 2984, 2990, 2996, 3002, 3009, 3015, 3021, 3027,
		 3033, 3039, 3045, 3051, 3057, 3063, 3069, 3075,
		 3080, 3086, 3092, 3098, 3104, 3110, 3116, 3122,
		 3127, 3133, 3139, 3145, 3151, 3156, 3162, 3168,
		 3174, 3179, 3185, 3191, 3196, 3202, 3208, 3213
	},
	{
		/* lsc - 6 */
		/* R */
		 1017, 1027, 1036, 1045, 1055, 1065, 1076, 1087,
		 1097, 1107, 1117, 1128, 1139, 1150, 1163, 1174,
		 1185, 1195, 1205, 1216, 1227, 1238, 1249, 1261,
		 1273, 1284, 1295, 1305, 1317, 1329, 1341, 1353,
		 1366, 1380, 1392, 1405, 1419, 1433, 1449, 1464,
		 1479, 1494, 1509, 1523, 1538, 1552, 1566, 1578,
		 1591, 1605, 1618, 1632, 1645, 1658, 1673, 1688,
		 1703, 1718, 1733, 1748, 1763, 1778, 1793, 1809,
		 1824, 1839, 1855, 1870, 1885, 1900, 1916, 1933,
		 1950, 1965, 1981, 1996, 2010, 2025, 2039, 2054,
		 2068, 2083, 2097, 2112, 2128, 2142, 2156, 2170,
		 2185, 2202, 2218, 2231, 2245, 2259, 2273, 2285,
		 2298, 2311, 2323, 2334, 2348, 2362, 2376, 2389,
		 2403, 2416, 2430, 2446, 2461, 2474, 2489, 2504,
		 2519, 2534, 2550, 2566, 2582, 2597, 2612, 2627,
		 2643, 2660, 2674, 2689, 2705, 2721, 2736, 2750,
		 2765, 2778, 2792, 2806, 2825, 2838, 2851, 2863,
		 2878, 2891, 2903, 2917, 2929, 2937, 2948, 2965,
		 2982, 2996, 3007, 3016, 3031, 3042, 3057, 3060,
		 3064, 3067, 3078, 3084, 3096, 3105, 3116, 3160,
		 3170, 3181, 3191, 3201, 3212, 3222, 3232, 3243,
		 3253, 3263, 3273, 3284, 3294, 3304, 3314, 3324,
		 3335, 3345, 3355, 3365, 3375, 3385, 3395, 3405,
		 3415, 3425, 3435, 3445, 3455, 3465, 3475, 3485,
		 3494, 3504, 3514, 3524, 3534, 3543, 3553, 3563,
		 3573, 3582, 3592, 3602, 3611, 3621, 3631, 3640,
		 3650, 3659, 3669, 3678, 3688, 3697, 3707, 3716,
		 3726, 3735, 3745, 3754, 3763, 3773, 3782, 3791,
		 3801, 3810, 3819, 3828, 3838, 3847, 3856, 3865,
		 3874, 3884, 3893, 3902, 3911, 3920, 3929, 3938,
		 3947, 3956, 3965, 3974, 3983, 3992, 4001, 4010,
		 4019, 4027, 4036, 4045, 4054, 4063, 4071, 4080,
		/* G */
		 1017, 1027, 1036, 1046, 1056, 1066, 1076, 1086,
		 1096, 1106, 1118, 1128, 1138, 1148, 1159, 1169,
		 1180, 1191, 1201, 1211, 1222, 1232, 1242, 1252,
		 1262, 1272, 1282, 1292, 1302, 1312, 1322, 1332,
		 1342, 1352, 1362, 1372, 1383, 1393, 1402, 1412,
		 1422, 1432, 1442, 1452, 1462, 1471, 1481, 1491,
		 1502, 1512, 1521, 1531, 1542, 1553, 1564, 1575,
		 1587, 1598, 1607, 1615, 1624, 1634, 1644, 1654,
		 1665, 1674, 1684, 1694, 1704, 1714, 1725, 1736,
		 1747, 1757, 1767, 1778, 1789, 1800, 1811, 1823,
		 1834, 1846, 1857, 1868, 1879, 1890, 1900, 1910,
		 1920, 1931, 1943, 1953, 1964, 1975, 1985, 1996,
		 2007, 2020, 2032, 2044, 2057, 2071, 2082, 2091,
		 2097, 2106, 2114, 2125, 2135, 2144, 2153, 2164,
		 2175, 2185, 2195, 2206, 2215, 2227, 2239, 2250,
		 2260, 2273, 2286, 2298, 2308, 2321, 2334, 2348,
		 2361, 2373, 2385, 2401, 2416, 2429, 2444, 2457,
		 2470, 2482, 2497, 2510, 2523, 2534, 2547, 2561,
		 2573, 2583, 2598, 2615, 2630, 2643, 2657, 2667,
		 2680, 2700, 2718, 2731, 2742, 2764, 2787, 2810,
		 2820, 2831, 2842, 2852, 2863, 2873, 2884, 2894,
		 2905, 2915, 2926, 2936, 2947, 2957, 2968, 2978,
		 2988, 2999, 3009, 3020, 3030, 3040, 3050, 3061,
		 3071, 3081, 3091, 3102, 3112, 3122, 3132, 3142,
		 3152, 3162, 3172, 3182, 3192, 3202, 3212, 3222,
		 3232, 3242, 3252, 3262, 3272, 3282, 3292, 3302,
		 3311, 3321, 3331, 3341, 3351, 3360, 3370, 3380,
		 3389, 3399, 3409, 3418, 3428, 3438, 3447, 3457,
		 3466, 3476, 3485, 3495, 3504, 3514, 3523, 3533,
		 3542, 3551, 3561, 3570, 3579, 3589, 3598, 3607,
		 3617, 3626, 3635, 3644, 3653, 3663, 3672, 3681,
		 3690, 3699, 3708, 3717, 3726, 3735, 3744, 3753,
		/* B */
		 1013, 1027, 1039, 1049, 1058, 1069, 1080, 1092,
		 1103, 1114, 1123, 1132, 1141, 1150, 1160, 1170,
		 1179, 1187, 1196, 1207, 1218, 1230, 1240, 1251,
		 1261, 1271, 1280, 1288, 1296, 1305, 1313, 1321,
		 1330, 1340, 1350, 1361, 1371, 1382, 1392, 1400,
		 1408, 1416, 1424, 1431, 1437, 1445, 1453, 1461,
		 1468, 1477, 1486, 1494, 1501, 1510, 1521, 1532,
		 1542, 1551, 1561, 1570, 1579, 1587, 1596, 1604,
		 1613, 1621, 1629, 1637, 1646, 1653, 1661, 1668,
		 1677, 1687, 1697, 1705, 1714, 1724, 1736, 1748,
		 1759, 1769, 1781, 1790, 1799, 1808, 1818, 1826,
		 1834, 1844, 1854, 1863, 1871, 1878, 1884, 1890,
		 1898, 1906, 1912, 1920, 1929, 1938, 1948, 1957,
		 1966, 1973, 1983, 1994, 2004, 2013, 2023, 2034,
		 2049, 2061, 2072, 2082, 2096, 2109, 2121, 2134,
		 2149, 2164, 2178, 2195, 2210, 2224, 2238, 2253,
		 2268, 2281, 2297, 2312, 2330, 2340, 2352, 2364,
		 2379, 2387, 2394, 2408, 2429, 2439, 2440, 2445,
		 2458, 2466, 2473, 2478, 2480, 2483, 2501, 2513,
		 2525, 2532, 2543, 2555, 2564, 2561, 2562, 2612,
		 2623, 2634, 2644, 2655, 2666, 2676, 2687, 2698,
		 2709, 2719, 2730, 2740, 2751, 2762, 2772, 2783,
		 2793, 2804, 2814, 2825, 2835, 2846, 2856, 2866,
		 2877, 2887, 2897, 2908, 2918, 2928, 2939, 2949,
		 2959, 2969, 2980, 2990, 3000, 3010, 3020, 3030,
		 3040, 3050, 3061, 3071, 3081, 3091, 3101, 3111,
		 3121, 3131, 3140, 3150, 3160, 3170, 3180, 3190,
		 3200, 3209, 3219, 3229, 3239, 3248, 3258, 3268,
		 3278, 3287, 3297, 3306, 3316, 3326, 3335, 3345,
		 3354, 3364, 3373, 3383, 3392, 3402, 3411, 3421,
		 3430, 3439, 3449, 3458, 3467, 3477, 3486, 3495,
		 3505, 3514, 3523, 3532, 3541, 3551, 3560, 3569
	},
	{
		/* lsc - 7 */
		/* R */
		 1012, 1029, 1045, 1061, 1076, 1091, 1107, 1120,
		 1133, 1144, 1159, 1174, 1189, 1202, 1218, 1233,
		 1249, 1265, 1279, 1293, 1309, 1324, 1339, 1353,
		 1367, 1382, 1397, 1412, 1426, 1442, 1458, 1473,
		 1488, 1503, 1519, 1535, 1552, 1568, 1584, 1600,
		 1614, 1628, 1641, 1655, 1667, 1680, 1693, 1707,
		 1720, 1734, 1748, 1761, 1773, 1785, 1799, 1813,
		 1826, 1842, 1858, 1874, 1889, 1906, 1923, 1939,
		 1955, 1970, 1987, 2004, 2021, 2035, 2049, 2066,
		 2086, 2106, 2124, 2140, 2158, 2175, 2192, 2208,
		 2223, 2239, 2255, 2273, 2289, 2306, 2319, 2333,
		 2348, 2364, 2378, 2393, 2409, 2424, 2437, 2451,
		 2463, 2474, 2484, 2499, 2512, 2523, 2539, 2556,
		 2570, 2582, 2599, 2617, 2634, 2646, 2660, 2672,
		 2683, 2695, 2714, 2731, 2747, 2760, 2775, 2791,
		 2810, 2825, 2841, 2858, 2870, 2884, 2900, 2921,
		 2933, 2952, 2973, 2996, 3011, 3031, 3049, 3063,
		 3083, 3109, 3132, 3144, 3161, 3173, 3180, 3187,
		 3204, 3220, 3246, 3251, 3255, 3264, 3296, 3315,
		 3319, 3328, 3333, 3342, 3358, 3405, 3434, 3404,
		 3417, 3429, 3442, 3454, 3467, 3479, 3491, 3504,
		 3516, 3528, 3540, 3553, 3565, 3577, 3589, 3601,
		 3613, 3626, 3638, 3650, 3662, 3674, 3686, 3698,
		 3710, 3722, 3734, 3745, 3757, 3769, 3781, 3793,
		 3805, 3816, 3828, 3840, 3852, 3863, 3875, 3887,
		 3898, 3910, 3921, 3933, 3945, 3956, 3968, 3979,
		 3991, 4002, 4013, 4025, 4036, 4048, 4059, 4070,
		 4082, 4093, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
		/* G */
		 1016, 1027, 1038, 1049, 1060, 1070, 1081, 1092,
		 1103, 1112, 1123, 1134, 1144, 1154, 1165, 1176,
		 1187, 1197, 1208, 1218, 1229, 1240, 1250, 1261,
		 1272, 1283, 1293, 1303, 1314, 1325, 1335, 1345,
		 1355, 1365, 1374, 1384, 1393, 1403, 1412, 1421,
		 1431, 1440, 1449, 1458, 1468, 1477, 1486, 1496,
		 1506, 1516, 1526, 1537, 1548, 1559, 1571, 1583,
		 1597, 1609, 1619, 1628, 1638, 1648, 1658, 1668,
		 1679, 1688, 1698, 1708, 1718, 1727, 1736, 1746,
		 1757, 1767, 1776, 1786, 1796, 1806, 1814, 1823,
		 1833, 1843, 1852, 1862, 1872, 1881, 1891, 1901,
		 1911, 1921, 1932, 1942, 1953, 1963, 1973, 1984,
		 1994, 2004, 2015, 2027, 2038, 2050, 2060, 2070,
		 2075, 2083, 2093, 2102, 2111, 2120, 2129, 2139,
		 2149, 2159, 2169, 2179, 2190, 2202, 2213, 2225,
		 2237, 2250, 2261, 2273, 2285, 2296, 2307, 2319,
		 2330, 2343, 2356, 2370, 2381, 2394, 2406, 2417,
		 2430, 2446, 2460, 2472, 2486, 2500, 2509, 2520,
		 2535, 2551, 2567, 2583, 2598, 2613, 2626, 2640,
		 2655, 2672, 2692, 2709, 2724, 2736, 2758, 2792,
		 2804, 2817, 2830, 2843, 2856, 2869, 2881, 2894,
		 2907, 2919, 2932, 2945, 2957, 2970, 2983, 2995,
		 3008, 3020, 3033, 3045, 3058, 3070, 3083, 3095,
		 3107, 3120, 3132, 3144, 3157, 3169, 3181, 3194,
		 3206, 3218, 3230, 3242, 3254, 3267, 3279, 3291,
		 3303, 3315, 3327, 3339, 3351, 3363, 3375, 3387,
		 3399, 3410, 3422, 3434, 3446, 3458, 3469, 3481,
		 3493, 3505, 3516, 3528, 3540, 3551, 3563, 3574,
		 3586, 3598, 3609, 3621, 3632, 3644, 3655, 3666,
		 3678, 3689, 3701, 3712, 3723, 3734, 3746, 3757,
		 3768, 3779, 3791, 3802, 3813, 3824, 3835, 3846,
		 3857, 3868, 3879, 3890, 3901, 3912, 3923, 3934,
		/* B */
		 1015, 1028, 1039, 1049, 1059, 1069, 1081, 1092,
		 1102, 1112, 1123, 1134, 1144, 1154, 1166, 1178,
		 1191, 1204, 1216, 1228, 1240, 1252, 1262, 1271,
		 1282, 1293, 1302, 1311, 1320, 1330, 1339, 1348,
		 1357, 1366, 1374, 1383, 1392, 1401, 1409, 1418,
		 1426, 1434, 1443, 1451, 1461, 1468, 1477, 1485,
		 1495, 1504, 1513, 1522, 1532, 1541, 1551, 1561,
		 1570, 1580, 1590, 1601, 1610, 1620, 1629, 1640,
		 1649, 1658, 1665, 1676, 1685, 1694, 1703, 1712,
		 1721, 1729, 1738, 1747, 1757, 1766, 1774, 1783,
		 1791, 1799, 1808, 1819, 1828, 1837, 1847, 1856,
		 1864, 1872, 1881, 1889, 1898, 1907, 1916, 1923,
		 1930, 1937, 1946, 1953, 1962, 1971, 1981, 1991,
		 1999, 2007, 2015, 2026, 2035, 2044, 2052, 2062,
		 2070, 2078, 2087, 2096, 2107, 2120, 2132, 2143,
		 2154, 2164, 2170, 2182, 2191, 2204, 2215, 2230,
		 2238, 2250, 2263, 2275, 2283, 2296, 2309, 2321,
		 2332, 2343, 2354, 2363, 2373, 2381, 2393, 2403,
		 2415, 2421, 2433, 2444, 2457, 2459, 2469, 2481,
		 2492, 2502, 2519, 2533, 2550, 2565, 2585, 2614,
		 2627, 2640, 2653, 2666, 2679, 2692, 2705, 2718,
		 2730, 2743, 2756, 2769, 2781, 2794, 2807, 2820,
		 2832, 2845, 2858, 2870, 2883, 2895, 2908, 2920,
		 2933, 2945, 2958, 2970, 2983, 2995, 3008, 3020,
		 3032, 3045, 3057, 3069, 3081, 3094, 3106, 3118,
		 3130, 3142, 3155, 3167, 3179, 3191, 3203, 3215,
		 3227, 3239, 3251, 3263, 3275, 3287, 3299, 3311,
		 3322, 3334, 3346, 3358, 3370, 3381, 3393, 3405,
		 3416, 3428, 3440, 3451, 3463, 3475, 3486, 3498,
		 3509, 3521, 3532, 3544, 3555, 3567, 3578, 3589,
		 3601, 3612, 3623, 3635, 3646, 3657, 3668, 3680,
		 3691, 3702, 3713, 3724, 3735, 3746, 3757, 3769
	},
	{
		/* lsc - 8 */
		/* R */
		 1020, 1022, 1028, 1033, 1036, 1039, 1043, 1046,
		 1050, 1055, 1059, 1063, 1066, 1071, 1074, 1078,
		 1081, 1084, 1089, 1093, 1098, 1101, 1105, 1109,
		 1112, 1115, 1118, 1122, 1126, 1129, 1133, 1135,
		 1138, 1141, 1143, 1146, 1150, 1154, 1157, 1160,
		 1162, 1165, 1167, 1169, 1172, 1177, 1181, 1183,
		 1186, 1188, 1192, 1195, 1196, 1200, 1204, 1208,
		 1210, 1212, 1214, 1218, 1222, 1225, 1227, 1230,
		 1234, 1237, 1240, 1242, 1245, 1248, 1251, 1254,
		 1256, 1259, 1262, 1266, 1268, 1270, 1272, 1275,
		 1279, 1282, 1285, 1288, 1291, 1293, 1297, 1300,
		 1303, 1305, 1308, 1311, 1314, 1315, 1319, 1322,
		 1327, 1331, 1334, 1335, 1336, 1339, 1342, 1345,
		 1347, 1350, 1353, 1357, 1359, 1362, 1365, 1370,
		 1372, 1374, 1377, 1380, 1382, 1385, 1388, 1390,
		 1392, 1395, 1400, 1403, 1405, 1406, 1408, 1412,
		 1417, 1421, 1423, 1426, 1429, 1431, 1434, 1437,
		 1441, 1443, 1445, 1446, 1448, 1452, 1454, 1458,
		 1462, 1467, 1469, 1471, 1474, 1477, 1481, 1485,
		 1488, 1492, 1492, 1496, 1499, 1503, 1506, 1510,
		 1512, 1514, 1517, 1521, 1526, 1529, 1531, 1534,
		 1538, 1539, 1542, 1546, 1550, 1553, 1556, 1558,
		 1562, 1568, 1570, 1573, 1576, 1580, 1581, 1585,
		 1591, 1598, 1600, 1603, 1605, 1611, 1613, 1616,
		 1616, 1619, 1626, 1632, 1636, 1637, 1642, 1645,
		 1650, 1655, 1662, 1664, 1666, 1668, 1674, 1682,
		 1686, 1690, 1691, 1698, 1702, 1710, 1715, 1721,
		 1720, 1724, 1734, 1745, 1747, 1749, 1753, 1757,
		 1760, 1761, 1768, 1772, 1780, 1780, 1788, 1795,
		 1806, 1810, 1822, 1831, 1836, 1832, 1836, 1844,
		 1855, 1858, 1863, 1869, 1874, 1880, 1885, 1891,
		 1897, 1902, 1908, 1913, 1919, 1924, 1930, 1935,
		/* G */
		 1024, 1022, 1026, 1030, 1033, 1035, 1037, 1039,
		 1044, 1047, 1049, 1051, 1054, 1056, 1058, 1062,
		 1065, 1068, 1069, 1073, 1076, 1079, 1083, 1085,
		 1088, 1090, 1094, 1097, 1099, 1102, 1105, 1108,
		 1110, 1113, 1115, 1117, 1121, 1124, 1126, 1129,
		 1132, 1134, 1135, 1137, 1139, 1142, 1145, 1146,
		 1148, 1150, 1154, 1156, 1157, 1159, 1163, 1166,
		 1167, 1168, 1169, 1173, 1175, 1178, 1180, 1182,
		 1185, 1187, 1189, 1191, 1193, 1196, 1198, 1200,
		 1202, 1203, 1206, 1209, 1212, 1213, 1214, 1216,
		 1219, 1222, 1224, 1225, 1229, 1231, 1235, 1237,
		 1239, 1239, 1242, 1244, 1246, 1248, 1250, 1252,
		 1256, 1260, 1262, 1262, 1263, 1266, 1269, 1270,
		 1272, 1275, 1278, 1281, 1282, 1284, 1287, 1290,
		 1292, 1294, 1295, 1297, 1299, 1301, 1304, 1307,
		 1308, 1311, 1314, 1316, 1318, 1320, 1321, 1324,
		 1328, 1331, 1332, 1334, 1336, 1338, 1340, 1344,
		 1346, 1349, 1350, 1352, 1354, 1357, 1358, 1360,
		 1364, 1367, 1370, 1371, 1373, 1375, 1378, 1382,
		 1384, 1389, 1390, 1393, 1395, 1398, 1400, 1404,
		 1406, 1407, 1410, 1413, 1417, 1419, 1421, 1423,
		 1425, 1428, 1431, 1433, 1436, 1440, 1444, 1446,
		 1448, 1452, 1455, 1459, 1462, 1464, 1465, 1468,
		 1472, 1478, 1480, 1482, 1485, 1489, 1490, 1494,
		 1498, 1501, 1504, 1508, 1512, 1514, 1519, 1520,
		 1525, 1529, 1536, 1537, 1539, 1543, 1549, 1554,
		 1557, 1562, 1564, 1567, 1570, 1576, 1580, 1583,
		 1584, 1590, 1599, 1608, 1609, 1608, 1610, 1616,
		 1623, 1629, 1638, 1642, 1648, 1649, 1655, 1662,
		 1675, 1678, 1680, 1682, 1692, 1704, 1715, 1722,
		 1726, 1731, 1736, 1742, 1747, 1753, 1759, 1764,
		 1770, 1776, 1781, 1787, 1792, 1798, 1804, 1809,
		/* B */
		 1023, 1023, 1026, 1030, 1032, 1035, 1036, 1038,
		 1040, 1042, 1043, 1045, 1047, 1050, 1052, 1055,
		 1057, 1059, 1061, 1064, 1065, 1067, 1070, 1073,
		 1074, 1074, 1078, 1080, 1082, 1084, 1087, 1087,
		 1088, 1092, 1094, 1095, 1098, 1100, 1102, 1103,
		 1106, 1107, 1109, 1111, 1113, 1116, 1118, 1119,
		 1121, 1124, 1126, 1127, 1129, 1131, 1134, 1136,
		 1137, 1138, 1140, 1142, 1145, 1148, 1150, 1151,
		 1153, 1156, 1158, 1160, 1161, 1164, 1165, 1166,
		 1168, 1170, 1173, 1175, 1176, 1176, 1178, 1181,
		 1182, 1185, 1187, 1188, 1189, 1192, 1195, 1197,
		 1197, 1197, 1199, 1201, 1204, 1205, 1206, 1207,
		 1209, 1213, 1214, 1213, 1214, 1217, 1219, 1220,
		 1220, 1222, 1226, 1227, 1226, 1228, 1231, 1235,
		 1235, 1237, 1237, 1239, 1241, 1242, 1245, 1246,
		 1248, 1250, 1252, 1254, 1256, 1257, 1258, 1260,
		 1263, 1265, 1265, 1266, 1268, 1269, 1270, 1274,
		 1277, 1279, 1279, 1280, 1282, 1284, 1285, 1285,
		 1289, 1293, 1294, 1295, 1297, 1298, 1299, 1303,
		 1306, 1308, 1309, 1311, 1312, 1314, 1317, 1320,
		 1321, 1322, 1324, 1325, 1329, 1332, 1335, 1337,
		 1338, 1339, 1341, 1344, 1347, 1351, 1352, 1356,
		 1358, 1360, 1362, 1364, 1367, 1368, 1370, 1373,
		 1378, 1382, 1382, 1383, 1385, 1388, 1391, 1395,
		 1396, 1396, 1399, 1403, 1408, 1410, 1414, 1414,
		 1418, 1422, 1425, 1423, 1424, 1427, 1432, 1436,
		 1440, 1446, 1447, 1451, 1451, 1454, 1455, 1458,
		 1460, 1468, 1477, 1483, 1480, 1479, 1481, 1484,
		 1490, 1495, 1503, 1506, 1513, 1514, 1518, 1524,
		 1535, 1536, 1531, 1527, 1535, 1549, 1559, 1558,
		 1553, 1547, 1553, 1559, 1565, 1570, 1576, 1582,
		 1587, 1593, 1599, 1605, 1610, 1616, 1622, 1627
	},
	{
		/* lsc - 9 */
		/* R */
		 1025, 1022, 1026, 1031, 1035, 1038, 1042, 1046,
		 1049, 1052, 1058, 1062, 1064, 1066, 1070, 1077,
		 1081, 1084, 1086, 1089, 1093, 1098, 1102, 1105,
		 1108, 1112, 1115, 1118, 1122, 1126, 1130, 1133,
		 1137, 1140, 1142, 1145, 1149, 1152, 1155, 1158,
		 1162, 1165, 1167, 1171, 1175, 1178, 1182, 1185,
		 1189, 1192, 1196, 1201, 1203, 1205, 1207, 1212,
		 1215, 1218, 1222, 1225, 1229, 1232, 1235, 1237,
		 1239, 1243, 1246, 1250, 1252, 1257, 1259, 1262,
		 1265, 1269, 1272, 1275, 1277, 1279, 1282, 1285,
		 1288, 1292, 1296, 1299, 1301, 1304, 1308, 1312,
		 1315, 1317, 1319, 1323, 1327, 1329, 1331, 1335,
		 1339, 1342, 1344, 1346, 1348, 1351, 1355, 1359,
		 1361, 1363, 1365, 1369, 1373, 1376, 1378, 1380,
		 1383, 1386, 1389, 1390, 1394, 1398, 1401, 1403,
		 1406, 1410, 1413, 1414, 1416, 1420, 1424, 1428,
		 1430, 1433, 1436, 1440, 1441, 1445, 1449, 1454,
		 1456, 1460, 1463, 1465, 1464, 1467, 1472, 1477,
		 1480, 1482, 1484, 1488, 1492, 1496, 1498, 1500,
		 1503, 1507, 1510, 1514, 1516, 1520, 1525, 1529,
		 1528, 1531, 1535, 1539, 1540, 1543, 1545, 1548,
		 1551, 1553, 1556, 1560, 1564, 1568, 1570, 1571,
		 1574, 1579, 1580, 1582, 1588, 1593, 1595, 1596,
		 1598, 1602, 1607, 1610, 1612, 1616, 1619, 1622,
		 1624, 1628, 1631, 1633, 1637, 1641, 1646, 1648,
		 1652, 1657, 1661, 1667, 1673, 1676, 1679, 1682,
		 1686, 1691, 1696, 1702, 1706, 1709, 1711, 1714,
		 1717, 1723, 1731, 1735, 1737, 1742, 1750, 1754,
		 1753, 1753, 1759, 1768, 1773, 1776, 1784, 1791,
		 1800, 1805, 1812, 1819, 1831, 1841, 1840, 1849,
		 1862, 1863, 1869, 1875, 1882, 1888, 1894, 1901,
		 1907, 1913, 1920, 1926, 1932, 1938, 1945, 1951,
		/* G */
		 1019, 1024, 1028, 1031, 1035, 1037, 1040, 1044,
		 1047, 1050, 1055, 1059, 1061, 1063, 1067, 1072,
		 1076, 1079, 1082, 1084, 1088, 1091, 1095, 1098,
		 1100, 1104, 1106, 1109, 1112, 1115, 1118, 1120,
		 1123, 1125, 1127, 1130, 1133, 1136, 1139, 1142,
		 1143, 1145, 1147, 1150, 1153, 1156, 1159, 1163,
		 1165, 1166, 1169, 1174, 1177, 1179, 1181, 1184,
		 1187, 1190, 1192, 1195, 1198, 1201, 1203, 1205,
		 1207, 1209, 1212, 1214, 1217, 1220, 1223, 1225,
		 1228, 1231, 1234, 1236, 1237, 1239, 1242, 1246,
		 1248, 1251, 1253, 1255, 1257, 1260, 1264, 1268,
		 1271, 1272, 1272, 1274, 1278, 1281, 1284, 1285,
		 1287, 1290, 1292, 1295, 1297, 1299, 1302, 1306,
		 1308, 1309, 1311, 1315, 1316, 1319, 1321, 1323,
		 1326, 1329, 1331, 1332, 1335, 1337, 1340, 1342,
		 1345, 1348, 1351, 1353, 1354, 1357, 1360, 1363,
		 1366, 1369, 1370, 1372, 1374, 1378, 1381, 1384,
		 1385, 1388, 1392, 1394, 1395, 1396, 1399, 1403,
		 1406, 1407, 1409, 1412, 1416, 1420, 1421, 1423,
		 1427, 1429, 1432, 1435, 1438, 1441, 1444, 1446,
		 1446, 1450, 1453, 1456, 1458, 1461, 1465, 1469,
		 1471, 1472, 1474, 1478, 1482, 1486, 1488, 1489,
		 1492, 1496, 1500, 1505, 1508, 1511, 1514, 1515,
		 1517, 1520, 1526, 1528, 1532, 1537, 1540, 1543,
		 1545, 1548, 1552, 1556, 1560, 1564, 1567, 1569,
		 1574, 1579, 1583, 1587, 1591, 1594, 1598, 1602,
		 1607, 1612, 1616, 1621, 1624, 1626, 1630, 1635,
		 1638, 1643, 1651, 1657, 1662, 1666, 1672, 1678,
		 1683, 1685, 1690, 1694, 1698, 1704, 1713, 1721,
		 1727, 1731, 1730, 1734, 1746, 1764, 1774, 1782,
		 1785, 1783, 1790, 1796, 1803, 1809, 1815, 1822,
		 1828, 1834, 1841, 1847, 1853, 1860, 1866, 1872,
		/* B */
		 1018, 1025, 1026, 1029, 1032, 1034, 1035, 1038,
		 1040, 1043, 1046, 1048, 1049, 1052, 1054, 1057,
		 1060, 1062, 1065, 1066, 1069, 1072, 1074, 1076,
		 1078, 1082, 1085, 1087, 1088, 1089, 1092, 1095,
		 1098, 1099, 1100, 1103, 1105, 1106, 1108, 1111,
		 1112, 1113, 1115, 1118, 1120, 1122, 1124, 1126,
		 1126, 1128, 1129, 1134, 1135, 1137, 1137, 1140,
		 1142, 1144, 1145, 1147, 1148, 1151, 1153, 1154,
		 1155, 1158, 1158, 1160, 1161, 1165, 1166, 1168,
		 1170, 1172, 1174, 1176, 1176, 1177, 1180, 1181,
		 1183, 1186, 1188, 1189, 1190, 1192, 1195, 1199,
		 1200, 1201, 1200, 1201, 1205, 1208, 1209, 1210,
		 1212, 1214, 1215, 1216, 1218, 1219, 1222, 1225,
		 1225, 1226, 1228, 1230, 1230, 1233, 1234, 1236,
		 1238, 1239, 1241, 1242, 1244, 1245, 1246, 1247,
		 1250, 1253, 1256, 1257, 1258, 1260, 1261, 1261,
		 1264, 1267, 1268, 1268, 1271, 1274, 1277, 1279,
		 1281, 1282, 1283, 1285, 1285, 1287, 1288, 1290,
		 1292, 1293, 1295, 1297, 1300, 1303, 1305, 1306,
		 1309, 1309, 1310, 1312, 1316, 1319, 1321, 1321,
		 1322, 1325, 1328, 1330, 1330, 1332, 1335, 1338,
		 1340, 1342, 1345, 1348, 1350, 1350, 1350, 1353,
		 1356, 1361, 1364, 1366, 1369, 1370, 1372, 1373,
		 1375, 1379, 1383, 1382, 1384, 1389, 1392, 1393,
		 1395, 1398, 1400, 1402, 1406, 1410, 1415, 1418,
		 1421, 1422, 1422, 1424, 1426, 1429, 1434, 1435,
		 1437, 1440, 1444, 1448, 1450, 1452, 1458, 1464,
		 1465, 1464, 1469, 1471, 1473, 1475, 1479, 1483,
		 1484, 1486, 1491, 1497, 1502, 1505, 1508, 1512,
		 1520, 1525, 1527, 1532, 1539, 1547, 1552, 1564,
		 1567, 1549, 1555, 1562, 1568, 1574, 1581, 1587,
		 1594, 1600, 1607, 1613, 1620, 1626, 1632, 1639
	},
	{
		/* lsc - 10 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	},
	{
		/* lsc - 11 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	}
	},
	.linear_tbl = {
		/* R */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1344, 1360, 1376, 1392,
		 1408, 1424, 1440, 1456, 1472, 1488, 1504, 1520,
		 1536, 1552, 1568, 1584, 1600, 1616, 1632, 1648,
		 1664, 1680, 1696, 1712, 1728, 1744, 1760, 1776,
		 1792, 1808, 1824, 1840, 1856, 1872, 1888, 1904,
		 1920, 1936, 1952, 1968, 1984, 2000, 2016, 2032,
		 2048, 2064, 2080, 2096, 2112, 2128, 2144, 2160,
		 2176, 2192, 2208, 2224, 2240, 2256, 2272, 2288,
		 2304, 2320, 2336, 2352, 2368, 2384, 2400, 2416,
		 2432, 2448, 2464, 2480, 2496, 2512, 2528, 2544,
		 2560, 2576, 2592, 2608, 2624, 2640, 2656, 2672,
		 2688, 2704, 2720, 2736, 2752, 2768, 2784, 2800,
		 2816, 2832, 2848, 2864, 2880, 2896, 2912, 2928,
		 2944, 2960, 2976, 2992, 3008, 3024, 3040, 3056,
		 3072, 3088, 3104, 3120, 3136, 3152, 3168, 3184,
		 3200, 3216, 3232, 3248, 3264, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* G */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* B */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080
	},
	.disc_tbl = {
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0
	},
	.isp_cem_table = {
		0x40, 0x3F, 0x48, 0x3F, 0x40, 0x47, 0x48, 0x47,
		0x50, 0x3F, 0x58, 0x3F, 0x50, 0x47, 0x58, 0x47,
		0x60, 0x3F, 0x68, 0x3F, 0x60, 0x47, 0x68, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x4F, 0x48, 0x4F, 0x40, 0x57, 0x48, 0x57,
		0x50, 0x4F, 0x58, 0x4F, 0x50, 0x57, 0x58, 0x57,
		0x60, 0x4F, 0x68, 0x4F, 0x60, 0x57, 0x68, 0x57,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x5F, 0x48, 0x5F, 0x40, 0x67, 0x48, 0x67,
		0x50, 0x5F, 0x58, 0x5F, 0x50, 0x67, 0x58, 0x67,
		0x60, 0x5F, 0x68, 0x5F, 0x60, 0x67, 0x68, 0x67,
		0x70, 0x5F, 0x78, 0x60, 0x70, 0x67, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x6F, 0x48, 0x6F, 0x40, 0x77, 0x48, 0x77,
		0x50, 0x6F, 0x58, 0x6F, 0x50, 0x77, 0x58, 0x77,
		0x60, 0x6F, 0x68, 0x6F, 0x60, 0x77, 0x68, 0x77,
		0x70, 0x6F, 0x78, 0x6F, 0x70, 0x77, 0x78, 0x77,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x7F, 0x48, 0x7F, 0x3F, 0x87, 0x47, 0x87,
		0x50, 0x7F, 0x58, 0x7F, 0x4F, 0x87, 0x57, 0x87,
		0x60, 0x7F, 0x68, 0x7F, 0x5F, 0x87, 0x67, 0x87,
		0x70, 0x7F, 0x78, 0x7F, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x8F, 0x88, 0x8F, 0x80, 0x97, 0x88, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0x9F, 0x88, 0x9F, 0x80, 0xA7, 0x88, 0xA7,
		0x90, 0x9F, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xAF, 0x88, 0xAF, 0x80, 0xB7, 0x88, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x90, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0x35, 0x54, 0x36, 0x4B, 0x3D, 0x53, 0x3E,
		0x5C, 0x38, 0x62, 0x3A, 0x5B, 0x40, 0x63, 0x41,
		0x66, 0x3D, 0x69, 0x3F, 0x67, 0x44, 0x6B, 0x46,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x99, 0x40, 0x90, 0x48, 0x99, 0x48,
		0xA2, 0x41, 0xAC, 0x42, 0xA3, 0x4A, 0xAC, 0x4B,
		0xB5, 0x44, 0xBD, 0x45, 0xB4, 0x4C, 0xBC, 0x4C,
		0xC4, 0x45, 0x00, 0x00, 0xC3, 0x4D, 0x00, 0x00,
		0x49, 0x44, 0x51, 0x46, 0x48, 0x4C, 0x50, 0x4E,
		0x59, 0x47, 0x61, 0x49, 0x58, 0x4F, 0x60, 0x51,
		0x68, 0x4B, 0x6C, 0x4D, 0x67, 0x52, 0x6C, 0x52,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x9A, 0x51, 0x93, 0x52, 0x9F, 0x52,
		0xA3, 0x52, 0xAD, 0x52, 0xAB, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xBE, 0x52, 0xC0, 0x52, 0xCB, 0x52,
		0xC7, 0x52, 0x00, 0x00, 0xD3, 0x52, 0x00, 0x00,
		0x47, 0x54, 0x4E, 0x56, 0x45, 0x5C, 0x4D, 0x5E,
		0x56, 0x57, 0x5B, 0x55, 0x55, 0x5F, 0x5B, 0x5F,
		0x60, 0x52, 0x69, 0x52, 0x5B, 0x58, 0x60, 0x52,
		0x6E, 0x52, 0x74, 0x52, 0x6C, 0x52, 0x71, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x99, 0x52, 0xA9, 0x52, 0xA3, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xC3, 0x52, 0xC7, 0x52, 0xD7, 0x52,
		0xD0, 0x52, 0xDA, 0x52, 0xE5, 0x52, 0xEB, 0x54,
		0xE4, 0x52, 0x00, 0x00, 0xEB, 0x59, 0x00, 0x00,
		0x44, 0x64, 0x4B, 0x65, 0x42, 0x6C, 0x4A, 0x6D,
		0x53, 0x67, 0x5B, 0x68, 0x52, 0x6F, 0x5A, 0x70,
		0x5B, 0x64, 0x5B, 0x5C, 0x5B, 0x6F, 0x5B, 0x6B,
		0x5F, 0x51, 0x6D, 0x4F, 0x5A, 0x63, 0x6D, 0x64,
		0x80, 0x53, 0x97, 0x56, 0x80, 0x6C, 0x95, 0x6D,
		0xAF, 0x57, 0xC5, 0x58, 0xA8, 0x6D, 0xB6, 0x6E,
		0xD7, 0x58, 0xDC, 0x5D, 0xD1, 0x6B, 0xDB, 0x6D,
		0xE5, 0x5F, 0xEB, 0x61, 0xE5, 0x6F, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x75, 0x48, 0x76, 0x3F, 0x81, 0x47, 0x82,
		0x50, 0x77, 0x58, 0x79, 0x4F, 0x83, 0x57, 0x85,
		0x5B, 0x79, 0x5B, 0x79, 0x5B, 0x87, 0x5B, 0x8B,
		0x5C, 0x79, 0x6C, 0x7C, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x8D, 0x80, 0x80, 0x8F, 0x92, 0x92,
		0xA5, 0x80, 0xB6, 0x80, 0xA3, 0x91, 0xB6, 0x92,
		0xD1, 0x80, 0xDB, 0x80, 0xD1, 0x94, 0xDB, 0x92,
		0xE5, 0x80, 0xEB, 0x80, 0xE5, 0x90, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x3F, 0x8D, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5B, 0x91, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x5F, 0xA0, 0x6D, 0xA6, 0x5E, 0xB2, 0x71, 0xB6,
		0x83, 0xA7, 0x92, 0xA4, 0x86, 0xC7, 0x97, 0xBD,
		0xA6, 0xA6, 0xBC, 0xA8, 0xA5, 0xB8, 0xB4, 0xB4,
		0xCC, 0xA6, 0xD8, 0xA3, 0xC4, 0xB3, 0xD1, 0xB0,
		0xE5, 0xA1, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBE,
		0x5E, 0xC9, 0x70, 0xD6, 0x5D, 0xE2, 0x74, 0xD9,
		0x87, 0xD9, 0x95, 0xC6, 0x87, 0xD6, 0x94, 0xCF,
		0xA3, 0xC3, 0xB0, 0xC0, 0xA0, 0xCA, 0xAA, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB7, 0x5B, 0xCE, 0x5B, 0xC2, 0x5B, 0xDE,
		0x65, 0xE2, 0x77, 0xDD, 0x69, 0xE4, 0x79, 0xDC,
		0x87, 0xD6, 0x92, 0xD0, 0x87, 0xD6, 0x90, 0xD1,
		0x9C, 0xCB, 0xA5, 0xC7, 0x99, 0xCD, 0xA1, 0xC9,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA9, 0xC5, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xCD, 0x5D, 0xEA, 0x00, 0x00, 0x00, 0x00,
		0x6D, 0xE2, 0x7B, 0xDB, 0x00, 0x00, 0x00, 0x00,
		0x87, 0xD6, 0x8F, 0xD2, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xCE, 0x9F, 0xCA, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC7, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x55, 0x24, 0x5C, 0x24, 0x4D, 0x24, 0x55, 0x24,
		0x64, 0x24, 0x66, 0x24, 0x5D, 0x24, 0x66, 0x24,
		0x64, 0x24, 0x63, 0x24, 0x67, 0x24, 0x64, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8C, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x98, 0x24, 0xA5, 0x24, 0x9C, 0x24, 0xAD, 0x24,
		0xB7, 0x24, 0xC7, 0x29, 0xC3, 0x24, 0xC7, 0x37,
		0xC7, 0x3C, 0xC7, 0x45, 0xC7, 0x44, 0xC7, 0x4C,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x44, 0x24, 0x4C, 0x24, 0x38, 0x24, 0x41, 0x24,
		0x55, 0x24, 0x5F, 0x24, 0x4A, 0x24, 0x55, 0x24,
		0x67, 0x24, 0x64, 0x24, 0x60, 0x22, 0x66, 0x21,
		0x63, 0x24, 0x70, 0x24, 0x64, 0x22, 0x6D, 0x23,
		0x80, 0x24, 0x90, 0x24, 0x80, 0x25, 0x93, 0x26,
		0xA0, 0x24, 0xB7, 0x24, 0xA8, 0x27, 0xC4, 0x28,
		0xC7, 0x31, 0xC7, 0x42, 0xC4, 0x43, 0xC7, 0x4C,
		0xC7, 0x4C, 0xC7, 0x53, 0xC7, 0x54, 0xC7, 0x5A,
		0xC7, 0x58, 0x00, 0x00, 0xC7, 0x5D, 0x00, 0x00,
		0x37, 0x33, 0x37, 0x2A, 0x37, 0x42, 0x37, 0x3C,
		0x3C, 0x24, 0x48, 0x24, 0x37, 0x33, 0x37, 0x26,
		0x54, 0x22, 0x62, 0x1D, 0x42, 0x21, 0x53, 0x20,
		0x63, 0x1E, 0x68, 0x21, 0x6C, 0x35, 0x69, 0x36,
		0x80, 0x27, 0x96, 0x2A, 0x80, 0x3D, 0x96, 0x3F,
		0xB2, 0x2C, 0xBF, 0x44, 0xB0, 0x4A, 0xBC, 0x53,
		0xC4, 0x4E, 0xC7, 0x56, 0xC4, 0x5A, 0xC7, 0x5E,
		0xC7, 0x5C, 0xC7, 0x60, 0xC7, 0x62, 0xC7, 0x65,
		0xC7, 0x62, 0x00, 0x00, 0xC7, 0x67, 0x00, 0x00,
		0x37, 0x4F, 0x37, 0x4B, 0x37, 0x5B, 0x37, 0x5A,
		0x37, 0x46, 0x37, 0x3F, 0x37, 0x57, 0x37, 0x54,
		0x35, 0x31, 0x39, 0x1F, 0x36, 0x4E, 0x34, 0x44,
		0x66, 0x48, 0x73, 0x56, 0x54, 0x52, 0x6F, 0x62,
		0x80, 0x58, 0x96, 0x5A, 0x80, 0x70, 0x93, 0x70,
		0xA5, 0x64, 0xBC, 0x62, 0xA4, 0x71, 0xBA, 0x6D,
		0xC3, 0x64, 0xC7, 0x66, 0xC3, 0x6F, 0xC7, 0x71,
		0xC7, 0x69, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x7B, 0x37, 0x7D,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x80, 0x37, 0x85,
		0x39, 0x68, 0x3F, 0x6A, 0x3E, 0x8B, 0x4D, 0x90,
		0x58, 0x73, 0x6B, 0x7B, 0x5E, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x92, 0x80, 0x81, 0x8F, 0x92, 0x92,
		0xA4, 0x80, 0xBA, 0x80, 0xA4, 0x92, 0xB9, 0x93,
		0xC3, 0x80, 0xC7, 0x80, 0xC3, 0x90, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x8D, 0x37, 0x92, 0x37, 0x9A, 0x37, 0x9E,
		0x37, 0x97, 0x37, 0x9C, 0x37, 0xA3, 0x37, 0xAB,
		0x3F, 0xA0, 0x4D, 0xA1, 0x3F, 0xB0, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA3, 0x5D, 0xB6, 0x72, 0xBC,
		0x86, 0xA9, 0x93, 0xA4, 0x8B, 0xC8, 0x98, 0xBC,
		0xA4, 0xA4, 0xBA, 0xA6, 0xA6, 0xB8, 0xB9, 0xB9,
		0xC3, 0xA1, 0xC7, 0x9C, 0xC3, 0xB2, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA3, 0x37, 0xA8, 0x37, 0xAC, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x3E, 0xC1, 0x4A, 0xC6, 0x3E, 0xD2, 0x51, 0xD3,
		0x5F, 0xCB, 0x75, 0xD4, 0x66, 0xD5, 0x79, 0xD8,
		0x8F, 0xDF, 0x9D, 0xD2, 0x8F, 0xDB, 0x9B, 0xD7,
		0xAA, 0xCD, 0xB8, 0xCB, 0xA8, 0xD5, 0xB4, 0xD4,
		0xC3, 0xC3, 0xC7, 0xB9, 0xC3, 0xD3, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x40, 0xDA,
		0x45, 0xDA, 0x59, 0xDA, 0x51, 0xDA, 0x61, 0xDA,
		0x6C, 0xDA, 0x7D, 0xDA, 0x71, 0xDA, 0x7F, 0xDA,
		0x8E, 0xDA, 0x99, 0xDA, 0x8E, 0xDA, 0x98, 0xDA,
		0xA5, 0xDA, 0xB1, 0xDA, 0xA2, 0xDA, 0xAC, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB6, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x49, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x58, 0xDA, 0x67, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x74, 0xDA, 0x81, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xDA, 0x96, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x9F, 0xDA, 0xA8, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xDA, 0xBA, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x37,
		0x68, 0x33, 0x6A, 0x32, 0x63, 0x35, 0x6A, 0x32,
		0x69, 0x32, 0x68, 0x33, 0x6A, 0x32, 0x69, 0x32,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8C, 0x21, 0x80, 0x27, 0x8E, 0x20,
		0x9B, 0x1A, 0xA3, 0x29, 0x9F, 0x18, 0xA3, 0x38,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3C, 0x58, 0x3A, 0x4D, 0x3F, 0x52, 0x3D,
		0x5E, 0x37, 0x64, 0x34, 0x57, 0x3B, 0x5E, 0x37,
		0x6B, 0x31, 0x69, 0x32, 0x66, 0x34, 0x6A, 0x32,
		0x68, 0x33, 0x72, 0x2E, 0x69, 0x32, 0x6F, 0x2F,
		0x80, 0x27, 0x91, 0x1F, 0x80, 0x2C, 0x92, 0x29,
		0xA1, 0x22, 0xA3, 0x45, 0xA1, 0x37, 0xA3, 0x52,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x46, 0x43, 0x4A, 0x41, 0x3E, 0x47, 0x41, 0x45,
		0x50, 0x3E, 0x56, 0x3B, 0x46, 0x43, 0x4D, 0x41,
		0x5E, 0x37, 0x68, 0x33, 0x56, 0x3F, 0x61, 0x3F,
		0x6A, 0x36, 0x6E, 0x39, 0x6D, 0x3B, 0x6C, 0x42,
		0x80, 0x36, 0x94, 0x33, 0x80, 0x45, 0x91, 0x4D,
		0xA1, 0x49, 0xA3, 0x5E, 0xA0, 0x5B, 0xA3, 0x66,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x33, 0x4C, 0x36, 0x4B, 0x26, 0x52, 0x28, 0x51,
		0x3E, 0x4B, 0x46, 0x4C, 0x30, 0x53, 0x3A, 0x55,
		0x4D, 0x4B, 0x58, 0x4A, 0x45, 0x58, 0x5C, 0x63,
		0x68, 0x4E, 0x73, 0x57, 0x68, 0x67, 0x73, 0x69,
		0x80, 0x5C, 0x91, 0x63, 0x80, 0x6F, 0x91, 0x73,
		0xA0, 0x68, 0xA3, 0x6E, 0xA0, 0x72, 0xA3, 0x74,
		0xA3, 0x71, 0xA8, 0x71, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x5C, 0x13, 0x5C, 0x13, 0x78, 0x13, 0x7C,
		0x1B, 0x5E, 0x26, 0x62, 0x1D, 0x80, 0x2B, 0x86,
		0x32, 0x66, 0x4B, 0x6E, 0x3B, 0x8C, 0x4F, 0x8F,
		0x5B, 0x75, 0x72, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x91, 0x80, 0x82, 0x92, 0x91, 0x91,
		0xA0, 0x80, 0xA3, 0x80, 0xA0, 0x90, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x93, 0x13, 0x9B, 0x13, 0xA7, 0x14, 0xAC,
		0x1F, 0x9F, 0x2E, 0x9F, 0x24, 0xAC, 0x34, 0xAC,
		0x3D, 0xA0, 0x4E, 0xA0, 0x44, 0xAC, 0x53, 0xAC,
		0x5E, 0xA1, 0x71, 0xA2, 0x63, 0xAC, 0x76, 0xAC,
		0x86, 0xA6, 0x93, 0xA2, 0x87, 0xAC, 0x92, 0xAC,
		0xA0, 0xA0, 0xA3, 0x97, 0x9E, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x24, 0xAC, 0x30, 0xAC, 0x37, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x67, 0xAC,
		0x6C, 0xAC, 0x7A, 0xAC, 0x72, 0xAC, 0x7C, 0xAC,
		0x87, 0xAC, 0x8F, 0xAC, 0x87, 0xAC, 0x8D, 0xAC,
		0x98, 0xAC, 0xA1, 0xAC, 0x95, 0xAC, 0x9C, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x91, 0x50, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x71, 0x61,
		0x75, 0x5D, 0x78, 0x60, 0x78, 0x64, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x5D, 0x6A,
		0x67, 0x66, 0x6E, 0x68, 0x64, 0x6D, 0x6C, 0x70,
		0x76, 0x6B, 0x7A, 0x6E, 0x73, 0x73, 0x7B, 0x75,
		0x80, 0x70, 0x89, 0x70, 0x80, 0x78, 0x89, 0x79,
		0x92, 0x72, 0x99, 0x73, 0x90, 0x79, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x3F, 0x7B, 0x47, 0x7E,
		0x52, 0x70, 0x5A, 0x73, 0x4F, 0x80, 0x57, 0x83,
		0x61, 0x75, 0x69, 0x78, 0x5F, 0x85, 0x67, 0x87,
		0x70, 0x7A, 0x78, 0x7D, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x81, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8B, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x7A, 0x98,
		0x82, 0x8F, 0x88, 0x8F, 0x83, 0x97, 0x89, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x45, 0x3B, 0x4C, 0x3B, 0x44, 0x43, 0x4C, 0x43,
		0x54, 0x3C, 0x5C, 0x3D, 0x54, 0x44, 0x5C, 0x45,
		0x62, 0x3E, 0x68, 0x3F, 0x63, 0x46, 0x69, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA1, 0x40, 0xA9, 0x41, 0xA1, 0x48, 0xA9, 0x49,
		0xB2, 0x41, 0xBA, 0x42, 0xB2, 0x49, 0xB9, 0x49,
		0xC1, 0x42, 0x00, 0x00, 0xC1, 0x4A, 0x00, 0x00,
		0x43, 0x4B, 0x4B, 0x4B, 0x43, 0x53, 0x4B, 0x53,
		0x53, 0x4C, 0x5B, 0x4D, 0x53, 0x54, 0x5B, 0x55,
		0x63, 0x4D, 0x69, 0x4F, 0x63, 0x55, 0x6A, 0x56,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x99, 0x58,
		0xA1, 0x51, 0xA9, 0x51, 0xA1, 0x59, 0xA9, 0x59,
		0xB1, 0x51, 0xB9, 0x51, 0xB1, 0x59, 0xB9, 0x59,
		0xC0, 0x51, 0x00, 0x00, 0xC0, 0x59, 0x00, 0x00,
		0x42, 0x5B, 0x4A, 0x5B, 0x41, 0x63, 0x49, 0x63,
		0x52, 0x5C, 0x5A, 0x5D, 0x51, 0x64, 0x59, 0x65,
		0x62, 0x5D, 0x6A, 0x5E, 0x61, 0x65, 0x69, 0x66,
		0x71, 0x5F, 0x78, 0x60, 0x71, 0x66, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x99, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA1, 0x61, 0xA9, 0x61, 0xA0, 0x69, 0xA8, 0x68,
		0xB0, 0x61, 0xB8, 0x61, 0xB0, 0x68, 0xB8, 0x68,
		0xC0, 0x61, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x41, 0x6B, 0x49, 0x6B, 0x40, 0x73, 0x48, 0x73,
		0x51, 0x6C, 0x59, 0x6D, 0x50, 0x74, 0x58, 0x75,
		0x61, 0x6D, 0x69, 0x6E, 0x60, 0x75, 0x68, 0x76,
		0x6D, 0x6A, 0x75, 0x69, 0x6D, 0x75, 0x6E, 0x6B,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x6B, 0x94, 0x6C,
		0x98, 0x69, 0xA4, 0x69, 0xA7, 0x6D, 0xB8, 0x6D,
		0xAF, 0x69, 0xBA, 0x69, 0xC9, 0x6D, 0xDC, 0x6D,
		0xC5, 0x69, 0xCF, 0x69, 0xE8, 0x6E, 0xF5, 0x6F,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x7B, 0x48, 0x7C, 0x3F, 0x85, 0x47, 0x85,
		0x50, 0x7C, 0x58, 0x7D, 0x4F, 0x86, 0x57, 0x86,
		0x60, 0x7D, 0x68, 0x7E, 0x5F, 0x87, 0x67, 0x87,
		0x6D, 0x7E, 0x6E, 0x7E, 0x6D, 0x89, 0x6F, 0x90,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x93, 0x92, 0x92,
		0xA3, 0x80, 0xB7, 0x80, 0xA4, 0x92, 0xB2, 0x90,
		0xCA, 0x80, 0xD4, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x6F, 0xA2, 0x6D, 0x9B, 0x70, 0xB1,
		0x81, 0xAA, 0x91, 0xA1, 0x81, 0xAC, 0x8D, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA5, 0x74, 0xB2, 0x6D, 0xAF, 0x77, 0xB0,
		0x81, 0xAC, 0x8A, 0xA7, 0x81, 0xAC, 0x89, 0xA8,
		0x92, 0xA3, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6F, 0xB4, 0x79, 0xB0, 0x71, 0xB8, 0x79, 0xB8,
		0x81, 0xAF, 0x89, 0xAF, 0x81, 0xB7, 0x89, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x91, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x69, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x71, 0xBF, 0x79, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x82, 0xBF, 0x89, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x54, 0x30, 0x5B, 0x33, 0x51, 0x38, 0x59, 0x3A,
		0x63, 0x35, 0x67, 0x38, 0x60, 0x3B, 0x67, 0x3B,
		0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x8A, 0x3B,
		0x92, 0x3B, 0x9B, 0x3B, 0x94, 0x3B, 0xA1, 0x3B,
		0xA8, 0x3B, 0xB5, 0x3B, 0xB0, 0x3B, 0xBF, 0x3B,
		0xC4, 0x3B, 0xCE, 0x3B, 0xCC, 0x3B, 0xD8, 0x3B,
		0xD7, 0x3B, 0x00, 0x00, 0xD9, 0x42, 0x00, 0x00,
		0x4C, 0x3B, 0x52, 0x3B, 0x49, 0x43, 0x49, 0x3B,
		0x5A, 0x3B, 0x61, 0x3B, 0x51, 0x3B, 0x5A, 0x3B,
		0x68, 0x3B, 0x69, 0x3B, 0x63, 0x3B, 0x69, 0x3B,
		0x6A, 0x3B, 0x74, 0x3B, 0x6A, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8E, 0x3B,
		0x98, 0x3B, 0xA8, 0x3B, 0x9E, 0x3B, 0xB2, 0x3B,
		0xBA, 0x3B, 0xCA, 0x3B, 0xC8, 0x3B, 0xD8, 0x3B,
		0xD8, 0x3B, 0xD9, 0x44, 0xD9, 0x45, 0xD9, 0x4E,
		0xD9, 0x4B, 0x00, 0x00, 0xD9, 0x52, 0x00, 0x00,
		0x49, 0x4E, 0x49, 0x48, 0x49, 0x57, 0x49, 0x53,
		0x49, 0x40, 0x4F, 0x3B, 0x49, 0x4E, 0x49, 0x45,
		0x5A, 0x3B, 0x65, 0x3A, 0x4C, 0x3B, 0x58, 0x39,
		0x68, 0x3B, 0x6E, 0x3B, 0x67, 0x36, 0x69, 0x37,
		0x80, 0x3C, 0x91, 0x3C, 0x80, 0x3D, 0x96, 0x40,
		0xA7, 0x3D, 0xC2, 0x3D, 0xB4, 0x42, 0xCD, 0x43,
		0xD6, 0x3D, 0xD7, 0x49, 0xCE, 0x52, 0xD4, 0x56,
		0xD9, 0x50, 0xD9, 0x55, 0xD9, 0x59, 0xD9, 0x5D,
		0xD9, 0x59, 0x00, 0x00, 0xD9, 0x60, 0x00, 0x00,
		0x46, 0x5F, 0x49, 0x5E, 0x44, 0x67, 0x49, 0x68,
		0x49, 0x5A, 0x49, 0x55, 0x49, 0x66, 0x49, 0x64,
		0x49, 0x4E, 0x48, 0x3E, 0x49, 0x60, 0x48, 0x59,
		0x58, 0x39, 0x71, 0x53, 0x48, 0x4C, 0x6F, 0x62,
		0x80, 0x57, 0x96, 0x58, 0x80, 0x6A, 0x97, 0x6C,
		0xB1, 0x5A, 0xBC, 0x60, 0xA4, 0x70, 0xBB, 0x6D,
		0xCD, 0x5F, 0xD4, 0x61, 0xCC, 0x6C, 0xD4, 0x6F,
		0xD9, 0x63, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x42, 0x6F, 0x49, 0x71, 0x3F, 0x7E, 0x47, 0x80,
		0x49, 0x71, 0x49, 0x71, 0x49, 0x82, 0x49, 0x85,
		0x49, 0x71, 0x4B, 0x72, 0x49, 0x8A, 0x4F, 0x8F,
		0x51, 0x73, 0x6B, 0x7A, 0x5D, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8A, 0x8F, 0x8F,
		0xA3, 0x80, 0xBB, 0x80, 0xA3, 0x91, 0xBA, 0x93,
		0xCC, 0x80, 0xD4, 0x80, 0xCB, 0x92, 0xD4, 0x90,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x3F, 0x8C, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x49, 0x91, 0x49, 0x95, 0x49, 0x9A, 0x49, 0xA0,
		0x49, 0x9A, 0x4F, 0xA0, 0x49, 0xA8, 0x4E, 0xB1,
		0x5C, 0xA3, 0x6E, 0xA7, 0x5A, 0xBA, 0x72, 0xB7,
		0x85, 0xAD, 0x93, 0xA5, 0x88, 0xBD, 0x95, 0xB6,
		0xA3, 0xA3, 0xBA, 0xA7, 0xA4, 0xB6, 0xBA, 0xBA,
		0xCB, 0xA5, 0xD4, 0xA1, 0xCC, 0xB9, 0xD4, 0xB2,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x4E, 0xC2, 0x49, 0xC3, 0x4D, 0xD9,
		0x5A, 0xD3, 0x72, 0xDA, 0x60, 0xE2, 0x75, 0xEB,
		0x8D, 0xE9, 0x9D, 0xD8, 0x8F, 0xF7, 0x9E, 0xE9,
		0xAB, 0xD0, 0xB9, 0xCC, 0xAB, 0xE0, 0xB6, 0xD8,
		0xCB, 0xCB, 0xD4, 0xC3, 0xC5, 0xD6, 0xD4, 0xD4,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCC,
		0x49, 0xD3, 0x50, 0xEA, 0x49, 0xE6, 0x57, 0xF0,
		0x66, 0xEA, 0x79, 0xEF, 0x6A, 0xF1, 0x7C, 0xF1,
		0x8F, 0xF4, 0x9C, 0xEE, 0x8E, 0xF2, 0x9B, 0xF1,
		0xAA, 0xE9, 0xB6, 0xE4, 0xA8, 0xF1, 0xB4, 0xED,
		0xC1, 0xE2, 0xCF, 0xDF, 0xBE, 0xE8, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD9, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0xF1, 0x5E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x6E, 0xF1, 0x7E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xF1, 0x99, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xF1, 0xB0, 0xEF, 0x00, 0x00, 0x00, 0x00,
		0xB9, 0xEA, 0xC1, 0xE6, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0x13, 0x54, 0x0F, 0x46, 0x16, 0x4D, 0x13,
		0x5D, 0x0D, 0x60, 0x0D, 0x55, 0x0F, 0x60, 0x0D,
		0x5E, 0x0D, 0x5C, 0x0D, 0x60, 0x0D, 0x5E, 0x0D,
		0x63, 0x0D, 0x71, 0x0D, 0x5F, 0x0D, 0x6F, 0x0E,
		0x80, 0x0D, 0x8F, 0x0D, 0x80, 0x0E, 0x91, 0x0E,
		0x9E, 0x0D, 0xAE, 0x0D, 0xA2, 0x0E, 0xB5, 0x15,
		0xB5, 0x27, 0xB5, 0x3F, 0xB5, 0x37, 0xB5, 0x49,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x3D, 0x1A, 0x45, 0x17, 0x34, 0x1F, 0x3B, 0x1C,
		0x4D, 0x13, 0x57, 0x0E, 0x43, 0x18, 0x4C, 0x12,
		0x61, 0x0D, 0x5D, 0x0C, 0x58, 0x0C, 0x60, 0x0F,
		0x5B, 0x0C, 0x6D, 0x0E, 0x5D, 0x09, 0x69, 0x0D,
		0x80, 0x11, 0x93, 0x12, 0x80, 0x13, 0x96, 0x16,
		0xA6, 0x13, 0xB3, 0x2B, 0xAF, 0x18, 0xB3, 0x3E,
		0xB5, 0x44, 0xB5, 0x52, 0xB5, 0x50, 0xB5, 0x59,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x29, 0x24, 0x2F, 0x21, 0x25, 0x32, 0x25, 0x2B,
		0x37, 0x1D, 0x3F, 0x16, 0x29, 0x24, 0x30, 0x1E,
		0x4A, 0x0D, 0x5C, 0x0C, 0x38, 0x12, 0x55, 0x24,
		0x5E, 0x0D, 0x66, 0x19, 0x6C, 0x34, 0x6C, 0x42,
		0x80, 0x21, 0x98, 0x25, 0x80, 0x45, 0x94, 0x47,
		0xAC, 0x37, 0xB3, 0x50, 0xAB, 0x50, 0xB3, 0x5B,
		0xB5, 0x59, 0xB5, 0x60, 0xB5, 0x62, 0xB5, 0x67,
		0xB5, 0x65, 0xBA, 0x65, 0xB5, 0x6A, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x25, 0x43, 0x25, 0x3E, 0x25, 0x52, 0x25, 0x50,
		0x25, 0x38, 0x24, 0x2D, 0x25, 0x4D, 0x25, 0x49,
		0x23, 0x1E, 0x42, 0x2C, 0x23, 0x41, 0x34, 0x44,
		0x68, 0x4D, 0x71, 0x4F, 0x58, 0x55, 0x74, 0x6E,
		0x80, 0x55, 0x94, 0x5E, 0x80, 0x74, 0x93, 0x71,
		0xAA, 0x61, 0xB2, 0x67, 0xA9, 0x6F, 0xB2, 0x70,
		0xB5, 0x6A, 0xB5, 0x6C, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6F, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x62, 0x25, 0x62, 0x25, 0x79, 0x25, 0x7D,
		0x25, 0x62, 0x28, 0x63, 0x25, 0x80, 0x2C, 0x86,
		0x2C, 0x64, 0x3F, 0x6A, 0x3A, 0x8C, 0x4D, 0x90,
		0x5A, 0x73, 0x70, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x92, 0x80, 0x82, 0x96, 0x92, 0x92,
		0xA8, 0x80, 0xB2, 0x80, 0xA8, 0x94, 0xB2, 0x90,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x90, 0x25, 0x96, 0x25, 0xA0, 0x25, 0xA5,
		0x25, 0x9C, 0x2E, 0x9F, 0x25, 0xAC, 0x2E, 0xB0,
		0x3D, 0xA0, 0x4D, 0xA1, 0x3D, 0xB2, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA4, 0x5D, 0xB5, 0x72, 0xBB,
		0x87, 0xAB, 0x94, 0xA4, 0x8B, 0xC6, 0x98, 0xBB,
		0xA7, 0xA7, 0xB2, 0xA1, 0xA5, 0xB8, 0xB2, 0xB2,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAC, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x2F, 0xC0, 0x2E, 0xC3, 0x3C, 0xC3,
		0x3F, 0xC0, 0x4F, 0xC0, 0x49, 0xC3, 0x5A, 0xC3,
		0x63, 0xC1, 0x77, 0xC2, 0x6B, 0xC3, 0x7B, 0xC3,
		0x8B, 0xC3, 0x97, 0xC2, 0x8B, 0xC3, 0x95, 0xC3,
		0xA3, 0xC1, 0xB1, 0xC1, 0x9F, 0xC3, 0xAA, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x50, 0xC3,
		0x54, 0xC3, 0x62, 0xC3, 0x5C, 0xC3, 0x68, 0xC3,
		0x71, 0xC3, 0x7D, 0xC3, 0x74, 0xC3, 0x7F, 0xC3,
		0x8B, 0xC3, 0x93, 0xC3, 0x8B, 0xC3, 0x91, 0xC3,
		0x9C, 0xC3, 0xA5, 0xC3, 0x99, 0xC3, 0xA1, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA8, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x57, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x62, 0xC3, 0x6D, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC3, 0x81, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x8B, 0xC3, 0x91, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xC3, 0x9E, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC3, 0xAB, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x8A, 0x4F,
		0x91, 0x4F, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x6F, 0x5C,
		0x74, 0x5A, 0x76, 0x59, 0x75, 0x59, 0x74, 0x5A,
		0x80, 0x54, 0x8D, 0x4E, 0x80, 0x54, 0x90, 0x51,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x57, 0x67,
		0x63, 0x62, 0x68, 0x5F, 0x5A, 0x66, 0x60, 0x66,
		0x6F, 0x5C, 0x75, 0x5C, 0x68, 0x67, 0x75, 0x69,
		0x80, 0x5D, 0x90, 0x65, 0x80, 0x6E, 0x90, 0x74,
		0x92, 0x72, 0x99, 0x73, 0x91, 0x78, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x31, 0x7A, 0x33, 0x7D,
		0x4A, 0x6E, 0x4F, 0x6F, 0x37, 0x80, 0x2F, 0x86,
		0x56, 0x72, 0x60, 0x75, 0x25, 0x90, 0x4D, 0x90,
		0x6B, 0x79, 0x74, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x90, 0x80, 0x82, 0x91, 0x90, 0x90,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x05, 0x96, 0x28, 0x96, 0x3F, 0x97, 0x47, 0x97,
		0x3B, 0x96, 0x47, 0x96, 0x4F, 0x97, 0x57, 0x97,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x76, 0x96, 0x70, 0x98, 0x7A, 0x98,
		0x83, 0x96, 0x8C, 0x96, 0x83, 0x97, 0x89, 0x97,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_cem_table1 = {
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x5B, 0x52, 0x64, 0x52,
		0x70, 0x50, 0x78, 0x50, 0x6D, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x98, 0x50, 0x92, 0x52, 0x9B, 0x52,
		0xA0, 0x50, 0xA8, 0x50, 0xA4, 0x52, 0xAD, 0x52,
		0xB0, 0x50, 0xB8, 0x50, 0xB6, 0x52, 0xBF, 0x52,
		0xBF, 0x50, 0x00, 0x00, 0xC8, 0x52, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x5B, 0x5B, 0x5D, 0x52, 0x5B, 0x64, 0x5B, 0x5B,
		0x69, 0x52, 0x74, 0x52, 0x61, 0x52, 0x70, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x96, 0x52, 0xA2, 0x52, 0x9E, 0x52, 0xAD, 0x52,
		0xAD, 0x52, 0xB9, 0x52, 0xBC, 0x52, 0xCC, 0x52,
		0xC4, 0x52, 0xCF, 0x52, 0xDB, 0x52, 0xEA, 0x52,
		0xDA, 0x52, 0x00, 0x00, 0xEB, 0x57, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x5B, 0x6D, 0x5B, 0x67, 0x5B, 0x76, 0x5B, 0x73,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xDF, 0x60, 0xEB, 0x61, 0xDF, 0x70, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x5B, 0x80, 0x5B, 0x80, 0x5B, 0x89, 0x5B, 0x8C,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xDF, 0x80, 0xEB, 0x80, 0xDF, 0x8F, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x5B, 0x92, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xDF, 0x9F, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBC,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xD0, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9E, 0xCB, 0xA9, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB6, 0x5B, 0xC8, 0x5B, 0xBF, 0x5B, 0xD4,
		0x60, 0xDF, 0x70, 0xDF, 0x62, 0xE7, 0x72, 0xE0,
		0x80, 0xD9, 0x8D, 0xD2, 0x80, 0xD9, 0x8B, 0xD3,
		0x99, 0xCD, 0xA4, 0xC8, 0x96, 0xCE, 0x9F, 0xCA,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA8, 0xC6, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xC7, 0x5B, 0xE0, 0x00, 0x00, 0x00, 0x00,
		0x66, 0xE5, 0x74, 0xDF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xD9, 0x8A, 0xD4, 0x00, 0x00, 0x00, 0x00,
		0x94, 0xCF, 0x9C, 0xCB, 0x00, 0x00, 0x00, 0x00,
		0xA4, 0xC8, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x37, 0x37, 0x37, 0x2D, 0x37, 0x40, 0x37, 0x37,
		0x3B, 0x24, 0x46, 0x24, 0x37, 0x2B, 0x3E, 0x24,
		0x52, 0x24, 0x5D, 0x24, 0x4B, 0x24, 0x58, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8B, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x96, 0x24, 0xA2, 0x24, 0x9A, 0x24, 0xA7, 0x24,
		0xAD, 0x24, 0xB9, 0x24, 0xB4, 0x24, 0xC1, 0x24,
		0xC4, 0x24, 0xC7, 0x2E, 0xC7, 0x2C, 0xC7, 0x38,
		0xC7, 0x37, 0x00, 0x00, 0xC7, 0x40, 0x00, 0x00,
		0x37, 0x49, 0x37, 0x42, 0x37, 0x52, 0x37, 0x4C,
		0x37, 0x37, 0x37, 0x29, 0x37, 0x43, 0x37, 0x37,
		0x43, 0x24, 0x52, 0x24, 0x40, 0x30, 0x50, 0x30,
		0x61, 0x24, 0x70, 0x24, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x24, 0x8F, 0x24, 0x80, 0x30, 0x8F, 0x30,
		0x9E, 0x24, 0xAD, 0x24, 0x9F, 0x30, 0xAF, 0x30,
		0xBC, 0x24, 0xC7, 0x29, 0xBF, 0x30, 0xC7, 0x38,
		0xC7, 0x38, 0xC7, 0x42, 0xC7, 0x44, 0xC7, 0x4C,
		0xC7, 0x49, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x37, 0x5B, 0x37, 0x56, 0x37, 0x64, 0x37, 0x61,
		0x37, 0x4F, 0x37, 0x46, 0x37, 0x5B, 0x37, 0x54,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xC7, 0x46, 0xBF, 0x50, 0xC7, 0x54,
		0xC7, 0x50, 0xC7, 0x57, 0xC7, 0x5C, 0xC7, 0x61,
		0xC7, 0x5B, 0x00, 0x00, 0xC7, 0x64, 0x00, 0x00,
		0x37, 0x6D, 0x37, 0x6B, 0x37, 0x76, 0x37, 0x75,
		0x37, 0x67, 0x37, 0x63, 0x37, 0x73, 0x37, 0x71,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xC7, 0x63, 0xBF, 0x70, 0xC7, 0x71,
		0xC7, 0x68, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x89, 0x37, 0x8A,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x8C, 0x37, 0x8E,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xC7, 0x80, 0xBF, 0x8F, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x92, 0x37, 0x94, 0x37, 0x9B, 0x37, 0x9E,
		0x37, 0x98, 0x37, 0x9C, 0x37, 0xA4, 0x37, 0xAB,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xC7, 0x9C, 0xBF, 0xAF, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA4, 0x37, 0xA9, 0x37, 0xAD, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x40, 0xBF, 0x50, 0xBF, 0x40, 0xCF, 0x50, 0xCF,
		0x60, 0xBF, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xC7, 0xB9, 0xBF, 0xCF, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x3F, 0xDA,
		0x43, 0xDA, 0x52, 0xDA, 0x4C, 0xDA, 0x59, 0xDA,
		0x62, 0xDA, 0x71, 0xDA, 0x66, 0xDA, 0x73, 0xDA,
		0x80, 0xDA, 0x8F, 0xDA, 0x80, 0xDA, 0x8C, 0xDA,
		0x9E, 0xDA, 0xAD, 0xDA, 0x99, 0xDA, 0xA6, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB3, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x47, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x52, 0xDA, 0x5E, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x69, 0xDA, 0x74, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xDA, 0x8B, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x96, 0xDA, 0xA2, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xAD, 0xDA, 0xB9, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x3E, 0x47, 0x44, 0x44,
		0x4F, 0x3F, 0x55, 0x3C, 0x4A, 0x41, 0x51, 0x3E,
		0x5C, 0x38, 0x63, 0x35, 0x58, 0x3A, 0x60, 0x36,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8B, 0x22, 0x80, 0x27, 0x8D, 0x21,
		0x99, 0x1B, 0xA3, 0x21, 0x9D, 0x19, 0xA3, 0x2D,
		0xA3, 0x38, 0xA8, 0x40, 0xA3, 0x41, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x38, 0x4A, 0x3E, 0x47, 0x30, 0x4E, 0x36, 0x4B,
		0x44, 0x44, 0x4B, 0x41, 0x3C, 0x48, 0x44, 0x44,
		0x53, 0x3D, 0x5C, 0x38, 0x4D, 0x40, 0x56, 0x3B,
		0x66, 0x33, 0x72, 0x2E, 0x62, 0x36, 0x70, 0x30,
		0x80, 0x27, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xA3, 0x38, 0x9F, 0x30, 0xA3, 0x44,
		0xA3, 0x4A, 0xA8, 0x50, 0xA3, 0x53, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x26, 0x53, 0x2C, 0x50, 0x19, 0x59, 0x1F, 0x56,
		0x33, 0x4C, 0x3B, 0x48, 0x26, 0x53, 0x30, 0x50,
		0x44, 0x44, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xA3, 0x50, 0x9F, 0x50, 0xA3, 0x5C,
		0xA3, 0x5C, 0xA8, 0x60, 0xA3, 0x65, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x13, 0x64, 0x13, 0x61, 0x13, 0x72, 0x13, 0x70,
		0x20, 0x60, 0x30, 0x60, 0x20, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xA3, 0x68, 0x9F, 0x70, 0xA3, 0x74,
		0xA3, 0x6E, 0xA8, 0x70, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x80, 0x13, 0x80, 0x13, 0x8D, 0x13, 0x8F,
		0x20, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xA3, 0x80, 0x9F, 0x8F, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x9B, 0x13, 0x9E, 0x13, 0xA8, 0x17, 0xAC,
		0x20, 0x9F, 0x30, 0x9F, 0x26, 0xAC, 0x35, 0xAC,
		0x40, 0x9F, 0x50, 0x9F, 0x44, 0xAC, 0x53, 0xAC,
		0x60, 0x9F, 0x70, 0x9F, 0x62, 0xAC, 0x71, 0xAC,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8E, 0xAC,
		0x9F, 0x9F, 0xA3, 0x97, 0x9D, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x26, 0xAC, 0x31, 0xAC, 0x38, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x65, 0xAC,
		0x69, 0xAC, 0x74, 0xAC, 0x6E, 0xAC, 0x77, 0xAC,
		0x80, 0xAC, 0x8B, 0xAC, 0x80, 0xAC, 0x88, 0xAC,
		0x96, 0xAC, 0xA1, 0xAC, 0x91, 0xAC, 0x9A, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x6D, 0x6D, 0x74, 0x69, 0x6D, 0x76, 0x70, 0x70,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x70, 0x8F, 0x70,
		0x96, 0x69, 0xA2, 0x69, 0x9F, 0x70, 0xAF, 0x70,
		0xAD, 0x69, 0xB9, 0x69, 0xBF, 0x70, 0xCF, 0x70,
		0xC4, 0x69, 0xCF, 0x69, 0xDF, 0x70, 0xEF, 0x70,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x6D, 0x80, 0x70, 0x80, 0x6D, 0x89, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x70, 0xA0, 0x6D, 0x9B, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8C, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA4, 0x73, 0xB2, 0x6D, 0xAD, 0x76, 0xB1,
		0x80, 0xAC, 0x89, 0xA7, 0x80, 0xAC, 0x88, 0xA8,
		0x92, 0xA4, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6E, 0xB5, 0x77, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x4C, 0x3B, 0x55, 0x3B, 0x49, 0x40, 0x4F, 0x3B,
		0x5D, 0x3B, 0x66, 0x3B, 0x58, 0x3B, 0x62, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x89, 0x3B,
		0x91, 0x3B, 0x99, 0x3B, 0x93, 0x3B, 0x9D, 0x3B,
		0xA2, 0x3B, 0xAA, 0x3B, 0xA7, 0x3B, 0xB0, 0x3B,
		0xB3, 0x3B, 0xBB, 0x3B, 0xBA, 0x3B, 0xC4, 0x3B,
		0xC3, 0x3B, 0x00, 0x00, 0xCD, 0x3B, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x49, 0x49, 0x49, 0x3E, 0x49, 0x52, 0x49, 0x49,
		0x52, 0x3B, 0x5D, 0x3B, 0x49, 0x3C, 0x56, 0x3B,
		0x69, 0x3B, 0x74, 0x3B, 0x64, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8D, 0x3B,
		0x96, 0x3B, 0xA2, 0x3B, 0x9B, 0x3B, 0xA9, 0x3B,
		0xAD, 0x3B, 0xB9, 0x3B, 0xB6, 0x3B, 0xC4, 0x3B,
		0xC4, 0x3B, 0xCF, 0x3B, 0xD2, 0x3B, 0xD9, 0x3F,
		0xD9, 0x3C, 0x00, 0x00, 0xD9, 0x47, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x49, 0x5B, 0x49, 0x54, 0x49, 0x64, 0x49, 0x5F,
		0x49, 0x49, 0x50, 0x40, 0x49, 0x57, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xCF, 0x40, 0xBF, 0x50, 0xCF, 0x50,
		0xD9, 0x44, 0xD9, 0x4C, 0xD9, 0x53, 0xD9, 0x59,
		0xD9, 0x52, 0x00, 0x00, 0xD9, 0x5E, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x49, 0x6D, 0x49, 0x6A, 0x49, 0x76, 0x49, 0x75,
		0x49, 0x64, 0x50, 0x60, 0x49, 0x72, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xD9, 0x62, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x49, 0x80, 0x49, 0x80, 0x49, 0x89, 0x49, 0x8A,
		0x49, 0x80, 0x50, 0x80, 0x49, 0x8D, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x49, 0x92, 0x49, 0x95, 0x49, 0x9B, 0x49, 0xA0,
		0x49, 0x9B, 0x50, 0x9F, 0x49, 0xA8, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x50, 0xBF, 0x49, 0xC3, 0x50, 0xD0,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xCF, 0xBF, 0xBF, 0xCF, 0xCF, 0xCF,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCB,
		0x49, 0xD1, 0x50, 0xE0, 0x49, 0xDF, 0x50, 0xEF,
		0x60, 0xDF, 0x70, 0xDF, 0x60, 0xEF, 0x70, 0xEF,
		0x80, 0xDF, 0x8F, 0xDF, 0x80, 0xEF, 0x8F, 0xEF,
		0x9F, 0xDF, 0xAF, 0xDF, 0x9F, 0xEF, 0xAF, 0xEF,
		0xBF, 0xDF, 0xCF, 0xDF, 0xBC, 0xE9, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD6, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xEB, 0x55, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xF1, 0x71, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xF1, 0x8E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x9C, 0xF1, 0xAA, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xB6, 0xEC, 0xC0, 0xE7, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x26, 0x26, 0x2E, 0x22, 0x25, 0x30, 0x26, 0x26,
		0x36, 0x1E, 0x40, 0x19, 0x2F, 0x21, 0x39, 0x1D,
		0x4A, 0x14, 0x55, 0x0F, 0x44, 0x17, 0x50, 0x11,
		0x63, 0x0D, 0x71, 0x0D, 0x60, 0x10, 0x70, 0x10,
		0x80, 0x0D, 0x8E, 0x0D, 0x80, 0x10, 0x8F, 0x10,
		0x9C, 0x0D, 0xAA, 0x0D, 0x9F, 0x10, 0xAF, 0x10,
		0xB5, 0x14, 0xB5, 0x2A, 0xB5, 0x22, 0xB5, 0x34,
		0xB5, 0x38, 0xB8, 0x40, 0xB5, 0x41, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x25, 0x3C, 0x25, 0x32, 0x25, 0x47, 0x25, 0x3F,
		0x26, 0x26, 0x30, 0x21, 0x25, 0x34, 0x30, 0x30,
		0x40, 0x20, 0x50, 0x20, 0x40, 0x30, 0x50, 0x30,
		0x60, 0x20, 0x70, 0x20, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x20, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xAF, 0x20, 0x9F, 0x30, 0xAF, 0x30,
		0xB5, 0x2F, 0xB5, 0x3F, 0xB5, 0x3C, 0xB5, 0x4A,
		0xB5, 0x4A, 0xB8, 0x50, 0xB5, 0x53, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x25, 0x52, 0x25, 0x4C, 0x25, 0x5E, 0x25, 0x59,
		0x25, 0x43, 0x30, 0x40, 0x25, 0x52, 0x30, 0x50,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xB5, 0x4A, 0xB5, 0x55, 0xB5, 0x57, 0xB5, 0x5F,
		0xB5, 0x5C, 0xB8, 0x60, 0xB5, 0x65, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x25, 0x69, 0x25, 0x66, 0x25, 0x74, 0x25, 0x73,
		0x25, 0x61, 0x30, 0x60, 0x25, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xB5, 0x65, 0xB5, 0x6A, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6E, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x25, 0x8B, 0x25, 0x8C,
		0x25, 0x80, 0x30, 0x80, 0x25, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x96, 0x25, 0x99, 0x25, 0xA1, 0x25, 0xA6,
		0x25, 0x9E, 0x30, 0x9F, 0x25, 0xAD, 0x30, 0xAF,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAD, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x30, 0xBF, 0x2E, 0xC3, 0x3C, 0xC3,
		0x40, 0xBF, 0x50, 0xBF, 0x49, 0xC3, 0x57, 0xC3,
		0x60, 0xBF, 0x70, 0xBF, 0x65, 0xC3, 0x72, 0xC3,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xC3, 0x8D, 0xC3,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9B, 0xC3, 0xA8, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x4F, 0xC3,
		0x52, 0xC3, 0x5E, 0xC3, 0x59, 0xC3, 0x63, 0xC3,
		0x69, 0xC3, 0x74, 0xC3, 0x6C, 0xC3, 0x76, 0xC3,
		0x80, 0xC3, 0x8B, 0xC3, 0x80, 0xC3, 0x89, 0xC3,
		0x96, 0xC3, 0xA1, 0xC3, 0x93, 0xC3, 0x9D, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA6, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x55, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x5D, 0xC3, 0x66, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x6F, 0xC3, 0x77, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xC3, 0x88, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xC3, 0x99, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xC3, 0xAA, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x89, 0x4F,
		0x91, 0x4C, 0x98, 0x50, 0x91, 0x54, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x57, 0x67,
		0x60, 0x60, 0x68, 0x60, 0x5C, 0x65, 0x62, 0x62,
		0x6E, 0x5D, 0x76, 0x59, 0x6A, 0x5F, 0x73, 0x5A,
		0x80, 0x54, 0x8C, 0x4E, 0x80, 0x54, 0x8F, 0x50,
		0x91, 0x5D, 0x98, 0x60, 0x91, 0x65, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x47, 0x6F, 0x38, 0x77, 0x3A, 0x76,
		0x4A, 0x6E, 0x4E, 0x6C, 0x3D, 0x74, 0x40, 0x73,
		0x53, 0x69, 0x5A, 0x66, 0x44, 0x71, 0x50, 0x70,
		0x62, 0x62, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x91, 0x6E, 0x98, 0x70, 0x91, 0x77, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x06, 0x8F, 0x10, 0x8F,
		0x25, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x27, 0x96, 0x32, 0x96, 0x40, 0x98, 0x48, 0x98,
		0x3D, 0x96, 0x48, 0x96, 0x50, 0x98, 0x58, 0x98,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x74, 0x96, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x96, 0x8B, 0x96, 0x80, 0x98, 0x88, 0x98,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_pltm_table = {
		/* pltm - H */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - V */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - P */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - F */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_wdr_table = { 0 }
};
struct isp_cfg_pt gc5024_isp_cfg = {
	.isp_test_settings = &gc5024_isp_test_settings,
	.isp_3a_settings = &gc5024_isp_3a_settings,
	.isp_tunning_settings = &gc5024_isp_tuning_settings,
	.isp_iso_settings = &gc5024_isp_iso_settings
};

#endif /* end of _GC5024_H_V100_ */
