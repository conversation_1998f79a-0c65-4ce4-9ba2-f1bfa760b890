
#include "no_effect_control_delegate.h"

#include <gtest/gtest.h>

using testing::Test;

namespace v4l2_camera_hal {

TEST(NoEffectControlDelegateTest, DefaultGet) {
  int32_t value = 12;
  NoEffectControlDelegate<int32_t> control(value);
  int32_t actual = 0;
  ASSERT_EQ(control.GetValue(&actual), 0);
  EXPECT_EQ(actual, value);
}

TEST(NoEffectControlDelegateTest, GetAndSet) {
  int32_t value = 12;
  NoEffectControlDelegate<int32_t> control(value);
  int32_t new_value = 13;
  ASSERT_EQ(control.SetValue(new_value), 0);
  int32_t actual = 0;
  ASSERT_EQ(control.GetValue(&actual), 0);
  EXPECT_EQ(actual, new_value);
}

}  // namespace v4l2_camera_hal
