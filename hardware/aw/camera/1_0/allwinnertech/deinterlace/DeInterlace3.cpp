/*
 * =====================================================================================
 *
 *       Filename:  DeInterlace3.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2020年03月10日 09时56分59秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  jilinglin
 *        Company:  allwinnertech.com
 *
 * =====================================================================================
 */

#include "DeInterlace3.h"
#include <sys/mman.h>
#include <cutils/log.h>
#include <fcntl.h>
#include <utils/Errors.h>
#include <string.h>

namespace android {
DeInterlace3::DeInterlace3():
    mFd(-1),
    mFilmDetect(0),
    mTnrOpen(0),
    mContrastOpen(0),
    mCropOpen(0),
    mMode(DI_MODE_INVALID){
    pthread_mutex_init(&mDIMutex, NULL);

}

DeInterlace3::~DeInterlace3(){
    pthread_mutex_destroy(&mDIMutex);

}

void DeInterlace3::init(void) {
    if (mFd >= 0) {
        ALOGE("DiProcess is already init");
        return;
    }
    openDevice(DI_DEVICE_NAME);
}

int DeInterlace3::openDevice(char* path){
    if(path == nullptr) {
        ALOGE("device path is null");
        return -1;
    }
    pthread_mutex_lock(&mDIMutex);
    mFd = open(path, O_RDWR, 0);
    pthread_mutex_unlock(&mDIMutex);
    if (mFd < 0) {
        ALOGE("open %s failed err: %s",path, strerror(errno));
        return -1;
    }

    ALOGD("open %s OK",path);
    return 0;
}

int DeInterlace3::closeDevice(void){
    if(mFd >= 0){
        pthread_mutex_lock(&mDIMutex);
        close(mFd);
        pthread_mutex_unlock(&mDIMutex);
        mFd = -1;
    }else {
        ALOGW("the fd is null,when close device.");
        return -1;
    }
    return 0;
}

void DeInterlace3::release(void){
    closeDevice();
}


void DeInterlace3::reset(void){
    closeDevice();
    openDevice(DI_DEVICE_NAME);
}


int DeInterlace3::process(InputData* input,OutputData* output){
    struct di_size size_in;
    struct di_rect rect;
    struct di_dit_mode dit_mode;
    struct di_fmd_enable fmd_en;
    struct di_tnr_mode tnr_mode;
    struct di_demo_crop_arg demo_arg;

    if(checkFormat(input,output,mMode) < 0){
        ALOGE("format not support");
        return -1;
    }
    if(mFd < 0){
        ALOGE("device no open!");
        return -1;
    }
    pthread_mutex_lock(&mDIMutex);
    if (ioctl(mFd, DI_IOC_RESET, 0) > 0) {
        ALOGE("DI_IOC_RESET failed\n");
        return -1;
    }

    size_in.width = input->input[0].mWidth;
    size_in.height = input->input[0].mHeight;
    if (ioctl(mFd, DI_IOC_SET_VIDEO_SIZE, &size_in) > 0) {
        ALOGE("DI_IOC_SET_VIDEO_SIZE failed\n");
        return -1;
    }

    if(mCropOpen){
        rect.left = mCrop.left;
        rect.top = mCrop.top;
        rect.right = mCrop.right;
        rect.bottom = mCrop.bottom;
    }else{
        rect.left = 0;
        rect.top = 0;
        rect.right = size_in.width;
        rect.bottom = size_in.height;
    }
    if (ioctl(mFd, DI_IOC_SET_VIDEO_CROP, &rect) > 0) {
        ALOGE("DI_IOC_SET_VIDEO_CROP failed\n");
        return -1;
    }
    switch(mMode) {
         case DI_MODE_30HZ:
            dit_mode.intp_mode = DI_DIT_INTP_MODE_MOTION;
            dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
            break;
         case DI_MODE_60HZ:
            dit_mode.intp_mode = DI_DIT_INTP_MODE_MOTION;
            dit_mode.out_frame_mode = DI_DIT_OUT_2FRAME;
            break;
         case DI_MODE_BOB:
            dit_mode.intp_mode = DI_DIT_INTP_MODE_BOB;
            dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
            break;
         case DI_MODE_WEAVE:
            dit_mode.intp_mode = DI_DIT_INTP_MODE_WEAVE;
            dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
            break;
         default:
            dit_mode.intp_mode = DI_DIT_INTP_MODE_INVALID;
            dit_mode.out_frame_mode = DI_DIT_OUT_0FRAME;
    }

    if (ioctl(mFd, DI_IOC_SET_DIT_MODE, &dit_mode) > 0) {
        ALOGE("DI_IOC_SET_DIT_MODE failed,error:%s\n",strerror(errno));
        return -1;
    }

    if(mFilmDetect) {
        fmd_en.en = 1;
        if (ioctl(mFd, DI_IOC_SET_FMD_ENABLE, &fmd_en) > 0) {
            ALOGE("DI_IOC_SET_FMD_ENABLE failed\n");
            return -1;
        }
    }

    if(mTnrOpen) {
        tnr_mode.mode = DI_TNR_MODE_FIX;
        tnr_mode.level = DI_TNR_LEVEL_HIGH;
        if (ioctl(mFd, DI_IOC_SET_FMD_ENABLE, &fmd_en) > 0) {
            ALOGE("DI_IOC_SET_FMD_ENABLE failed\n");
            return -1;
        }
   }

    if(mContrastOpen){
        if(mTnrOpen) {
            demo_arg.tnr_demo.left = mContrast.left;
            demo_arg.tnr_demo.top = mContrast.top;
            demo_arg.tnr_demo.right = mContrast.right;
            demo_arg.tnr_demo.bottom = mContrast.bottom;
        }
        demo_arg.dit_demo.left = mContrast.left;
        demo_arg.dit_demo.top = mContrast.top;
        demo_arg.dit_demo.right = mContrast.right;
        demo_arg.dit_demo.bottom = mContrast.bottom;
        if (ioctl(mFd, DI_IOC_SET_DEMO_CROP, &demo_arg) < 0)
        {
            ALOGE("DI_IOC_SET_DEMO_CROP failed.");
            return -1;
        }
    }

    if (ioctl(mFd, DI_IOC_CHECK_PARA, 0) > 0)
    {
        ALOGE("DI_IOC_CHECK_PARA\n");
        return -1;
    }

    configBufAndStart(input,output);
    pthread_mutex_unlock(&mDIMutex);
    return 0;
}

int DeInterlace3::configBufAndStart(InputData* input,OutputData* output) {

    DiParaT3 di_para;
    memset(&di_para,0x0,sizeof(DiParaT3));

    switch(mMode){
        case DI_MODE_BOB:
            setBufAddr(&di_para.in_fb0,input->input[0]);
            setBufAddr(&di_para.out_dit_fb0,output->output[0]);
            break;
        case DI_MODE_30HZ:
            setBufAddr(&di_para.in_fb0,input->input[0]);
            setBufAddr(&di_para.in_fb1,input->input[1]);
            setBufAddr(&di_para.out_dit_fb0,output->output[0]);
            setBufAddr(&di_para.out_tnr_fb0,output->output[1]);
            break;
       case DI_MODE_60HZ:
            setBufAddr(&di_para.in_fb0,input->input[0]);
            setBufAddr(&di_para.in_fb1,input->input[1]);
            setBufAddr(&di_para.in_fb2,input->input[2]);
            setBufAddr(&di_para.out_dit_fb0,output->output[0]);
            setBufAddr(&di_para.out_dit_fb1,output->output[1]);
            setBufAddr(&di_para.out_tnr_fb0,output->output[2]);
            break;
       case DI_MODE_TNR:
            setBufAddr(&di_para.in_fb0,input->input[0]);
            setBufAddr(&di_para.in_fb1,input->input[1]);
            setBufAddr(&di_para.out_tnr_fb0,output->output[0]);
            break;
        default:
            ALOGE("di mode not support!");
    }

    di_para.is_interlace = 1;
    di_para.is_pulldown = 0;
    di_para.top_field_first = 1;
    di_para.base_field = 1;

    if (ioctl(mFd, DI_IOC_PROCESS_FB, (unsigned long)&di_para) < 0)
    {
        ALOGE("di process failed.%s",strerror(errno));
        return -1;
    }
    ALOGD("ji**********di ok");
    return 0;

}

int DeInterlace3::setBufAddr(struct di_fb* fb, DIFrame frame)
{

    //note: need to support more foramt
    fb->format = frame.mPixelFormat;
    fb->size.width = frame.mWidth;
    fb->size.height = frame.mHeight;
    fb->buf.ystride = ALIGN(frame.mAlignSize,frame.mWidth);

    if(frame.mBufFd < 0) {
        fb->dma_buf_fd = -1;
        fb->buf.y_addr = frame.mAddrPhy;
        fb->buf.cb_addr = frame.mAddrPhy + ALIGN(frame.mAlignSize,frame.mWidth) * ALIGN(frame.mAlignSize,frame.mHeight);

    }else {
        fb->dma_buf_fd = frame.mBufFd;
        fb->buf.y_addr = 0;
        fb->buf.cb_addr = ALIGN(frame.mAlignSize,frame.mWidth) * ALIGN(frame.mAlignSize,frame.mHeight);
    }

    switch(fb->format) {
        case DRM_FORMAT_YUV420:
        case DRM_FORMAT_YVU420:
            fb->buf.cstride = frame.mWidth/2;
            fb->buf.cr_addr = fb->buf.cb_addr + (ALIGN(frame.mAlignSize,frame.mWidth) * ALIGN(frame.mAlignSize,frame.mHeight))/4;
            break;
        case DRM_FORMAT_YUV422:
        case DRM_FORMAT_YVU422:
            fb->buf.cstride = frame.mWidth;
            fb->buf.cr_addr = fb->buf.cb_addr + (ALIGN(frame.mAlignSize,frame.mWidth) * ALIGN(frame.mAlignSize,frame.mHeight))/2;
            break;
        case DRM_FORMAT_NV12:
        case DRM_FORMAT_NV21:
            fb->buf.cstride = frame.mWidth;
            fb->buf.cr_addr = 0;
            break;
        case DRM_FORMAT_NV16:
        case DRM_FORMAT_NV61:
            fb->buf.cstride = frame.mWidth*2;
            fb->buf.cr_addr = 0;
            break;
        default:
            ALOGW("note: format not support");
    }

    ALOGD("Format:%d width:%u height:%u dma_buf_fd:%d "
        "y_addr:%llx cb_addr:%llx cr_addr:%llx ystride:%u cstride:%u\n",
        fb->format, fb->size.width, fb->size.height, fb->dma_buf_fd,
        fb->buf.y_addr, fb->buf.cb_addr, fb->buf.cr_addr,
        fb->buf.ystride, fb->buf.cstride);
    return 0;
}

int DeInterlace3:: setParameter(void* parm){
    DIParam* pParm = (DIParam*)parm;
    mFilmDetect = pParm->filmDetect;
    mTnrOpen = pParm->tnrOpen;
    mContrastOpen = pParm->contrastOpen;
    mContrast = pParm->contrast;
    mMode = pParm->mode;
    mCropOpen = pParm->cropOpen;
    mCrop = pParm->crop;

    switch(mMode){
        case DI_MODE_30HZ:
        case DI_MODE_60HZ:
           if(mTnrOpen == 0 || mFilmDetect == 0 || mCropOpen == 1){
                ALOGW("mode 30hz we now just support tnr and film detect open.");
                return -1;
           }
           break;
         case DI_MODE_BOB:
            if(mTnrOpen == 1 || mFilmDetect == 1 || mContrastOpen == 1){
                ALOGW("mode 30hz we now just support tnr and film detect close.");
                return -1;
           }
           break;
         case DI_MODE_TNR:
            if(mTnrOpen == 0 || mFilmDetect == 1){
                ALOGW("mode 30hz we now just support tnr open and film detect close.");
                return -1;
           }
           break;
        default:
            ALOGD("di mode invalid!");
            return -1;
    }
    return 0;
}

int DeInterlace3::checkFormat(InputData* input,OutputData* output, DIMode mode){
    int result = 0;
    switch(mode) {
        case DI_MODE_30HZ:
        case DI_MODE_60HZ:
            for(int i=0; i<output->frameNum; i++){
                if(output->output[i].mPixelFormat == DRM_FORMAT_NV12 ||
                    output->output[i].mPixelFormat == DRM_FORMAT_NV21 ||
                    output->output[i].mPixelFormat == DRM_FORMAT_NV16 ||
                    output->output[i].mPixelFormat == DRM_FORMAT_NV61){
                        result = -1;
                        break;
                }
            }
            break;
        case DI_MODE_BOB:
            for(int i=0; i<output->frameNum; i++){
                if(input->input[i].mPixelFormat != output->output[i].mPixelFormat){
                    result = -1;
                    break;
                }
            }
            break;
        default:
            ALOGW("di mode invalid");

    }

    return result;
}

};
