#ifndef __DIPROCESS__H__
#define __DIPROCESS__H__

#include <sys/mman.h>
#include <cutils/log.h>
#include <fcntl.h>
#include <utils/Errors.h>
#include "deinterlace3.h"

namespace android {
//using android::status_t;
typedef struct DTFrameT {
    unsigned long long mAddrPhy;
    int mWidth;
    int mHeight;
    int mAlignSize;
    int mBufFd;
    int mPixelFormat;
    int mTopFeild;
} DIFrame;

class DiProcess{

public:
    /* Constructs DiProcess instance. */
    DiProcess();

    /* Destructs DiProcess instance. */
    ~DiProcess();

public:

    status_t init();
    status_t diProcess(unsigned char *pre_src, unsigned char *now_src, unsigned char *next_src,
                                int src_width, int src_height, int in_format,
                                unsigned char *dst, int dst_width, int dst_height,
                                int out_format, int nField);


    status_t diProcess3(DIFrame PrePrePic, DIFrame PrePic, DIFrame CurPic,
                          DIFrame OutPic, DIFrame OutPic1, DIFrame OutTnr, DIMode Mode);
    status_t release();

private:
    unsigned int changeToDiFormat(int format);
    status_t setBufAddr(di_fb* fb, DIFrame frame);
    status_t setDiMode(int fd, DIMode mode, int width, int height);
private:
    int mDiFd;
    int mDiReqId;

};
};

#endif