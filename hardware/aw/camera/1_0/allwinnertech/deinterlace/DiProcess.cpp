#define LOG_NDEBUG 1

#define LOG_TAG "DiProcess"

#include "DiProcess.h"
#include "deinterlace2.h"
#include "deinterlace3.h"

#include <string.h>

#include <linux/videodev2.h>

#define ALIGN_4K(x) (((x) + (4095)) & ~(4095))
#define ALIGN_32B(x) (((x) + (31)) & ~(31))
#define ALIGN_16B(x) (((x) + (15)) & ~(15))
#define ALIGN_8B(x) (((x) + (7)) & ~(7))

namespace android {

DiProcess::DiProcess()
    :mDiFd(-1)
    ,mDiReqId(-1)
{
    ALOGD("F:%s,L:%d",__FUNCTION__,__LINE__);
}

DiProcess::~DiProcess()
{
    ALOGD("F:%s,L:%d",__FUNCTION__,__LINE__);
    release();
    ALOGD("F:%s ok ,L:%d",__FUNCTION__,__LINE__);
}

status_t DiProcess::init()
{
    int ret = -1;
    if (mDiFd > 0) {
        ALOGE("DiProcess is already init,F:%s,L:%d",__FUNCTION__,__LINE__);
        return INVALID_OPERATION;
    }
    ALOGD("open /dev/deinterlace , F:%s,L:%d",__FUNCTION__,__LINE__);

    mDiFd = open("/dev/deinterlace", O_RDWR, 0);
    if (mDiFd <= 0) {
        ALOGE("open /dev/deinterlace failed,F:%s,L:%d",__FUNCTION__,__LINE__);
        return INVALID_OPERATION;
    }

    ALOGD("open /dev/deinterlace OK, F:%s,L:%d",__FUNCTION__,__LINE__);
    return NO_ERROR;
}

status_t DiProcess::diProcess(unsigned char *pre_src, unsigned char *now_src, unsigned char *next_src,
                                int src_width, int src_height, int in_format,
                                unsigned char *dst, int dst_width, int dst_height,
                                int out_format, int nField)
{
    int ret = -1;
    __di_para_t2 di_para;
    memset(&di_para,0x0,sizeof(__di_para_t2));
    if (mDiFd < 0)
    {
        ALOGE("deinterlace is not init!F:%s,L:%d",__FUNCTION__,__LINE__);
        return NO_INIT;
    }
    if(mDiReqId < 0)
    {
        ret = ioctl(mDiFd, DI_REQUEST, &mDiReqId);
        if(ret > 0)
        {
            ALOGE("DI_REQUEST failed,F:%s,L:%d",__FUNCTION__,__LINE__);
            return INVALID_OPERATION;
        }
        //ALOGD("DI_REQUEST OK, F:%s,L:%d, id:%d",__FUNCTION__,__LINE__,mDiReqId);
    }
    di_para.dma_if = 1;
    __di_pixel_fmt_t di_in_format;
    switch (in_format) {
        case V4L2_PIX_FMT_NV21:
            di_in_format = DI_FORMAT_NV21;
            break;
        case V4L2_PIX_FMT_NV16:
            di_in_format = DI_FORMAT_YUV422_SP_UVUV;
            break;
        case V4L2_PIX_FMT_NV61:
            di_in_format = DI_FORMAT_YUV422_SP_VUVU;
            break;
        default:
            di_in_format = DI_FORMAT_NV21;
            break;
    }

    __di_pixel_fmt_t di_out_format;
    switch (out_format) {
        case V4L2_PIX_FMT_NV21:
            di_out_format = DI_FORMAT_NV21;
            break;
        case V4L2_PIX_FMT_NV16:
            di_out_format = DI_FORMAT_YUV422_SP_UVUV;
            break;
        case V4L2_PIX_FMT_NV61:
            di_out_format = DI_FORMAT_YUV422_SP_VUVU;
            break;
        default:
            di_out_format = DI_FORMAT_NV21;
            break;
    }

    di_para.pre_fb.addr[0] = (unsigned long long)pre_src;
    di_para.pre_fb.addr[1] = (unsigned long long)(pre_src + ALIGN_16B(src_width)*src_height);
    di_para.pre_fb.addr[2] = 0x0;
    di_para.pre_fb.size.width = src_width;
    di_para.pre_fb.size.height = src_height;
    di_para.pre_fb.format = di_in_format;

    di_para.input_fb.addr[0] = (unsigned long long)now_src;
    di_para.input_fb.addr[1] = (unsigned long long)(now_src + ALIGN_16B(src_width)*src_height);
    di_para.input_fb.addr[2] = 0x0;
    di_para.input_fb.size.width = src_width;
    di_para.input_fb.size.height = src_height;
    di_para.input_fb.format = di_in_format;

    di_para.next_fb.addr[0] = (unsigned long long)next_src;
    di_para.next_fb.addr[1] = (unsigned long long)(next_src + ALIGN_16B(src_width)*src_height);
    di_para.next_fb.addr[2] = 0x0;
    di_para.next_fb.size.width = src_width;
    di_para.next_fb.size.height = src_height;
    di_para.next_fb.format = di_in_format;

    di_para.source_regn.width = src_width;
    di_para.source_regn.height = src_height;

    di_para.output_fb.addr[0] = (unsigned long long)dst;
    di_para.output_fb.addr[1] = (unsigned long long)(dst + ALIGN_16B(dst_width)*dst_height);
    di_para.output_fb.addr[2] = 0x0;
    di_para.output_fb.size.width = dst_width;
    di_para.output_fb.size.height = dst_height;
    di_para.output_fb.format = di_out_format;
    di_para.out_regn.width = dst_width;
    di_para.out_regn.height = dst_height;

    di_para.field = nField;
    di_para.top_field_first = 1;//nField;
    di_para.id = mDiReqId;

    ret = ioctl(mDiFd,DI_IOCSTART2,&di_para);
    if (ret < 0) {
        return INVALID_OPERATION;
    }

    return NO_ERROR;
}




status_t DiProcess::diProcess3(DIFrame PrePrePic, DIFrame PrePic, DIFrame CurPic,
                          DIFrame OutPic, DIFrame OutPic1, DIFrame OutTnr, DIMode Mode)
{
    DiParaT3 di_para;
    memset(&di_para,0x0,sizeof(DiParaT3));

    switch(Mode){
        case DI300_MODE_BOB:
            setDiMode(mDiFd, DI300_MODE_BOB, PrePrePic.mWidth, PrePrePic.mHeight);
            setBufAddr(&di_para.in_fb0,PrePrePic);
            setBufAddr(&di_para.out_dit_fb0,OutPic);
            break;
        case DI300_MODE_30HZ:
            setDiMode(mDiFd, DI300_MODE_30HZ, PrePrePic.mWidth, PrePrePic.mHeight);
            setBufAddr(&di_para.in_fb0,PrePrePic);
            setBufAddr(&di_para.in_fb1,PrePic);
            setBufAddr(&di_para.out_dit_fb0,OutPic);
            setBufAddr(&di_para.out_tnr_fb0,OutTnr);
            break;
        default:
            ALOGE("di mode not support!");
    }

    di_para.is_interlace = 1;
    di_para.is_pulldown = 0;
    di_para.top_field_first = 1;
    di_para.base_field = 1;

    if (ioctl(mDiFd, DI_IOC_PROCESS_FB, (unsigned long)&di_para) < 0)
    {
        ALOGE("di process failed.%s",strerror(errno));
        return INVALID_OPERATION;
    }
    ALOGV("ji*****di process ok");
    return NO_ERROR;
}

status_t DiProcess::release()
{
    ALOGD("DiProcess::release mDiFd=%d",mDiFd);
    if (mDiFd > 0) {
        if (mDiReqId >= 0) {
            ALOGD("DI_RELEASE, F:%s,L:%d, id:%d start",__FUNCTION__,__LINE__,mDiReqId);
            ioctl(mDiFd, DI_RELEASE, &mDiReqId);
            ALOGD("DI_RELEASE, F:%s,L:%d, id:%d",__FUNCTION__,__LINE__,mDiReqId);
            mDiReqId = -1;
        }
        close(mDiFd);
        mDiFd = -1;
        ALOGD("DiProcess,release ok, F:%s,L:%d",__FUNCTION__,__LINE__);
    }
    return NO_ERROR;
}

unsigned int DiProcess::changeToDiFormat(int format)
{
    unsigned int di_format = 0;
    switch (format) {
        case V4L2_PIX_FMT_NV21:
            di_format = DRM_FORMAT_NV21;
            break;
        case V4L2_PIX_FMT_NV12:
            di_format = DRM_FORMAT_NV12;
            break;
        case V4L2_PIX_FMT_YUV420:
            di_format = DRM_FORMAT_YUV420;
            break;
        case V4L2_PIX_FMT_YVU420:
            di_format = DRM_FORMAT_YVU420;
            break;
        case V4L2_PIX_FMT_NV61:
            di_format = DRM_FORMAT_NV61;
            break;
        case V4L2_PIX_FMT_NV16:
            di_format = DRM_FORMAT_NV16;
            break;
        case V4L2_PIX_FMT_YUYV:
            di_format = DRM_FORMAT_YUYV;
            break;
        default:
            ALOGE("note: format: %d not support!!",format);
    }
    return di_format;
}

status_t DiProcess::setBufAddr(struct di_fb* fb, DIFrame frame)
{
    unsigned int Format;
    Format = changeToDiFormat(frame.mPixelFormat);
    if(frame.mBufFd < 0) {
        fb->format = Format;
        fb->size.width = frame.mWidth;
        fb->size.height = frame.mHeight;
        fb->buf.ystride = ALIGN_16B(frame.mWidth);//better to do it in di300 driver.
        fb->buf.cstride = ALIGN_16B(frame.mWidth);//* attention: yv12
        fb->dma_buf_fd = -1;
        fb->buf.y_addr = frame.mAddrPhy;
        fb->buf.cb_addr = frame.mAddrPhy + ALIGN_16B(frame.mWidth * frame.mHeight);
        if(Format == DRM_FORMAT_YUV420 || Format == DRM_FORMAT_YVU420)
            fb->buf.cr_addr = fb->buf.cb_addr + ALIGN_16B(frame.mWidth * frame.mHeight)/4;
        else if(Format == DRM_FORMAT_YUV422 || Format == DRM_FORMAT_YVU422)
            fb->buf.cr_addr = fb->buf.cb_addr + ALIGN_16B(frame.mWidth * frame.mHeight)/2;
        else
            fb->buf.cr_addr = 0;
    }else {
        fb->format = Format;
        fb->size.width = frame.mWidth;
        fb->size.height = frame.mHeight;
        fb->buf.ystride = ALIGN_16B(frame.mWidth);//better to do it in di300 driver.
        fb->buf.cstride = ALIGN_16B(frame.mWidth);//* attention: yv12
        fb->dma_buf_fd = frame.mBufFd;
        fb->buf.y_addr = 0;
        fb->buf.cb_addr = ALIGN_16B(frame.mWidth * frame.mHeight);
        if(Format == DRM_FORMAT_YUV420 || Format == DRM_FORMAT_YVU420)
            fb->buf.cr_addr = fb->buf.cb_addr + ALIGN_16B(frame.mWidth * frame.mHeight)/4;
        else if(Format == DRM_FORMAT_YUV422 || Format == DRM_FORMAT_YVU422)
            fb->buf.cr_addr = fb->buf.cb_addr + ALIGN_16B(frame.mWidth * frame.mHeight)/2;
        else
            fb->buf.cr_addr = 0;
    }
    ALOGV("Format:%d width:%u height:%u dma_buf_fd:%d "
        "y_addr:%llx cb_addr:%llx cr_addr:%llx ystride:%u cstride:%u\n",
        fb->format, fb->size.width, fb->size.height, fb->dma_buf_fd,
        fb->buf.y_addr, fb->buf.cb_addr, fb->buf.cr_addr,
        fb->buf.ystride, fb->buf.cstride);
    return NO_ERROR;
}

status_t DiProcess::setDiMode(int fd, DIMode mode, int width, int height)
{
    int ret = NO_ERROR;

    struct di_dit_mode dit_mode;
    struct di_tnr_mode tnr_mode;
    struct di_fmd_enable fmd_en;
    struct di_demo_crop_arg demo_arg;
    struct di_rect rect;
    struct di_size size_in;
    memset(&rect, 0, sizeof(rect));
    memset(&dit_mode, 0, sizeof(dit_mode));
    memset(&tnr_mode, 0, sizeof(tnr_mode));
    memset(&fmd_en, 0, sizeof(fmd_en));
    memset(&size_in, 0, sizeof(size_in));

     //* 1. size_in
    size_in.width = width;
    size_in.height = height;

    //* 2. rect
    rect.left = 0;
    rect.top = 0;
    rect.right = size_in.width;
    rect.bottom = size_in.height;

    ret = ioctl(fd, DI_IOC_RESET, 0);
    if (ret)
    {
        ALOGE("DI_IOC_RESET failed\n");
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_SET_VIDEO_SIZE, &size_in);
    if (ret)
    {
        ALOGE("DI_IOC_SET_VIDEO_SIZE failed\n");
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_SET_VIDEO_CROP, &rect);
    if (ret)
    {
        ALOGE("DI_IOC_SET_VIDEO_CROP failed\n");
        return INVALID_OPERATION;
    }

    if(mode == DI300_MODE_60HZ) {
        dit_mode.intp_mode = DI_DIT_INTP_MODE_MOTION;
        dit_mode.out_frame_mode = DI_DIT_OUT_2FRAME;
        tnr_mode.mode = DI_TNR_MODE_FIX;
        tnr_mode.level = DI_TNR_LEVEL_HIGH;
        fmd_en.en = 1;
        demo_arg.tnr_demo.left = 0;
        demo_arg.tnr_demo.top = 0;
        demo_arg.tnr_demo.right = width/2;
        demo_arg.tnr_demo.bottom = height/2;

        demo_arg.dit_demo.left = 0;
        demo_arg.dit_demo.top = 0;
        demo_arg.dit_demo.right = width/2;
        demo_arg.dit_demo.bottom = height/2;
    }else if(mode == DI300_MODE_30HZ) {
        dit_mode.intp_mode = DI_DIT_INTP_MODE_MOTION;
        dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
        tnr_mode.mode = DI_TNR_MODE_FIX;
        tnr_mode.level = DI_TNR_LEVEL_HIGH;
        demo_arg.tnr_demo.left = 0;
        demo_arg.tnr_demo.top = 0;
        demo_arg.tnr_demo.right = width/2;
        demo_arg.tnr_demo.bottom = height/2;

        demo_arg.dit_demo.left = 0;
        demo_arg.dit_demo.top = 0;
        demo_arg.dit_demo.right = width/2;
        demo_arg.dit_demo.bottom = height/2;
        fmd_en.en = 0;
    }else if(mode == DI300_MODE_BOB) {
        dit_mode.intp_mode = DI_DIT_INTP_MODE_BOB;
        dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
        tnr_mode.mode = DI_TNR_MODE_INVALID;
        fmd_en.en = 0;
    }else if(mode == DI300_MODE_WEAVE){
        dit_mode.intp_mode = DI_DIT_INTP_MODE_WEAVE;
        dit_mode.out_frame_mode = DI_DIT_OUT_1FRAME;
        tnr_mode.mode = DI_TNR_MODE_INVALID;
        fmd_en.en = 0;
    }else if(mode == DI300_MODE_TNR){
        dit_mode.intp_mode = DI_DIT_INTP_MODE_INVALID;
        dit_mode.out_frame_mode = DI_DIT_OUT_0FRAME;
        tnr_mode.mode = DI_TNR_MODE_FIX;
        tnr_mode.level = DI_TNR_LEVEL_HIGH;
        fmd_en.en = 0;
    }else{
        dit_mode.intp_mode = DI_DIT_INTP_MODE_INVALID;
        dit_mode.out_frame_mode = DI_DIT_OUT_0FRAME;
        tnr_mode.mode = DI_TNR_MODE_INVALID;
        fmd_en.en = 0;
    }

    ret = ioctl(fd, DI_IOC_SET_DIT_MODE, &dit_mode);
    if (ret)
    {
        ALOGE("DI_IOC_SET_DIT_MODE failed,error:%s\n",strerror(errno));
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_SET_FMD_ENABLE, &fmd_en);
    if (ret)
    {
        ALOGE("DI_IOC_SET_FMD_ENABLE failed\n");
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_SET_TNR_MODE, &tnr_mode);
    if (ret) {
        ALOGE("DI_IOC_SET_TNR_MODE failed\n");
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_SET_DEMO_CROP, &demo_arg);
    if (ret < 0)
    {
        ALOGE("DI_IOC_SET_DEMO_CROP failed.");
        return INVALID_OPERATION;
    }

    ret = ioctl(fd, DI_IOC_CHECK_PARA, 0);
    if (ret)
    {
        ALOGE("DI_IOC_CHECK_PARA\n");
        return INVALID_OPERATION;
    }

    return NO_ERROR;
}

}
