/*
 *****************************************************************************
 * gc2385_mipi
 * 1600x1200@30fps, wdr: 0
 * Hawkview ISP - gc2385_mipi config module
 * Copyright (c) 2018 by Allwinnertech Co., Ltd. http://www.allwinnertech.com
 *  Version  |     Author      |     Date     |      Description
 *    2.0    |  Hawkview Tool  |  2018/05/04  |  Automatic generation.
 *
 *****************************************************************************
 */

#ifndef _GC2385_MIPI_H_V100_
#define _GC2385_MIPI_H_V100_

#include "../../include/isp_ini_parse.h"

struct isp_test_param gc2385_mipi_isp_test_settings = {
	.isp_test_mode = 0,
	.isp_test_exptime = 0,
	.exp_line_start = 1000,
	.exp_line_step = 1000,
	.exp_line_end = 32000,
	.exp_change_interval = 5,
	.isp_test_gain = 0,
	.gain_start = 256,
	.gain_step = 64,
	.gain_end = 4096,
	.gain_change_interval = 5,
	.isp_test_focus = 0,
	.focus_start = 10,
	.focus_step = 10,
	.focus_end = 800,
	.focus_change_interval = 5,
	.isp_log_param = 0,
	.isp_gain = 280,
	.isp_exp_line = 2000,
	.isp_color_temp = 2700,
	.ae_forced = 0,
	.lum_forced = 80,
	.manual_en = 1,
	.afs_en = 1,
	.sharp_en = 1,
	.contrast_en = 1,
	.denoise_en = 1,
	.drc_en = 1,
	.cem_en = 0,
	.lsc_en = 1,
	.gamma_en = 1,
	.cm_en = 1,
	.ae_en = 1,
	.af_en = 0,
	.awb_en = 1,
	.hist_en = 1,
	.blc_en = 0,
	.so_en = 1,
	.wb_en = 1,
	.otf_dpc_en = 1,
	.cfa_en = 1,
	.tdf_en = 0,
	.cnr_en = 1,
	.satur_en = 1,
	.defog_en = 0,
	.linear_en = 0,
	.gtm_en = 0,
	.dig_gain_en = 1,
	.pltm_en = 0,
	.wdr_en = 0,
	.ctc_en = 0
};
struct isp_3a_param gc2385_mipi_isp_3a_settings = {
	.define_ae_table = 1,
	.ae_max_lv = 1650,
	.ae_table_preview_length = 2,
	.ae_table_preview = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_capture_length = 2,
	.ae_table_capture = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_video_length = 2,
	.ae_table_video = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_win_weight = {
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     8,     8,     4,     4,     4,
		    4,     4,     6,     8,     8,     6,     4,     4,
		    4,     6,     8,     8,     8,     8,     6,     4,
		    4,     8,     8,     8,     8,     8,     8,     4,
		    4,     4,     4,     4,     4,     4,     4,     4
	},
	.ae_hist_mod_en = 1,
	.ae_hist_sel = 0,
	.ae_stat_sel = 2,
	.ae_ki = 50,
	.ae_ConvDataIndex = 3,
	.ae_blowout_pre_en = 0,
	.ae_blowout_attr = 30,
	.ae_delay_frame = 0,
	.exp_delay_frame = 2,
	.gain_delay_frame = 2,
	.exp_comp_step = 4,
	.ae_touch_dist_ind = 0,
	.ae_iso2gain_ratio = 16,
	.ae_fno_step = {
		  141,   145,   152,   163,   175,   190,   209,   233,
		  266,   311,   379,   487,   657,   971,  1825,  3794
	},
	.wdr_cfg = {
		   16,   128,  1280
	},
	.awb_interval = 2,
	.awb_speed = 16,
	.awb_stat_sel = 0,
	.awb_color_temper_low = 1800,
	.awb_color_temper_high = 8000,
	.awb_base_temper = 6500,
	.awb_green_zone_dist = 63,
	.awb_blue_sky_dist = 63,
	.awb_light_num = 8,
	.awb_light_info = {
		  336,   256,   126,   256,   256,   256,    64,  1900,    32,    90,
		  306,   256,   133,   256,   256,   256,    64,  2500,    32,    90,
		  278,   256,   141,   256,   256,   256,    64,  2800,    32,    90,
		  240,   256,   162,   256,   256,   256,    64,  4000,    64,   100,
		  196,   256,   156,   256,   256,   256,    64,  4100,    96,   100,
		  216,   256,   186,   256,   256,   256,    64,  5000,   100,   100,
		  202,   256,   220,   256,   256,   256,    64,  6500,    64,   100,
		  193,   256,   256,   256,   256,   256,    64,  7500,    64,   100
	},
	.awb_ext_light_num = 0,
	.awb_ext_light_info = {
		0
	},
	.awb_skin_color_num = 0,
	.awb_skin_color_info = {
		0
	},
	.awb_special_color_num = 0,
	.awb_special_color_info = {
		0
	},
	.awb_preset_gain = {
		  256,   256,   256,   256,   151,   405,   210,   340,   210,   340,
		  145,   480,   265,   256,   256,   256,   285,   245,   280,   235,
		  140,   480
	},
	.awb_rgain_favor = 256,
	.awb_bgain_favor = 256,
	.af_use_otp = 0,
	.vcm_min_code = 380,
	.vcm_max_code = 800,
	.af_interval_time = 136,
	.af_speed_ind = 20,
	.af_auto_fine_en = 0,
	.af_single_fine_en = 0,
	.af_fine_step = 10,
	.af_move_cnt = 4,
	.af_still_cnt = 2,
	.af_move_monitor_cnt = 6,
	.af_still_monitor_cnt = 3,
	.af_stable_min = 245,
	.af_stable_max = 265,
	.af_low_light_lv = 10,
	.af_near_tolerance = 15,
	.af_far_tolerance = 25,
	.af_tolerance_off = 0,
	.af_peak_th = 100,
	.af_dir_th = 10,
	.af_change_ratio = 30,
	.af_move_minus = 2,
	.af_still_minus = 1,
	.af_scene_motion_th = 0,
	.af_tolerance_tbl_len = 0,
	.af_std_code_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	},
	.af_tolerance_value_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	}
};
struct isp_dynamic_param gc2385_mipi_isp_iso_settings = {
	.triger = {
		.sharp_triger = 1,
		.contrast_triger = 1,
		.denoise_triger = 1,
		.sensor_offset_triger = 1,
		.black_level_triger = 1,
		.dpc_triger = 1,
		.defog_value_triger = 0,
		.pltm_dynamic_triger = 0,
		.brightness_triger = 0,
		.gcontrast_triger = 0,
		.saturation_triger = 1,
		.cem_ratio_triger = 0,
		.tdf_triger = 0,
		.color_denoise_triger = 1,
		.ae_cfg_triger = 0,
		.gtm_cfg_triger = 1
	},
	.isp_lum_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_gain_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_dynamic_cfg[0] = {
		.sharp_cfg = {
			8, 20, 188, 188, 188, 188, 256, 0, 256, 0
		},
		.contrast_cfg = {
			1, 16, 36, 12, 105, 512, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			100, 0, 80, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			16, 16, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 160, 80, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 256, 0, 256, 0, 256, 0, 256, 0
		},
		.color_denoise = 4,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 62, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[1] = {
		.sharp_cfg = {
			8, 24, 177, 177, 177, 177, 256, 0, 256, 0
		},
		.contrast_cfg = {
			2, 16, 36, 12, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			200, 0, 160, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			12, 12, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 160, 80, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 576, 0, 576, 0, 384, 0, 384, 0
		},
		.color_denoise = 16,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 62, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[2] = {
		.sharp_cfg = {
			8, 28, 166, 166, 166, 166, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			300, 0, 240, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			8, 8, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 128, 40, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 768, 0, 768, 0, 512, 0, 512, 0
		},
		.color_denoise = 32,
		.ae_cfg = {
			380, 192, 256, 256, 12, 12, 12, 12, 3, 64, 4, 12, 22, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[3] = {
		.sharp_cfg = {
			8, 32, 155, 155, 155, 155, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			400, 0, 320, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			4, 4, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 64, 20, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 960, 0, 960, 0, 768, 0, 768, 0
		},
		.color_denoise = 50,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 64, 4, 12, 16, 64
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[4] = {
		.sharp_cfg = {
			8, 36, 144, 144, 144, 144, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 36, 12, 105, 256, 64, 256, 0, 256, 0
		},
		.denoise_cfg = {
			500, 0, 400, 0
		},
		.sensor_offset = {
			-240, -240, -240, -240
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			2, 3, 2047, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 32, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1152, 0, 1152, 0, 1152, 0, 1152, 0
		},
		.color_denoise = 80,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 16, 12, 48
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[5] = {
		.sharp_cfg = {
			10, 40, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 34, 12, 105, 256, 80, 256, 0, 256, 0
		},
		.denoise_cfg = {
			600, 0, 480, 0
		},
		.sensor_offset = {
			-240, -240, -240, -240
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1600, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 16, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1344, 0, 1344, 0, 1536, 0, 1536, 0
		},
		.color_denoise = 120,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 24, 8, 32
		},
		.gtm_cfg = {
			1024, 22, 22, 5, 215, 1, 7, 1, -2
		},
	},
	.isp_dynamic_cfg[6] = {
		.sharp_cfg = {
			12, 50, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 30, 12, 105, 256, 96, 256, 0, 256, 0
		},
		.denoise_cfg = {
			512, 0, 512, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1536, 0, 1536, 0, 1920, 0, 1920, 0
		},
		.color_denoise = 160,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 6, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[7] = {
		.sharp_cfg = {
			14, 60, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 26, 12, 105, 256, 104, 256, 0, 256, 0
		},
		.denoise_cfg = {
			768, 0, 768, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1728, 0, 1728, 0, 2340, 0, 2340, 0
		},
		.color_denoise = 100,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[8] = {
		.sharp_cfg = {
			16, 70, 33, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 22, 12, 105, 256, 112, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1000, 0, 1280, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1920, 0, 1920, 0, 2560, 0, 2560, 0
		},
		.color_denoise = 122,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[9] = {
		.sharp_cfg = {
			18, 80, 33, 133, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 18, 10, 105, 200, 128, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1200, 0, 1200, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2112, 0, 2112, 0, 2816, 0, 2816, 0
		},
		.color_denoise = 144,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[10] = {
		.sharp_cfg = {
			20, 90, 122, 122, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 14, 8, 105, 199, 160, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2200, 0, 2000, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2304, 0, 2304, 0, 3072, 0, 3072, 0
		},
		.color_denoise = 166,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[11] = {
		.sharp_cfg = {
			22, 100, 88, 88, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 10, 7, 88, 188, 256, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2800, 0, 2240, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2496, 0, 2496, 0, 3238, 0, 3238, 0
		},
		.color_denoise = 188,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[12] = {
		.sharp_cfg = {
			24, 110, 66, 66, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 8, 6, 77, 177, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			3600, 0, 2360, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2688, 0, 2688, 0, 3584, 0, 3584, 0
		},
		.color_denoise = 200,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[13] = {
		.sharp_cfg = {
			26, 120, 44, 44, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 4, 3, 66, 166, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			4400, 0, 2560, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 3072, 0, 3072, 0, 4096, 0, 4096, 0
		},
		.color_denoise = 222,
		.ae_cfg = {
			256, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	}
};
struct isp_tunning_param gc2385_mipi_isp_tuning_settings = {
	.flash_gain = 80,
	.flash_delay_frame = 16,
	.flicker_type = 0,
	.flicker_ratio = 15,
	.hor_visual_angle = 60,
	.ver_visual_angle = 40,
	.focus_length = 300,
	.gamma_num = 5,
	.rolloff_ratio = 8,
	.gtm_type = 1,
	.gamma_type = 1,
	.auto_alpha_en = 1,
	.cfa_dir_th = 2047,
	.ctc_th_max = 316,
	.ctc_th_min = 60,
	.ctc_th_slope = 262,
	.ctc_dir_wt = 64,
	.ctc_dir_th = 80,
	.bayer_gain = {
		 1024,  1024,  1024,  1024
	},
	.ff_mod = 2,
	.lsc_center_x = 2048,
	.lsc_center_y = 2048,
	.lsc_trig_cfg = {
		 2200,  2800,  4000,  5000,  5500,  6500
	},
	.gamma_trig_cfg = {
		 1300,  1100,   900,   600,   300
	},
	.color_matrix_ini[0] = {
		.matrix = { { 271, 0, -15 }, { -154, 525, -115 },
				{ -126, -111, 493 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[1] = {
		.matrix = { { 362, 0, -106 }, { -120, 426, -50 },
				{ -69, -91, 416 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[2] = {
		.matrix = { { 407, -46, -105 }, { -101, 411, -54 },
				{ -40, -70, 366 } },
		.offset = { 0, 0, 0 }
	},
	.cm_trig_cfg = {
		 2700,  4000,  6500
	},
	.pltm_cfg = {
		    1,     0,    10,     7,  2048,  2048,     0,    15,
		   15,   210,    32,   255,    23,    31,    34
	},
	.isp_bdnf_th = {
		    7,    18,    25,    28,    39,    66,    94,   116,
		  138,   133,   111,   105,   105,    94,    77,    77,
		   77,    72,    59,    62,    63,    66,    67,    70,
		   71,    74,    76,    78,    80,    83,    84,    87,
		   88
	},
	.isp_tdnf_th = {
		    4,     4,     5,     6,     7,     8,     9,    10,
		   11,    12,    13,    14,    15,    16,    17,    18,
		   19,    20,    21,    22,    23,    24,    25,    26,
		   27,    28,    29,    30,    31,    32,    32,    32,
		   32
	},
	.isp_tdnf_ref_noise = {
		    6,     6,     7,     8,     8,     9,    10,    11,
		   12,    13,    14,    15,    16,    17,    18,    19,
		   20,    21,    22,    23,    24,    25,    26,    27,
		   28,    29,    30,    31,    32,    32,    32,    32,
		   32
	},
	.isp_tdnf_k = {
		    4,     4,     6,     8,    12,    16,    24,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31
	},
	.isp_contrast_val = {
		  103,   160,   160,   160,   160,   160,   176,   192,
		  208,   208,   208,   208,   208,   208,   208,   208,
		  208,   208,   180,   160,   144,   128,   112,    96,
		   80,    72,    64,    56,    48,    32,    32,    32,
		   32
	},
	.isp_contrast_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_sharp_val = {
		  144,   132,   128,   128,   128,   128,   124,   117,
		   96,    96,    80,    80,    64,    64,    48,    48,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32
	},
	.isp_sharp_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_tdnf_diff = {
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   254,   254,   254,   254,   254,   254,   253,
		  253,   253,   253,   253,   252,   252,   252,   252,
		  252,   251,   251,   251,   250,   250,   250,   250,
		  249,   249,   249,   248,   248,   248,   247,   247,
		  247,   246,   246,   245,   245,   245,   244,   244,
		  243,   243,   242,   242,   241,   241,   240,   240,
		  240,   239,   238,   238,   237,   237,   236,   236,
		  235,   235,   234,   234,   233,   232,   232,   231,
		  231,   230,   229,   229,   228,   227,   227,   226,
		  225,   225,   224,   223,   222,   222,   221,   220,
		  220,   219,   218,   217,   216,   216,   215,   214,
		  213,   212,   212,   211,   210,   209,   208,   207,
		  207,   206,   205,   204,   203,   202,   201,   200,
		  199,   198,   197,   196,   195,   194,   193,   192,
		  192,   190,   189,   188,   187,   186,   185,   184,
		  183,   182,   181,   180,   179,   178,   177,   176,
		  175,   173,   172,   171,   170,   169,   168,   166,
		  165,   164,   163,   162,   160,   159,   158,   157,
		  156,   154,   153,   152,   150,   149,   148,   147,
		  145,   144,   143,   141,   140,   139,   137,   136,
		  135,   133,   132,   130,   129,   128,   126,   125,
		  123,   122,   120,   119,   117,   116,   114,   113,
		  112,   110,   108,   107,   105,   104,   102,   101,
		   99,    98,    96,    95,    93,    91,    90,    88,
		   87,    85,    83,    82,    80,    78,    77,    75,
		   73,    72,    70,    68,    66,    65,    63,    61,
		   60,    58,    56,    54,    52,    51,    49,    47,
		   45,    43,    42,    40,    38,    36,    34,    32,
		   31,    29,    27,    25,    23,    21,    19,    17,
		   15,    13,    11,     9,     7,     5,     3,     1
	},
	.isp_contrat_pe = {
		    0,     2,     4,     6,     8,    10,    12,    14,
		   16,    26,    36,    46,    56,    66,    76,    86,
		   96,   100,   104,   108,   112,   116,   120,   124,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   130,   132,   134,   136,   138,   140,   142,
		  144,   146,   148,   150,   152,   154,   156,   158,
		  160,   164,   168,   172,   176,   180,   184,   188,
		  192,   195,   197,   200,   202,   205,   207,   209,
		  212,   209,   207,   205,   202,   200,   197,   195,
		  192,   188,   184,   180,   176,   172,   168,   164,
		  160,   158,   156,   154,   152,   150,   148,   146,
		  144,   142,   140,   138,   136,   134,   132,   130,
		  128,   126,   124,   122,   120,   118,   116,   114,
		  112,   110,   108,   106,   104,   102,   100,    98,
		   96,    96,    96,    96,    96,    96,    96,    96,
		   96,    96,    96,    96,    96,    96,    96,    96
	},
	.gamma_tbl_ini = {
	{
		/* gamma - 0 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 1 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 2 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 3 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 4 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	}
	},
	.lsc_tbl = {
	{
		/* lsc - 0 */
		/* R */
		 1021, 1023, 1028, 1034, 1040, 1047, 1053, 1061,
		 1068, 1076, 1083, 1090, 1095, 1102, 1111, 1119,
		 1126, 1132, 1139, 1146, 1153, 1161, 1166, 1172,
		 1178, 1184, 1190, 1197, 1205, 1212, 1217, 1222,
		 1229, 1236, 1241, 1246, 1254, 1261, 1266, 1271,
		 1279, 1286, 1290, 1295, 1303, 1309, 1314, 1319,
		 1325, 1332, 1337, 1342, 1348, 1354, 1361, 1367,
		 1372, 1377, 1383, 1388, 1395, 1401, 1407, 1411,
		 1415, 1422, 1428, 1434, 1439, 1444, 1449, 1456,
		 1463, 1468, 1471, 1476, 1481, 1486, 1492, 1498,
		 1503, 1508, 1513, 1519, 1525, 1530, 1535, 1542,
		 1548, 1550, 1553, 1557, 1563, 1569, 1572, 1577,
		 1585, 1592, 1597, 1601, 1605, 1610, 1617, 1621,
		 1626, 1631, 1636, 1642, 1648, 1653, 1656, 1663,
		 1668, 1673, 1676, 1680, 1684, 1692, 1696, 1700,
		 1703, 1711, 1714, 1719, 1724, 1730, 1735, 1740,
		 1746, 1752, 1756, 1760, 1763, 1768, 1774, 1778,
		 1784, 1787, 1791, 1795, 1802, 1807, 1814, 1820,
		 1823, 1826, 1831, 1836, 1838, 1842, 1847, 1856,
		 1864, 1867, 1870, 1875, 1882, 1886, 1892, 1897,
		 1903, 1907, 1912, 1917, 1921, 1923, 1928, 1935,
		 1940, 1945, 1950, 1956, 1961, 1966, 1971, 1975,
		 1978, 1980, 1981, 1986, 1993, 2001, 2004, 2006,
		 2012, 2019, 2022, 2026, 2030, 2033, 2036, 2042,
		 2044, 2048, 2057, 2064, 2069, 2070, 2075, 2080,
		 2087, 2092, 2099, 2105, 2112, 2117, 2125, 2132,
		 2140, 2145, 2149, 2152, 2157, 2166, 2172, 2184,
		 2193, 2200, 2205, 2205, 2205, 2211, 2227, 2232,
		 2241, 2245, 2249, 2249, 2264, 2278, 2288, 2288,
		 2303, 2318, 2327, 2335, 2341, 2358, 2368, 2377,
		 2375, 2419, 2430, 2442, 2454, 2466, 2477, 2489,
		 2500, 2512, 2524, 2535, 2547, 2558, 2570, 2582,
		/* G */
		 1020, 1025, 1028, 1030, 1032, 1036, 1041, 1045,
		 1049, 1054, 1059, 1062, 1065, 1069, 1073, 1079,
		 1082, 1085, 1088, 1093, 1097, 1102, 1104, 1107,
		 1112, 1117, 1120, 1123, 1126, 1131, 1133, 1137,
		 1140, 1145, 1148, 1151, 1154, 1159, 1162, 1164,
		 1168, 1172, 1175, 1177, 1180, 1183, 1187, 1190,
		 1194, 1197, 1200, 1203, 1207, 1210, 1214, 1217,
		 1220, 1222, 1226, 1228, 1231, 1236, 1240, 1242,
		 1243, 1246, 1250, 1254, 1257, 1259, 1261, 1265,
		 1268, 1271, 1272, 1274, 1278, 1281, 1283, 1287,
		 1290, 1292, 1295, 1299, 1302, 1305, 1308, 1312,
		 1315, 1316, 1317, 1320, 1322, 1325, 1328, 1333,
		 1336, 1339, 1341, 1344, 1347, 1351, 1355, 1358,
		 1361, 1364, 1366, 1370, 1372, 1376, 1378, 1381,
		 1383, 1385, 1387, 1391, 1394, 1398, 1400, 1405,
		 1407, 1410, 1410, 1412, 1416, 1420, 1422, 1424,
		 1427, 1430, 1432, 1433, 1433, 1436, 1441, 1445,
		 1448, 1449, 1450, 1451, 1455, 1458, 1462, 1464,
		 1466, 1469, 1472, 1475, 1477, 1478, 1479, 1482,
		 1487, 1490, 1492, 1495, 1499, 1500, 1503, 1506,
		 1509, 1510, 1514, 1518, 1519, 1519, 1522, 1527,
		 1530, 1533, 1535, 1538, 1542, 1544, 1548, 1554,
		 1557, 1557, 1558, 1563, 1568, 1571, 1571, 1571,
		 1575, 1580, 1582, 1585, 1590, 1595, 1597, 1599,
		 1599, 1601, 1608, 1612, 1614, 1615, 1620, 1623,
		 1629, 1635, 1638, 1640, 1646, 1650, 1654, 1656,
		 1663, 1669, 1673, 1674, 1678, 1687, 1693, 1701,
		 1704, 1709, 1716, 1716, 1713, 1716, 1727, 1733,
		 1743, 1746, 1748, 1747, 1757, 1763, 1768, 1768,
		 1779, 1789, 1790, 1793, 1800, 1824, 1841, 1845,
		 1834, 1864, 1876, 1888, 1900, 1911, 1923, 1935,
		 1947, 1959, 1971, 1983, 1995, 2006, 2018, 2030,
		/* B */
		 1026, 1022, 1021, 1022, 1024, 1027, 1031, 1035,
		 1037, 1039, 1042, 1046, 1048, 1051, 1056, 1060,
		 1063, 1063, 1064, 1069, 1073, 1075, 1076, 1078,
		 1081, 1085, 1089, 1091, 1093, 1097, 1098, 1099,
		 1101, 1107, 1109, 1110, 1112, 1117, 1119, 1120,
		 1123, 1126, 1127, 1129, 1131, 1131, 1134, 1137,
		 1139, 1140, 1143, 1145, 1147, 1150, 1152, 1154,
		 1155, 1156, 1158, 1161, 1161, 1164, 1168, 1171,
		 1170, 1171, 1172, 1175, 1177, 1179, 1180, 1182,
		 1183, 1184, 1185, 1184, 1187, 1190, 1192, 1193,
		 1194, 1195, 1195, 1198, 1200, 1201, 1203, 1205,
		 1206, 1206, 1207, 1208, 1209, 1210, 1212, 1215,
		 1218, 1219, 1221, 1224, 1227, 1228, 1230, 1231,
		 1232, 1234, 1236, 1238, 1240, 1241, 1243, 1246,
		 1248, 1250, 1252, 1253, 1254, 1257, 1257, 1260,
		 1261, 1264, 1265, 1268, 1269, 1273, 1274, 1276,
		 1275, 1278, 1280, 1281, 1279, 1282, 1287, 1290,
		 1291, 1291, 1293, 1294, 1296, 1298, 1302, 1304,
		 1305, 1305, 1307, 1310, 1311, 1312, 1312, 1315,
		 1318, 1318, 1318, 1319, 1322, 1322, 1322, 1325,
		 1328, 1330, 1332, 1336, 1336, 1335, 1335, 1339,
		 1342, 1344, 1346, 1352, 1355, 1354, 1351, 1353,
		 1356, 1358, 1359, 1363, 1366, 1368, 1367, 1367,
		 1371, 1372, 1372, 1375, 1381, 1384, 1385, 1388,
		 1387, 1385, 1387, 1392, 1398, 1400, 1400, 1400,
		 1405, 1408, 1409, 1411, 1413, 1415, 1417, 1423,
		 1431, 1436, 1436, 1436, 1438, 1442, 1445, 1451,
		 1454, 1455, 1457, 1457, 1456, 1456, 1464, 1473,
		 1483, 1484, 1483, 1483, 1493, 1499, 1503, 1502,
		 1506, 1512, 1513, 1512, 1511, 1525, 1548, 1566,
		 1559, 1531, 1543, 1555, 1567, 1579, 1591, 1603,
		 1615, 1627, 1639, 1651, 1663, 1675, 1687, 1699
	},
	{
		/* lsc - 1 */
		/* R */
		 1020, 1025, 1032, 1038, 1043, 1049, 1056, 1063,
		 1069, 1074, 1080, 1086, 1092, 1098, 1104, 1110,
		 1116, 1122, 1129, 1135, 1141, 1146, 1152, 1159,
		 1166, 1171, 1177, 1184, 1190, 1195, 1201, 1207,
		 1213, 1219, 1225, 1230, 1236, 1242, 1248, 1255,
		 1260, 1265, 1270, 1276, 1282, 1287, 1292, 1298,
		 1304, 1310, 1318, 1324, 1330, 1335, 1341, 1348,
		 1353, 1360, 1366, 1372, 1377, 1383, 1388, 1393,
		 1398, 1403, 1408, 1413, 1419, 1424, 1429, 1433,
		 1438, 1442, 1447, 1452, 1457, 1463, 1467, 1471,
		 1476, 1481, 1486, 1491, 1495, 1502, 1507, 1513,
		 1516, 1521, 1525, 1530, 1534, 1539, 1543, 1549,
		 1555, 1561, 1565, 1569, 1573, 1577, 1582, 1587,
		 1591, 1596, 1601, 1606, 1611, 1616, 1621, 1625,
		 1629, 1633, 1638, 1643, 1647, 1651, 1656, 1660,
		 1664, 1669, 1674, 1680, 1685, 1688, 1691, 1694,
		 1699, 1704, 1710, 1714, 1719, 1724, 1729, 1734,
		 1738, 1743, 1745, 1749, 1753, 1759, 1765, 1769,
		 1772, 1777, 1781, 1785, 1787, 1793, 1798, 1804,
		 1808, 1811, 1815, 1823, 1829, 1833, 1835, 1841,
		 1844, 1850, 1854, 1859, 1862, 1867, 1872, 1876,
		 1882, 1889, 1892, 1897, 1900, 1903, 1905, 1913,
		 1920, 1926, 1929, 1932, 1937, 1943, 1948, 1952,
		 1958, 1961, 1966, 1970, 1975, 1978, 1982, 1987,
		 1991, 1995, 1999, 2007, 2015, 2021, 2031, 2034,
		 2037, 2039, 2046, 2052, 2058, 2065, 2070, 2078,
		 2084, 2089, 2094, 2100, 2104, 2107, 2115, 2130,
		 2140, 2150, 2149, 2157, 2163, 2171, 2170, 2182,
		 2190, 2195, 2198, 2208, 2211, 2215, 2220, 2227,
		 2236, 2241, 2251, 2263, 2277, 2284, 2301, 2312,
		 2316, 2316, 2324, 2332, 2340, 2348, 2355, 2363,
		 2371, 2379, 2387, 2395, 2402, 2410, 2418, 2426,
		/* G */
		 1022, 1025, 1028, 1031, 1035, 1039, 1043, 1048,
		 1053, 1056, 1059, 1064, 1067, 1070, 1075, 1081,
		 1085, 1089, 1094, 1098, 1102, 1105, 1109, 1113,
		 1116, 1119, 1123, 1127, 1131, 1133, 1136, 1139,
		 1142, 1145, 1149, 1151, 1155, 1159, 1162, 1166,
		 1169, 1171, 1174, 1177, 1180, 1184, 1187, 1191,
		 1193, 1197, 1201, 1205, 1208, 1210, 1213, 1217,
		 1220, 1223, 1227, 1230, 1233, 1237, 1239, 1242,
		 1245, 1249, 1252, 1255, 1259, 1262, 1264, 1267,
		 1269, 1272, 1275, 1278, 1280, 1282, 1285, 1288,
		 1291, 1294, 1297, 1300, 1303, 1306, 1310, 1314,
		 1316, 1317, 1319, 1322, 1325, 1327, 1330, 1333,
		 1336, 1339, 1342, 1344, 1346, 1348, 1352, 1355,
		 1357, 1360, 1363, 1366, 1369, 1372, 1374, 1377,
		 1378, 1381, 1385, 1388, 1390, 1392, 1395, 1398,
		 1400, 1404, 1407, 1410, 1413, 1415, 1416, 1419,
		 1422, 1425, 1427, 1431, 1434, 1437, 1438, 1442,
		 1445, 1448, 1448, 1451, 1453, 1457, 1461, 1463,
		 1465, 1469, 1471, 1473, 1475, 1479, 1482, 1485,
		 1488, 1491, 1494, 1499, 1501, 1503, 1504, 1508,
		 1510, 1514, 1517, 1519, 1521, 1525, 1529, 1532,
		 1536, 1537, 1539, 1543, 1546, 1549, 1551, 1556,
		 1558, 1562, 1565, 1568, 1572, 1577, 1580, 1581,
		 1585, 1587, 1590, 1594, 1599, 1601, 1604, 1607,
		 1611, 1613, 1616, 1620, 1625, 1626, 1631, 1636,
		 1643, 1646, 1650, 1654, 1660, 1663, 1666, 1670,
		 1675, 1681, 1685, 1689, 1692, 1698, 1702, 1707,
		 1710, 1719, 1725, 1734, 1737, 1740, 1740, 1749,
		 1752, 1756, 1763, 1775, 1774, 1778, 1783, 1788,
		 1792, 1801, 1806, 1810, 1814, 1821, 1830, 1840,
		 1846, 1845, 1853, 1861, 1869, 1877, 1885, 1894,
		 1902, 1910, 1918, 1926, 1934, 1942, 1950, 1958,
		/* B */
		 1017, 1025, 1029, 1031, 1032, 1034, 1037, 1040,
		 1044, 1046, 1048, 1051, 1054, 1057, 1061, 1066,
		 1069, 1072, 1075, 1079, 1082, 1083, 1086, 1090,
		 1093, 1096, 1097, 1100, 1104, 1107, 1109, 1111,
		 1113, 1115, 1117, 1119, 1122, 1124, 1126, 1129,
		 1132, 1133, 1134, 1137, 1139, 1142, 1143, 1146,
		 1148, 1150, 1152, 1154, 1157, 1158, 1161, 1163,
		 1165, 1167, 1169, 1173, 1174, 1177, 1179, 1181,
		 1183, 1186, 1187, 1189, 1192, 1195, 1196, 1197,
		 1199, 1201, 1203, 1204, 1205, 1206, 1208, 1210,
		 1212, 1215, 1216, 1218, 1220, 1223, 1225, 1227,
		 1228, 1229, 1230, 1232, 1234, 1236, 1238, 1240,
		 1242, 1244, 1246, 1247, 1248, 1251, 1255, 1256,
		 1257, 1259, 1260, 1262, 1265, 1268, 1269, 1271,
		 1272, 1274, 1277, 1280, 1280, 1281, 1283, 1286,
		 1287, 1289, 1291, 1295, 1298, 1299, 1301, 1303,
		 1306, 1308, 1309, 1311, 1312, 1314, 1315, 1319,
		 1322, 1324, 1324, 1324, 1325, 1329, 1332, 1332,
		 1333, 1337, 1339, 1340, 1341, 1344, 1345, 1348,
		 1350, 1352, 1354, 1356, 1357, 1359, 1360, 1362,
		 1364, 1368, 1370, 1371, 1372, 1373, 1376, 1379,
		 1382, 1384, 1385, 1387, 1389, 1392, 1394, 1396,
		 1396, 1398, 1399, 1403, 1407, 1411, 1413, 1414,
		 1418, 1420, 1423, 1424, 1425, 1426, 1430, 1434,
		 1436, 1436, 1437, 1438, 1442, 1443, 1448, 1452,
		 1456, 1457, 1459, 1460, 1464, 1465, 1464, 1467,
		 1475, 1482, 1484, 1486, 1489, 1494, 1497, 1499,
		 1500, 1510, 1514, 1519, 1518, 1522, 1521, 1526,
		 1524, 1526, 1530, 1539, 1537, 1543, 1546, 1549,
		 1551, 1557, 1559, 1559, 1564, 1576, 1589, 1594,
		 1592, 1592, 1600, 1608, 1617, 1625, 1633, 1641,
		 1649, 1658, 1666, 1674, 1682, 1690, 1698, 1707
	},
	{
		/* lsc - 2 */
		/* R */
		 1020, 1022, 1028, 1033, 1036, 1039, 1043, 1046,
		 1050, 1055, 1059, 1063, 1066, 1071, 1074, 1078,
		 1081, 1084, 1089, 1093, 1098, 1101, 1105, 1109,
		 1112, 1115, 1118, 1122, 1126, 1129, 1133, 1135,
		 1138, 1141, 1143, 1146, 1150, 1154, 1157, 1160,
		 1162, 1165, 1167, 1169, 1172, 1177, 1181, 1183,
		 1186, 1188, 1192, 1195, 1196, 1200, 1204, 1208,
		 1210, 1212, 1214, 1218, 1222, 1225, 1227, 1230,
		 1234, 1237, 1240, 1242, 1245, 1248, 1251, 1254,
		 1256, 1259, 1262, 1266, 1268, 1270, 1272, 1275,
		 1279, 1282, 1285, 1288, 1291, 1293, 1297, 1300,
		 1303, 1305, 1308, 1311, 1314, 1315, 1319, 1322,
		 1327, 1331, 1334, 1335, 1336, 1339, 1342, 1345,
		 1347, 1350, 1353, 1357, 1359, 1362, 1365, 1370,
		 1372, 1374, 1377, 1380, 1382, 1385, 1388, 1390,
		 1392, 1395, 1400, 1403, 1405, 1406, 1408, 1412,
		 1417, 1421, 1423, 1426, 1429, 1431, 1434, 1437,
		 1441, 1443, 1445, 1446, 1448, 1452, 1454, 1458,
		 1462, 1467, 1469, 1471, 1474, 1477, 1481, 1485,
		 1488, 1492, 1492, 1496, 1499, 1503, 1506, 1510,
		 1512, 1514, 1517, 1521, 1526, 1529, 1531, 1534,
		 1538, 1539, 1542, 1546, 1550, 1553, 1556, 1558,
		 1562, 1568, 1570, 1573, 1576, 1580, 1581, 1585,
		 1591, 1598, 1600, 1603, 1605, 1611, 1613, 1616,
		 1616, 1619, 1626, 1632, 1636, 1637, 1642, 1645,
		 1650, 1655, 1662, 1664, 1666, 1668, 1674, 1682,
		 1686, 1690, 1691, 1698, 1702, 1710, 1715, 1721,
		 1720, 1724, 1734, 1745, 1747, 1749, 1753, 1757,
		 1760, 1761, 1768, 1772, 1780, 1780, 1788, 1795,
		 1806, 1810, 1822, 1831, 1836, 1832, 1836, 1844,
		 1855, 1858, 1863, 1869, 1874, 1880, 1885, 1891,
		 1897, 1902, 1908, 1913, 1919, 1924, 1930, 1935,
		/* G */
		 1024, 1022, 1026, 1030, 1033, 1035, 1037, 1039,
		 1044, 1047, 1049, 1051, 1054, 1056, 1058, 1062,
		 1065, 1068, 1069, 1073, 1076, 1079, 1083, 1085,
		 1088, 1090, 1094, 1097, 1099, 1102, 1105, 1108,
		 1110, 1113, 1115, 1117, 1121, 1124, 1126, 1129,
		 1132, 1134, 1135, 1137, 1139, 1142, 1145, 1146,
		 1148, 1150, 1154, 1156, 1157, 1159, 1163, 1166,
		 1167, 1168, 1169, 1173, 1175, 1178, 1180, 1182,
		 1185, 1187, 1189, 1191, 1193, 1196, 1198, 1200,
		 1202, 1203, 1206, 1209, 1212, 1213, 1214, 1216,
		 1219, 1222, 1224, 1225, 1229, 1231, 1235, 1237,
		 1239, 1239, 1242, 1244, 1246, 1248, 1250, 1252,
		 1256, 1260, 1262, 1262, 1263, 1266, 1269, 1270,
		 1272, 1275, 1278, 1281, 1282, 1284, 1287, 1290,
		 1292, 1294, 1295, 1297, 1299, 1301, 1304, 1307,
		 1308, 1311, 1314, 1316, 1318, 1320, 1321, 1324,
		 1328, 1331, 1332, 1334, 1336, 1338, 1340, 1344,
		 1346, 1349, 1350, 1352, 1354, 1357, 1358, 1360,
		 1364, 1367, 1370, 1371, 1373, 1375, 1378, 1382,
		 1384, 1389, 1390, 1393, 1395, 1398, 1400, 1404,
		 1406, 1407, 1410, 1413, 1417, 1419, 1421, 1423,
		 1425, 1428, 1431, 1433, 1436, 1440, 1444, 1446,
		 1448, 1452, 1455, 1459, 1462, 1464, 1465, 1468,
		 1472, 1478, 1480, 1482, 1485, 1489, 1490, 1494,
		 1498, 1501, 1504, 1508, 1512, 1514, 1519, 1520,
		 1525, 1529, 1536, 1537, 1539, 1543, 1549, 1554,
		 1557, 1562, 1564, 1567, 1570, 1576, 1580, 1583,
		 1584, 1590, 1599, 1608, 1609, 1608, 1610, 1616,
		 1623, 1629, 1638, 1642, 1648, 1649, 1655, 1662,
		 1675, 1678, 1680, 1682, 1692, 1704, 1715, 1722,
		 1726, 1731, 1736, 1742, 1747, 1753, 1759, 1764,
		 1770, 1776, 1781, 1787, 1792, 1798, 1804, 1809,
		/* B */
		 1023, 1023, 1026, 1030, 1032, 1035, 1036, 1038,
		 1040, 1042, 1043, 1045, 1047, 1050, 1052, 1055,
		 1057, 1059, 1061, 1064, 1065, 1067, 1070, 1073,
		 1074, 1074, 1078, 1080, 1082, 1084, 1087, 1087,
		 1088, 1092, 1094, 1095, 1098, 1100, 1102, 1103,
		 1106, 1107, 1109, 1111, 1113, 1116, 1118, 1119,
		 1121, 1124, 1126, 1127, 1129, 1131, 1134, 1136,
		 1137, 1138, 1140, 1142, 1145, 1148, 1150, 1151,
		 1153, 1156, 1158, 1160, 1161, 1164, 1165, 1166,
		 1168, 1170, 1173, 1175, 1176, 1176, 1178, 1181,
		 1182, 1185, 1187, 1188, 1189, 1192, 1195, 1197,
		 1197, 1197, 1199, 1201, 1204, 1205, 1206, 1207,
		 1209, 1213, 1214, 1213, 1214, 1217, 1219, 1220,
		 1220, 1222, 1226, 1227, 1226, 1228, 1231, 1235,
		 1235, 1237, 1237, 1239, 1241, 1242, 1245, 1246,
		 1248, 1250, 1252, 1254, 1256, 1257, 1258, 1260,
		 1263, 1265, 1265, 1266, 1268, 1269, 1270, 1274,
		 1277, 1279, 1279, 1280, 1282, 1284, 1285, 1285,
		 1289, 1293, 1294, 1295, 1297, 1298, 1299, 1303,
		 1306, 1308, 1309, 1311, 1312, 1314, 1317, 1320,
		 1321, 1322, 1324, 1325, 1329, 1332, 1335, 1337,
		 1338, 1339, 1341, 1344, 1347, 1351, 1352, 1356,
		 1358, 1360, 1362, 1364, 1367, 1368, 1370, 1373,
		 1378, 1382, 1382, 1383, 1385, 1388, 1391, 1395,
		 1396, 1396, 1399, 1403, 1408, 1410, 1414, 1414,
		 1418, 1422, 1425, 1423, 1424, 1427, 1432, 1436,
		 1440, 1446, 1447, 1451, 1451, 1454, 1455, 1458,
		 1460, 1468, 1477, 1483, 1480, 1479, 1481, 1484,
		 1490, 1495, 1503, 1506, 1513, 1514, 1518, 1524,
		 1535, 1536, 1531, 1527, 1535, 1549, 1559, 1558,
		 1553, 1547, 1553, 1559, 1565, 1570, 1576, 1582,
		 1587, 1593, 1599, 1605, 1610, 1616, 1622, 1627
	},
	{
		/* lsc - 3 */
		/* R */
		 1025, 1022, 1026, 1031, 1035, 1038, 1042, 1046,
		 1049, 1052, 1058, 1062, 1064, 1066, 1070, 1077,
		 1081, 1084, 1086, 1089, 1093, 1098, 1102, 1105,
		 1108, 1112, 1115, 1118, 1122, 1126, 1130, 1133,
		 1137, 1140, 1142, 1145, 1149, 1152, 1155, 1158,
		 1162, 1165, 1167, 1171, 1175, 1178, 1182, 1185,
		 1189, 1192, 1196, 1201, 1203, 1205, 1207, 1212,
		 1215, 1218, 1222, 1225, 1229, 1232, 1235, 1237,
		 1239, 1243, 1246, 1250, 1252, 1257, 1259, 1262,
		 1265, 1269, 1272, 1275, 1277, 1279, 1282, 1285,
		 1288, 1292, 1296, 1299, 1301, 1304, 1308, 1312,
		 1315, 1317, 1319, 1323, 1327, 1329, 1331, 1335,
		 1339, 1342, 1344, 1346, 1348, 1351, 1355, 1359,
		 1361, 1363, 1365, 1369, 1373, 1376, 1378, 1380,
		 1383, 1386, 1389, 1390, 1394, 1398, 1401, 1403,
		 1406, 1410, 1413, 1414, 1416, 1420, 1424, 1428,
		 1430, 1433, 1436, 1440, 1441, 1445, 1449, 1454,
		 1456, 1460, 1463, 1465, 1464, 1467, 1472, 1477,
		 1480, 1482, 1484, 1488, 1492, 1496, 1498, 1500,
		 1503, 1507, 1510, 1514, 1516, 1520, 1525, 1529,
		 1528, 1531, 1535, 1539, 1540, 1543, 1545, 1548,
		 1551, 1553, 1556, 1560, 1564, 1568, 1570, 1571,
		 1574, 1579, 1580, 1582, 1588, 1593, 1595, 1596,
		 1598, 1602, 1607, 1610, 1612, 1616, 1619, 1622,
		 1624, 1628, 1631, 1633, 1637, 1641, 1646, 1648,
		 1652, 1657, 1661, 1667, 1673, 1676, 1679, 1682,
		 1686, 1691, 1696, 1702, 1706, 1709, 1711, 1714,
		 1717, 1723, 1731, 1735, 1737, 1742, 1750, 1754,
		 1753, 1753, 1759, 1768, 1773, 1776, 1784, 1791,
		 1800, 1805, 1812, 1819, 1831, 1841, 1840, 1849,
		 1862, 1863, 1869, 1875, 1882, 1888, 1894, 1901,
		 1907, 1913, 1920, 1926, 1932, 1938, 1945, 1951,
		/* G */
		 1019, 1024, 1028, 1031, 1035, 1037, 1040, 1044,
		 1047, 1050, 1055, 1059, 1061, 1063, 1067, 1072,
		 1076, 1079, 1082, 1084, 1088, 1091, 1095, 1098,
		 1100, 1104, 1106, 1109, 1112, 1115, 1118, 1120,
		 1123, 1125, 1127, 1130, 1133, 1136, 1139, 1142,
		 1143, 1145, 1147, 1150, 1153, 1156, 1159, 1163,
		 1165, 1166, 1169, 1174, 1177, 1179, 1181, 1184,
		 1187, 1190, 1192, 1195, 1198, 1201, 1203, 1205,
		 1207, 1209, 1212, 1214, 1217, 1220, 1223, 1225,
		 1228, 1231, 1234, 1236, 1237, 1239, 1242, 1246,
		 1248, 1251, 1253, 1255, 1257, 1260, 1264, 1268,
		 1271, 1272, 1272, 1274, 1278, 1281, 1284, 1285,
		 1287, 1290, 1292, 1295, 1297, 1299, 1302, 1306,
		 1308, 1309, 1311, 1315, 1316, 1319, 1321, 1323,
		 1326, 1329, 1331, 1332, 1335, 1337, 1340, 1342,
		 1345, 1348, 1351, 1353, 1354, 1357, 1360, 1363,
		 1366, 1369, 1370, 1372, 1374, 1378, 1381, 1384,
		 1385, 1388, 1392, 1394, 1395, 1396, 1399, 1403,
		 1406, 1407, 1409, 1412, 1416, 1420, 1421, 1423,
		 1427, 1429, 1432, 1435, 1438, 1441, 1444, 1446,
		 1446, 1450, 1453, 1456, 1458, 1461, 1465, 1469,
		 1471, 1472, 1474, 1478, 1482, 1486, 1488, 1489,
		 1492, 1496, 1500, 1505, 1508, 1511, 1514, 1515,
		 1517, 1520, 1526, 1528, 1532, 1537, 1540, 1543,
		 1545, 1548, 1552, 1556, 1560, 1564, 1567, 1569,
		 1574, 1579, 1583, 1587, 1591, 1594, 1598, 1602,
		 1607, 1612, 1616, 1621, 1624, 1626, 1630, 1635,
		 1638, 1643, 1651, 1657, 1662, 1666, 1672, 1678,
		 1683, 1685, 1690, 1694, 1698, 1704, 1713, 1721,
		 1727, 1731, 1730, 1734, 1746, 1764, 1774, 1782,
		 1785, 1783, 1790, 1796, 1803, 1809, 1815, 1822,
		 1828, 1834, 1841, 1847, 1853, 1860, 1866, 1872,
		/* B */
		 1018, 1025, 1026, 1029, 1032, 1034, 1035, 1038,
		 1040, 1043, 1046, 1048, 1049, 1052, 1054, 1057,
		 1060, 1062, 1065, 1066, 1069, 1072, 1074, 1076,
		 1078, 1082, 1085, 1087, 1088, 1089, 1092, 1095,
		 1098, 1099, 1100, 1103, 1105, 1106, 1108, 1111,
		 1112, 1113, 1115, 1118, 1120, 1122, 1124, 1126,
		 1126, 1128, 1129, 1134, 1135, 1137, 1137, 1140,
		 1142, 1144, 1145, 1147, 1148, 1151, 1153, 1154,
		 1155, 1158, 1158, 1160, 1161, 1165, 1166, 1168,
		 1170, 1172, 1174, 1176, 1176, 1177, 1180, 1181,
		 1183, 1186, 1188, 1189, 1190, 1192, 1195, 1199,
		 1200, 1201, 1200, 1201, 1205, 1208, 1209, 1210,
		 1212, 1214, 1215, 1216, 1218, 1219, 1222, 1225,
		 1225, 1226, 1228, 1230, 1230, 1233, 1234, 1236,
		 1238, 1239, 1241, 1242, 1244, 1245, 1246, 1247,
		 1250, 1253, 1256, 1257, 1258, 1260, 1261, 1261,
		 1264, 1267, 1268, 1268, 1271, 1274, 1277, 1279,
		 1281, 1282, 1283, 1285, 1285, 1287, 1288, 1290,
		 1292, 1293, 1295, 1297, 1300, 1303, 1305, 1306,
		 1309, 1309, 1310, 1312, 1316, 1319, 1321, 1321,
		 1322, 1325, 1328, 1330, 1330, 1332, 1335, 1338,
		 1340, 1342, 1345, 1348, 1350, 1350, 1350, 1353,
		 1356, 1361, 1364, 1366, 1369, 1370, 1372, 1373,
		 1375, 1379, 1383, 1382, 1384, 1389, 1392, 1393,
		 1395, 1398, 1400, 1402, 1406, 1410, 1415, 1418,
		 1421, 1422, 1422, 1424, 1426, 1429, 1434, 1435,
		 1437, 1440, 1444, 1448, 1450, 1452, 1458, 1464,
		 1465, 1464, 1469, 1471, 1473, 1475, 1479, 1483,
		 1484, 1486, 1491, 1497, 1502, 1505, 1508, 1512,
		 1520, 1525, 1527, 1532, 1539, 1547, 1552, 1564,
		 1567, 1549, 1555, 1562, 1568, 1574, 1581, 1587,
		 1594, 1600, 1607, 1613, 1620, 1626, 1632, 1639
	},
	{
		/* lsc - 4 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	},
	{
		/* lsc - 5 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	},
	{
		/* lsc - 6 */
		/* R */
		 1021, 1023, 1028, 1034, 1040, 1047, 1053, 1061,
		 1068, 1076, 1083, 1090, 1095, 1102, 1111, 1119,
		 1126, 1132, 1139, 1146, 1153, 1161, 1166, 1172,
		 1178, 1184, 1190, 1197, 1205, 1212, 1217, 1222,
		 1229, 1236, 1241, 1246, 1254, 1261, 1266, 1271,
		 1279, 1286, 1290, 1295, 1303, 1309, 1314, 1319,
		 1325, 1332, 1337, 1342, 1348, 1354, 1361, 1367,
		 1372, 1377, 1383, 1388, 1395, 1401, 1407, 1411,
		 1415, 1422, 1428, 1434, 1439, 1444, 1449, 1456,
		 1463, 1468, 1471, 1476, 1481, 1486, 1492, 1498,
		 1503, 1508, 1513, 1519, 1525, 1530, 1535, 1542,
		 1548, 1550, 1553, 1557, 1563, 1569, 1572, 1577,
		 1585, 1592, 1597, 1601, 1605, 1610, 1617, 1621,
		 1626, 1631, 1636, 1642, 1648, 1653, 1656, 1663,
		 1668, 1673, 1676, 1680, 1684, 1692, 1696, 1700,
		 1703, 1711, 1714, 1719, 1724, 1730, 1735, 1740,
		 1746, 1752, 1756, 1760, 1763, 1768, 1774, 1778,
		 1784, 1787, 1791, 1795, 1802, 1807, 1814, 1820,
		 1823, 1826, 1831, 1836, 1838, 1842, 1847, 1856,
		 1864, 1867, 1870, 1875, 1882, 1886, 1892, 1897,
		 1903, 1907, 1912, 1917, 1921, 1923, 1928, 1935,
		 1940, 1945, 1950, 1956, 1961, 1966, 1971, 1975,
		 1978, 1980, 1981, 1986, 1993, 2001, 2004, 2006,
		 2012, 2019, 2022, 2026, 2030, 2033, 2036, 2042,
		 2044, 2048, 2057, 2064, 2069, 2070, 2075, 2080,
		 2087, 2092, 2099, 2105, 2112, 2117, 2125, 2132,
		 2140, 2145, 2149, 2152, 2157, 2166, 2172, 2184,
		 2193, 2200, 2205, 2205, 2205, 2211, 2227, 2232,
		 2241, 2245, 2249, 2249, 2264, 2278, 2288, 2288,
		 2303, 2318, 2327, 2335, 2341, 2358, 2368, 2377,
		 2375, 2419, 2430, 2442, 2454, 2466, 2477, 2489,
		 2500, 2512, 2524, 2535, 2547, 2558, 2570, 2582,
		/* G */
		 1020, 1025, 1028, 1030, 1032, 1036, 1041, 1045,
		 1049, 1054, 1059, 1062, 1065, 1069, 1073, 1079,
		 1082, 1085, 1088, 1093, 1097, 1102, 1104, 1107,
		 1112, 1117, 1120, 1123, 1126, 1131, 1133, 1137,
		 1140, 1145, 1148, 1151, 1154, 1159, 1162, 1164,
		 1168, 1172, 1175, 1177, 1180, 1183, 1187, 1190,
		 1194, 1197, 1200, 1203, 1207, 1210, 1214, 1217,
		 1220, 1222, 1226, 1228, 1231, 1236, 1240, 1242,
		 1243, 1246, 1250, 1254, 1257, 1259, 1261, 1265,
		 1268, 1271, 1272, 1274, 1278, 1281, 1283, 1287,
		 1290, 1292, 1295, 1299, 1302, 1305, 1308, 1312,
		 1315, 1316, 1317, 1320, 1322, 1325, 1328, 1333,
		 1336, 1339, 1341, 1344, 1347, 1351, 1355, 1358,
		 1361, 1364, 1366, 1370, 1372, 1376, 1378, 1381,
		 1383, 1385, 1387, 1391, 1394, 1398, 1400, 1405,
		 1407, 1410, 1410, 1412, 1416, 1420, 1422, 1424,
		 1427, 1430, 1432, 1433, 1433, 1436, 1441, 1445,
		 1448, 1449, 1450, 1451, 1455, 1458, 1462, 1464,
		 1466, 1469, 1472, 1475, 1477, 1478, 1479, 1482,
		 1487, 1490, 1492, 1495, 1499, 1500, 1503, 1506,
		 1509, 1510, 1514, 1518, 1519, 1519, 1522, 1527,
		 1530, 1533, 1535, 1538, 1542, 1544, 1548, 1554,
		 1557, 1557, 1558, 1563, 1568, 1571, 1571, 1571,
		 1575, 1580, 1582, 1585, 1590, 1595, 1597, 1599,
		 1599, 1601, 1608, 1612, 1614, 1615, 1620, 1623,
		 1629, 1635, 1638, 1640, 1646, 1650, 1654, 1656,
		 1663, 1669, 1673, 1674, 1678, 1687, 1693, 1701,
		 1704, 1709, 1716, 1716, 1713, 1716, 1727, 1733,
		 1743, 1746, 1748, 1747, 1757, 1763, 1768, 1768,
		 1779, 1789, 1790, 1793, 1800, 1824, 1841, 1845,
		 1834, 1864, 1876, 1888, 1900, 1911, 1923, 1935,
		 1947, 1959, 1971, 1983, 1995, 2006, 2018, 2030,
		/* B */
		 1026, 1022, 1021, 1022, 1024, 1027, 1031, 1035,
		 1037, 1039, 1042, 1046, 1048, 1051, 1056, 1060,
		 1063, 1063, 1064, 1069, 1073, 1075, 1076, 1078,
		 1081, 1085, 1089, 1091, 1093, 1097, 1098, 1099,
		 1101, 1107, 1109, 1110, 1112, 1117, 1119, 1120,
		 1123, 1126, 1127, 1129, 1131, 1131, 1134, 1137,
		 1139, 1140, 1143, 1145, 1147, 1150, 1152, 1154,
		 1155, 1156, 1158, 1161, 1161, 1164, 1168, 1171,
		 1170, 1171, 1172, 1175, 1177, 1179, 1180, 1182,
		 1183, 1184, 1185, 1184, 1187, 1190, 1192, 1193,
		 1194, 1195, 1195, 1198, 1200, 1201, 1203, 1205,
		 1206, 1206, 1207, 1208, 1209, 1210, 1212, 1215,
		 1218, 1219, 1221, 1224, 1227, 1228, 1230, 1231,
		 1232, 1234, 1236, 1238, 1240, 1241, 1243, 1246,
		 1248, 1250, 1252, 1253, 1254, 1257, 1257, 1260,
		 1261, 1264, 1265, 1268, 1269, 1273, 1274, 1276,
		 1275, 1278, 1280, 1281, 1279, 1282, 1287, 1290,
		 1291, 1291, 1293, 1294, 1296, 1298, 1302, 1304,
		 1305, 1305, 1307, 1310, 1311, 1312, 1312, 1315,
		 1318, 1318, 1318, 1319, 1322, 1322, 1322, 1325,
		 1328, 1330, 1332, 1336, 1336, 1335, 1335, 1339,
		 1342, 1344, 1346, 1352, 1355, 1354, 1351, 1353,
		 1356, 1358, 1359, 1363, 1366, 1368, 1367, 1367,
		 1371, 1372, 1372, 1375, 1381, 1384, 1385, 1388,
		 1387, 1385, 1387, 1392, 1398, 1400, 1400, 1400,
		 1405, 1408, 1409, 1411, 1413, 1415, 1417, 1423,
		 1431, 1436, 1436, 1436, 1438, 1442, 1445, 1451,
		 1454, 1455, 1457, 1457, 1456, 1456, 1464, 1473,
		 1483, 1484, 1483, 1483, 1493, 1499, 1503, 1502,
		 1506, 1512, 1513, 1512, 1511, 1525, 1548, 1566,
		 1559, 1531, 1543, 1555, 1567, 1579, 1591, 1603,
		 1615, 1627, 1639, 1651, 1663, 1675, 1687, 1699
	},
	{
		/* lsc - 7 */
		/* R */
		 1020, 1025, 1032, 1038, 1043, 1049, 1056, 1063,
		 1069, 1074, 1080, 1086, 1092, 1098, 1104, 1110,
		 1116, 1122, 1129, 1135, 1141, 1146, 1152, 1159,
		 1166, 1171, 1177, 1184, 1190, 1195, 1201, 1207,
		 1213, 1219, 1225, 1230, 1236, 1242, 1248, 1255,
		 1260, 1265, 1270, 1276, 1282, 1287, 1292, 1298,
		 1304, 1310, 1318, 1324, 1330, 1335, 1341, 1348,
		 1353, 1360, 1366, 1372, 1377, 1383, 1388, 1393,
		 1398, 1403, 1408, 1413, 1419, 1424, 1429, 1433,
		 1438, 1442, 1447, 1452, 1457, 1463, 1467, 1471,
		 1476, 1481, 1486, 1491, 1495, 1502, 1507, 1513,
		 1516, 1521, 1525, 1530, 1534, 1539, 1543, 1549,
		 1555, 1561, 1565, 1569, 1573, 1577, 1582, 1587,
		 1591, 1596, 1601, 1606, 1611, 1616, 1621, 1625,
		 1629, 1633, 1638, 1643, 1647, 1651, 1656, 1660,
		 1664, 1669, 1674, 1680, 1685, 1688, 1691, 1694,
		 1699, 1704, 1710, 1714, 1719, 1724, 1729, 1734,
		 1738, 1743, 1745, 1749, 1753, 1759, 1765, 1769,
		 1772, 1777, 1781, 1785, 1787, 1793, 1798, 1804,
		 1808, 1811, 1815, 1823, 1829, 1833, 1835, 1841,
		 1844, 1850, 1854, 1859, 1862, 1867, 1872, 1876,
		 1882, 1889, 1892, 1897, 1900, 1903, 1905, 1913,
		 1920, 1926, 1929, 1932, 1937, 1943, 1948, 1952,
		 1958, 1961, 1966, 1970, 1975, 1978, 1982, 1987,
		 1991, 1995, 1999, 2007, 2015, 2021, 2031, 2034,
		 2037, 2039, 2046, 2052, 2058, 2065, 2070, 2078,
		 2084, 2089, 2094, 2100, 2104, 2107, 2115, 2130,
		 2140, 2150, 2149, 2157, 2163, 2171, 2170, 2182,
		 2190, 2195, 2198, 2208, 2211, 2215, 2220, 2227,
		 2236, 2241, 2251, 2263, 2277, 2284, 2301, 2312,
		 2316, 2316, 2324, 2332, 2340, 2348, 2355, 2363,
		 2371, 2379, 2387, 2395, 2402, 2410, 2418, 2426,
		/* G */
		 1022, 1025, 1028, 1031, 1035, 1039, 1043, 1048,
		 1053, 1056, 1059, 1064, 1067, 1070, 1075, 1081,
		 1085, 1089, 1094, 1098, 1102, 1105, 1109, 1113,
		 1116, 1119, 1123, 1127, 1131, 1133, 1136, 1139,
		 1142, 1145, 1149, 1151, 1155, 1159, 1162, 1166,
		 1169, 1171, 1174, 1177, 1180, 1184, 1187, 1191,
		 1193, 1197, 1201, 1205, 1208, 1210, 1213, 1217,
		 1220, 1223, 1227, 1230, 1233, 1237, 1239, 1242,
		 1245, 1249, 1252, 1255, 1259, 1262, 1264, 1267,
		 1269, 1272, 1275, 1278, 1280, 1282, 1285, 1288,
		 1291, 1294, 1297, 1300, 1303, 1306, 1310, 1314,
		 1316, 1317, 1319, 1322, 1325, 1327, 1330, 1333,
		 1336, 1339, 1342, 1344, 1346, 1348, 1352, 1355,
		 1357, 1360, 1363, 1366, 1369, 1372, 1374, 1377,
		 1378, 1381, 1385, 1388, 1390, 1392, 1395, 1398,
		 1400, 1404, 1407, 1410, 1413, 1415, 1416, 1419,
		 1422, 1425, 1427, 1431, 1434, 1437, 1438, 1442,
		 1445, 1448, 1448, 1451, 1453, 1457, 1461, 1463,
		 1465, 1469, 1471, 1473, 1475, 1479, 1482, 1485,
		 1488, 1491, 1494, 1499, 1501, 1503, 1504, 1508,
		 1510, 1514, 1517, 1519, 1521, 1525, 1529, 1532,
		 1536, 1537, 1539, 1543, 1546, 1549, 1551, 1556,
		 1558, 1562, 1565, 1568, 1572, 1577, 1580, 1581,
		 1585, 1587, 1590, 1594, 1599, 1601, 1604, 1607,
		 1611, 1613, 1616, 1620, 1625, 1626, 1631, 1636,
		 1643, 1646, 1650, 1654, 1660, 1663, 1666, 1670,
		 1675, 1681, 1685, 1689, 1692, 1698, 1702, 1707,
		 1710, 1719, 1725, 1734, 1737, 1740, 1740, 1749,
		 1752, 1756, 1763, 1775, 1774, 1778, 1783, 1788,
		 1792, 1801, 1806, 1810, 1814, 1821, 1830, 1840,
		 1846, 1845, 1853, 1861, 1869, 1877, 1885, 1894,
		 1902, 1910, 1918, 1926, 1934, 1942, 1950, 1958,
		/* B */
		 1017, 1025, 1029, 1031, 1032, 1034, 1037, 1040,
		 1044, 1046, 1048, 1051, 1054, 1057, 1061, 1066,
		 1069, 1072, 1075, 1079, 1082, 1083, 1086, 1090,
		 1093, 1096, 1097, 1100, 1104, 1107, 1109, 1111,
		 1113, 1115, 1117, 1119, 1122, 1124, 1126, 1129,
		 1132, 1133, 1134, 1137, 1139, 1142, 1143, 1146,
		 1148, 1150, 1152, 1154, 1157, 1158, 1161, 1163,
		 1165, 1167, 1169, 1173, 1174, 1177, 1179, 1181,
		 1183, 1186, 1187, 1189, 1192, 1195, 1196, 1197,
		 1199, 1201, 1203, 1204, 1205, 1206, 1208, 1210,
		 1212, 1215, 1216, 1218, 1220, 1223, 1225, 1227,
		 1228, 1229, 1230, 1232, 1234, 1236, 1238, 1240,
		 1242, 1244, 1246, 1247, 1248, 1251, 1255, 1256,
		 1257, 1259, 1260, 1262, 1265, 1268, 1269, 1271,
		 1272, 1274, 1277, 1280, 1280, 1281, 1283, 1286,
		 1287, 1289, 1291, 1295, 1298, 1299, 1301, 1303,
		 1306, 1308, 1309, 1311, 1312, 1314, 1315, 1319,
		 1322, 1324, 1324, 1324, 1325, 1329, 1332, 1332,
		 1333, 1337, 1339, 1340, 1341, 1344, 1345, 1348,
		 1350, 1352, 1354, 1356, 1357, 1359, 1360, 1362,
		 1364, 1368, 1370, 1371, 1372, 1373, 1376, 1379,
		 1382, 1384, 1385, 1387, 1389, 1392, 1394, 1396,
		 1396, 1398, 1399, 1403, 1407, 1411, 1413, 1414,
		 1418, 1420, 1423, 1424, 1425, 1426, 1430, 1434,
		 1436, 1436, 1437, 1438, 1442, 1443, 1448, 1452,
		 1456, 1457, 1459, 1460, 1464, 1465, 1464, 1467,
		 1475, 1482, 1484, 1486, 1489, 1494, 1497, 1499,
		 1500, 1510, 1514, 1519, 1518, 1522, 1521, 1526,
		 1524, 1526, 1530, 1539, 1537, 1543, 1546, 1549,
		 1551, 1557, 1559, 1559, 1564, 1576, 1589, 1594,
		 1592, 1592, 1600, 1608, 1617, 1625, 1633, 1641,
		 1649, 1658, 1666, 1674, 1682, 1690, 1698, 1707
	},
	{
		/* lsc - 8 */
		/* R */
		 1020, 1022, 1028, 1033, 1036, 1039, 1043, 1046,
		 1050, 1055, 1059, 1063, 1066, 1071, 1074, 1078,
		 1081, 1084, 1089, 1093, 1098, 1101, 1105, 1109,
		 1112, 1115, 1118, 1122, 1126, 1129, 1133, 1135,
		 1138, 1141, 1143, 1146, 1150, 1154, 1157, 1160,
		 1162, 1165, 1167, 1169, 1172, 1177, 1181, 1183,
		 1186, 1188, 1192, 1195, 1196, 1200, 1204, 1208,
		 1210, 1212, 1214, 1218, 1222, 1225, 1227, 1230,
		 1234, 1237, 1240, 1242, 1245, 1248, 1251, 1254,
		 1256, 1259, 1262, 1266, 1268, 1270, 1272, 1275,
		 1279, 1282, 1285, 1288, 1291, 1293, 1297, 1300,
		 1303, 1305, 1308, 1311, 1314, 1315, 1319, 1322,
		 1327, 1331, 1334, 1335, 1336, 1339, 1342, 1345,
		 1347, 1350, 1353, 1357, 1359, 1362, 1365, 1370,
		 1372, 1374, 1377, 1380, 1382, 1385, 1388, 1390,
		 1392, 1395, 1400, 1403, 1405, 1406, 1408, 1412,
		 1417, 1421, 1423, 1426, 1429, 1431, 1434, 1437,
		 1441, 1443, 1445, 1446, 1448, 1452, 1454, 1458,
		 1462, 1467, 1469, 1471, 1474, 1477, 1481, 1485,
		 1488, 1492, 1492, 1496, 1499, 1503, 1506, 1510,
		 1512, 1514, 1517, 1521, 1526, 1529, 1531, 1534,
		 1538, 1539, 1542, 1546, 1550, 1553, 1556, 1558,
		 1562, 1568, 1570, 1573, 1576, 1580, 1581, 1585,
		 1591, 1598, 1600, 1603, 1605, 1611, 1613, 1616,
		 1616, 1619, 1626, 1632, 1636, 1637, 1642, 1645,
		 1650, 1655, 1662, 1664, 1666, 1668, 1674, 1682,
		 1686, 1690, 1691, 1698, 1702, 1710, 1715, 1721,
		 1720, 1724, 1734, 1745, 1747, 1749, 1753, 1757,
		 1760, 1761, 1768, 1772, 1780, 1780, 1788, 1795,
		 1806, 1810, 1822, 1831, 1836, 1832, 1836, 1844,
		 1855, 1858, 1863, 1869, 1874, 1880, 1885, 1891,
		 1897, 1902, 1908, 1913, 1919, 1924, 1930, 1935,
		/* G */
		 1024, 1022, 1026, 1030, 1033, 1035, 1037, 1039,
		 1044, 1047, 1049, 1051, 1054, 1056, 1058, 1062,
		 1065, 1068, 1069, 1073, 1076, 1079, 1083, 1085,
		 1088, 1090, 1094, 1097, 1099, 1102, 1105, 1108,
		 1110, 1113, 1115, 1117, 1121, 1124, 1126, 1129,
		 1132, 1134, 1135, 1137, 1139, 1142, 1145, 1146,
		 1148, 1150, 1154, 1156, 1157, 1159, 1163, 1166,
		 1167, 1168, 1169, 1173, 1175, 1178, 1180, 1182,
		 1185, 1187, 1189, 1191, 1193, 1196, 1198, 1200,
		 1202, 1203, 1206, 1209, 1212, 1213, 1214, 1216,
		 1219, 1222, 1224, 1225, 1229, 1231, 1235, 1237,
		 1239, 1239, 1242, 1244, 1246, 1248, 1250, 1252,
		 1256, 1260, 1262, 1262, 1263, 1266, 1269, 1270,
		 1272, 1275, 1278, 1281, 1282, 1284, 1287, 1290,
		 1292, 1294, 1295, 1297, 1299, 1301, 1304, 1307,
		 1308, 1311, 1314, 1316, 1318, 1320, 1321, 1324,
		 1328, 1331, 1332, 1334, 1336, 1338, 1340, 1344,
		 1346, 1349, 1350, 1352, 1354, 1357, 1358, 1360,
		 1364, 1367, 1370, 1371, 1373, 1375, 1378, 1382,
		 1384, 1389, 1390, 1393, 1395, 1398, 1400, 1404,
		 1406, 1407, 1410, 1413, 1417, 1419, 1421, 1423,
		 1425, 1428, 1431, 1433, 1436, 1440, 1444, 1446,
		 1448, 1452, 1455, 1459, 1462, 1464, 1465, 1468,
		 1472, 1478, 1480, 1482, 1485, 1489, 1490, 1494,
		 1498, 1501, 1504, 1508, 1512, 1514, 1519, 1520,
		 1525, 1529, 1536, 1537, 1539, 1543, 1549, 1554,
		 1557, 1562, 1564, 1567, 1570, 1576, 1580, 1583,
		 1584, 1590, 1599, 1608, 1609, 1608, 1610, 1616,
		 1623, 1629, 1638, 1642, 1648, 1649, 1655, 1662,
		 1675, 1678, 1680, 1682, 1692, 1704, 1715, 1722,
		 1726, 1731, 1736, 1742, 1747, 1753, 1759, 1764,
		 1770, 1776, 1781, 1787, 1792, 1798, 1804, 1809,
		/* B */
		 1023, 1023, 1026, 1030, 1032, 1035, 1036, 1038,
		 1040, 1042, 1043, 1045, 1047, 1050, 1052, 1055,
		 1057, 1059, 1061, 1064, 1065, 1067, 1070, 1073,
		 1074, 1074, 1078, 1080, 1082, 1084, 1087, 1087,
		 1088, 1092, 1094, 1095, 1098, 1100, 1102, 1103,
		 1106, 1107, 1109, 1111, 1113, 1116, 1118, 1119,
		 1121, 1124, 1126, 1127, 1129, 1131, 1134, 1136,
		 1137, 1138, 1140, 1142, 1145, 1148, 1150, 1151,
		 1153, 1156, 1158, 1160, 1161, 1164, 1165, 1166,
		 1168, 1170, 1173, 1175, 1176, 1176, 1178, 1181,
		 1182, 1185, 1187, 1188, 1189, 1192, 1195, 1197,
		 1197, 1197, 1199, 1201, 1204, 1205, 1206, 1207,
		 1209, 1213, 1214, 1213, 1214, 1217, 1219, 1220,
		 1220, 1222, 1226, 1227, 1226, 1228, 1231, 1235,
		 1235, 1237, 1237, 1239, 1241, 1242, 1245, 1246,
		 1248, 1250, 1252, 1254, 1256, 1257, 1258, 1260,
		 1263, 1265, 1265, 1266, 1268, 1269, 1270, 1274,
		 1277, 1279, 1279, 1280, 1282, 1284, 1285, 1285,
		 1289, 1293, 1294, 1295, 1297, 1298, 1299, 1303,
		 1306, 1308, 1309, 1311, 1312, 1314, 1317, 1320,
		 1321, 1322, 1324, 1325, 1329, 1332, 1335, 1337,
		 1338, 1339, 1341, 1344, 1347, 1351, 1352, 1356,
		 1358, 1360, 1362, 1364, 1367, 1368, 1370, 1373,
		 1378, 1382, 1382, 1383, 1385, 1388, 1391, 1395,
		 1396, 1396, 1399, 1403, 1408, 1410, 1414, 1414,
		 1418, 1422, 1425, 1423, 1424, 1427, 1432, 1436,
		 1440, 1446, 1447, 1451, 1451, 1454, 1455, 1458,
		 1460, 1468, 1477, 1483, 1480, 1479, 1481, 1484,
		 1490, 1495, 1503, 1506, 1513, 1514, 1518, 1524,
		 1535, 1536, 1531, 1527, 1535, 1549, 1559, 1558,
		 1553, 1547, 1553, 1559, 1565, 1570, 1576, 1582,
		 1587, 1593, 1599, 1605, 1610, 1616, 1622, 1627
	},
	{
		/* lsc - 9 */
		/* R */
		 1025, 1022, 1026, 1031, 1035, 1038, 1042, 1046,
		 1049, 1052, 1058, 1062, 1064, 1066, 1070, 1077,
		 1081, 1084, 1086, 1089, 1093, 1098, 1102, 1105,
		 1108, 1112, 1115, 1118, 1122, 1126, 1130, 1133,
		 1137, 1140, 1142, 1145, 1149, 1152, 1155, 1158,
		 1162, 1165, 1167, 1171, 1175, 1178, 1182, 1185,
		 1189, 1192, 1196, 1201, 1203, 1205, 1207, 1212,
		 1215, 1218, 1222, 1225, 1229, 1232, 1235, 1237,
		 1239, 1243, 1246, 1250, 1252, 1257, 1259, 1262,
		 1265, 1269, 1272, 1275, 1277, 1279, 1282, 1285,
		 1288, 1292, 1296, 1299, 1301, 1304, 1308, 1312,
		 1315, 1317, 1319, 1323, 1327, 1329, 1331, 1335,
		 1339, 1342, 1344, 1346, 1348, 1351, 1355, 1359,
		 1361, 1363, 1365, 1369, 1373, 1376, 1378, 1380,
		 1383, 1386, 1389, 1390, 1394, 1398, 1401, 1403,
		 1406, 1410, 1413, 1414, 1416, 1420, 1424, 1428,
		 1430, 1433, 1436, 1440, 1441, 1445, 1449, 1454,
		 1456, 1460, 1463, 1465, 1464, 1467, 1472, 1477,
		 1480, 1482, 1484, 1488, 1492, 1496, 1498, 1500,
		 1503, 1507, 1510, 1514, 1516, 1520, 1525, 1529,
		 1528, 1531, 1535, 1539, 1540, 1543, 1545, 1548,
		 1551, 1553, 1556, 1560, 1564, 1568, 1570, 1571,
		 1574, 1579, 1580, 1582, 1588, 1593, 1595, 1596,
		 1598, 1602, 1607, 1610, 1612, 1616, 1619, 1622,
		 1624, 1628, 1631, 1633, 1637, 1641, 1646, 1648,
		 1652, 1657, 1661, 1667, 1673, 1676, 1679, 1682,
		 1686, 1691, 1696, 1702, 1706, 1709, 1711, 1714,
		 1717, 1723, 1731, 1735, 1737, 1742, 1750, 1754,
		 1753, 1753, 1759, 1768, 1773, 1776, 1784, 1791,
		 1800, 1805, 1812, 1819, 1831, 1841, 1840, 1849,
		 1862, 1863, 1869, 1875, 1882, 1888, 1894, 1901,
		 1907, 1913, 1920, 1926, 1932, 1938, 1945, 1951,
		/* G */
		 1019, 1024, 1028, 1031, 1035, 1037, 1040, 1044,
		 1047, 1050, 1055, 1059, 1061, 1063, 1067, 1072,
		 1076, 1079, 1082, 1084, 1088, 1091, 1095, 1098,
		 1100, 1104, 1106, 1109, 1112, 1115, 1118, 1120,
		 1123, 1125, 1127, 1130, 1133, 1136, 1139, 1142,
		 1143, 1145, 1147, 1150, 1153, 1156, 1159, 1163,
		 1165, 1166, 1169, 1174, 1177, 1179, 1181, 1184,
		 1187, 1190, 1192, 1195, 1198, 1201, 1203, 1205,
		 1207, 1209, 1212, 1214, 1217, 1220, 1223, 1225,
		 1228, 1231, 1234, 1236, 1237, 1239, 1242, 1246,
		 1248, 1251, 1253, 1255, 1257, 1260, 1264, 1268,
		 1271, 1272, 1272, 1274, 1278, 1281, 1284, 1285,
		 1287, 1290, 1292, 1295, 1297, 1299, 1302, 1306,
		 1308, 1309, 1311, 1315, 1316, 1319, 1321, 1323,
		 1326, 1329, 1331, 1332, 1335, 1337, 1340, 1342,
		 1345, 1348, 1351, 1353, 1354, 1357, 1360, 1363,
		 1366, 1369, 1370, 1372, 1374, 1378, 1381, 1384,
		 1385, 1388, 1392, 1394, 1395, 1396, 1399, 1403,
		 1406, 1407, 1409, 1412, 1416, 1420, 1421, 1423,
		 1427, 1429, 1432, 1435, 1438, 1441, 1444, 1446,
		 1446, 1450, 1453, 1456, 1458, 1461, 1465, 1469,
		 1471, 1472, 1474, 1478, 1482, 1486, 1488, 1489,
		 1492, 1496, 1500, 1505, 1508, 1511, 1514, 1515,
		 1517, 1520, 1526, 1528, 1532, 1537, 1540, 1543,
		 1545, 1548, 1552, 1556, 1560, 1564, 1567, 1569,
		 1574, 1579, 1583, 1587, 1591, 1594, 1598, 1602,
		 1607, 1612, 1616, 1621, 1624, 1626, 1630, 1635,
		 1638, 1643, 1651, 1657, 1662, 1666, 1672, 1678,
		 1683, 1685, 1690, 1694, 1698, 1704, 1713, 1721,
		 1727, 1731, 1730, 1734, 1746, 1764, 1774, 1782,
		 1785, 1783, 1790, 1796, 1803, 1809, 1815, 1822,
		 1828, 1834, 1841, 1847, 1853, 1860, 1866, 1872,
		/* B */
		 1018, 1025, 1026, 1029, 1032, 1034, 1035, 1038,
		 1040, 1043, 1046, 1048, 1049, 1052, 1054, 1057,
		 1060, 1062, 1065, 1066, 1069, 1072, 1074, 1076,
		 1078, 1082, 1085, 1087, 1088, 1089, 1092, 1095,
		 1098, 1099, 1100, 1103, 1105, 1106, 1108, 1111,
		 1112, 1113, 1115, 1118, 1120, 1122, 1124, 1126,
		 1126, 1128, 1129, 1134, 1135, 1137, 1137, 1140,
		 1142, 1144, 1145, 1147, 1148, 1151, 1153, 1154,
		 1155, 1158, 1158, 1160, 1161, 1165, 1166, 1168,
		 1170, 1172, 1174, 1176, 1176, 1177, 1180, 1181,
		 1183, 1186, 1188, 1189, 1190, 1192, 1195, 1199,
		 1200, 1201, 1200, 1201, 1205, 1208, 1209, 1210,
		 1212, 1214, 1215, 1216, 1218, 1219, 1222, 1225,
		 1225, 1226, 1228, 1230, 1230, 1233, 1234, 1236,
		 1238, 1239, 1241, 1242, 1244, 1245, 1246, 1247,
		 1250, 1253, 1256, 1257, 1258, 1260, 1261, 1261,
		 1264, 1267, 1268, 1268, 1271, 1274, 1277, 1279,
		 1281, 1282, 1283, 1285, 1285, 1287, 1288, 1290,
		 1292, 1293, 1295, 1297, 1300, 1303, 1305, 1306,
		 1309, 1309, 1310, 1312, 1316, 1319, 1321, 1321,
		 1322, 1325, 1328, 1330, 1330, 1332, 1335, 1338,
		 1340, 1342, 1345, 1348, 1350, 1350, 1350, 1353,
		 1356, 1361, 1364, 1366, 1369, 1370, 1372, 1373,
		 1375, 1379, 1383, 1382, 1384, 1389, 1392, 1393,
		 1395, 1398, 1400, 1402, 1406, 1410, 1415, 1418,
		 1421, 1422, 1422, 1424, 1426, 1429, 1434, 1435,
		 1437, 1440, 1444, 1448, 1450, 1452, 1458, 1464,
		 1465, 1464, 1469, 1471, 1473, 1475, 1479, 1483,
		 1484, 1486, 1491, 1497, 1502, 1505, 1508, 1512,
		 1520, 1525, 1527, 1532, 1539, 1547, 1552, 1564,
		 1567, 1549, 1555, 1562, 1568, 1574, 1581, 1587,
		 1594, 1600, 1607, 1613, 1620, 1626, 1632, 1639
	},
	{
		/* lsc - 10 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	},
	{
		/* lsc - 11 */
		/* R */
		 1016, 1026, 1030, 1033, 1037, 1042, 1047, 1051,
		 1057, 1062, 1067, 1070, 1073, 1078, 1084, 1090,
		 1095, 1099, 1103, 1108, 1111, 1115, 1121, 1126,
		 1130, 1133, 1137, 1141, 1147, 1151, 1155, 1160,
		 1164, 1167, 1171, 1176, 1181, 1185, 1189, 1192,
		 1196, 1200, 1205, 1208, 1213, 1217, 1220, 1224,
		 1230, 1235, 1239, 1242, 1246, 1249, 1255, 1259,
		 1262, 1266, 1270, 1275, 1279, 1283, 1287, 1291,
		 1295, 1300, 1304, 1308, 1311, 1316, 1319, 1321,
		 1324, 1328, 1332, 1335, 1339, 1342, 1346, 1349,
		 1352, 1354, 1357, 1360, 1364, 1368, 1371, 1374,
		 1377, 1381, 1385, 1389, 1390, 1392, 1395, 1401,
		 1404, 1407, 1408, 1410, 1414, 1419, 1422, 1425,
		 1427, 1430, 1434, 1438, 1442, 1446, 1449, 1450,
		 1452, 1455, 1458, 1461, 1465, 1469, 1471, 1474,
		 1479, 1483, 1485, 1487, 1489, 1491, 1495, 1499,
		 1503, 1507, 1509, 1513, 1516, 1519, 1522, 1524,
		 1526, 1530, 1535, 1537, 1539, 1543, 1546, 1549,
		 1552, 1556, 1560, 1562, 1566, 1568, 1570, 1574,
		 1578, 1581, 1582, 1586, 1590, 1595, 1597, 1599,
		 1602, 1606, 1608, 1611, 1613, 1616, 1619, 1625,
		 1629, 1630, 1632, 1638, 1643, 1645, 1646, 1650,
		 1653, 1657, 1659, 1663, 1666, 1669, 1672, 1676,
		 1679, 1682, 1687, 1691, 1692, 1694, 1697, 1699,
		 1701, 1708, 1714, 1718, 1721, 1723, 1728, 1731,
		 1737, 1740, 1739, 1740, 1746, 1756, 1762, 1765,
		 1768, 1770, 1774, 1776, 1781, 1786, 1791, 1796,
		 1802, 1809, 1814, 1816, 1816, 1820, 1826, 1832,
		 1832, 1825, 1830, 1849, 1865, 1867, 1864, 1867,
		 1867, 1875, 1888, 1902, 1904, 1898, 1907, 1914,
		 1928, 1955, 1963, 1971, 1978, 1986, 1994, 2002,
		 2009, 2017, 2025, 2033, 2041, 2048, 2056, 2064,
		/* G */
		 1022, 1024, 1027, 1030, 1032, 1036, 1039, 1041,
		 1045, 1049, 1053, 1056, 1059, 1061, 1064, 1069,
		 1073, 1076, 1078, 1081, 1085, 1088, 1092, 1095,
		 1097, 1100, 1102, 1106, 1109, 1112, 1115, 1118,
		 1121, 1124, 1126, 1129, 1133, 1136, 1138, 1140,
		 1143, 1145, 1149, 1152, 1154, 1156, 1159, 1163,
		 1166, 1169, 1171, 1175, 1176, 1178, 1181, 1184,
		 1187, 1189, 1191, 1194, 1197, 1199, 1202, 1206,
		 1209, 1211, 1213, 1215, 1218, 1222, 1225, 1226,
		 1227, 1230, 1233, 1235, 1237, 1239, 1242, 1245,
		 1247, 1249, 1252, 1254, 1257, 1260, 1264, 1266,
		 1268, 1270, 1272, 1276, 1278, 1280, 1282, 1285,
		 1287, 1289, 1291, 1294, 1296, 1298, 1300, 1303,
		 1305, 1308, 1310, 1312, 1314, 1318, 1321, 1322,
		 1323, 1326, 1328, 1331, 1333, 1337, 1339, 1341,
		 1344, 1348, 1350, 1350, 1351, 1354, 1356, 1359,
		 1362, 1366, 1367, 1370, 1372, 1376, 1379, 1381,
		 1382, 1385, 1389, 1391, 1393, 1395, 1396, 1399,
		 1403, 1406, 1408, 1410, 1414, 1416, 1417, 1420,
		 1424, 1427, 1429, 1433, 1437, 1439, 1440, 1442,
		 1445, 1448, 1450, 1455, 1458, 1460, 1462, 1466,
		 1469, 1470, 1472, 1477, 1481, 1482, 1485, 1488,
		 1491, 1494, 1495, 1498, 1501, 1504, 1507, 1510,
		 1512, 1515, 1518, 1521, 1522, 1525, 1527, 1528,
		 1532, 1537, 1542, 1545, 1549, 1550, 1554, 1557,
		 1563, 1566, 1567, 1568, 1571, 1576, 1581, 1587,
		 1592, 1596, 1600, 1601, 1603, 1604, 1608, 1615,
		 1622, 1627, 1630, 1636, 1637, 1640, 1645, 1650,
		 1651, 1653, 1659, 1668, 1674, 1678, 1682, 1686,
		 1690, 1696, 1705, 1714, 1723, 1726, 1737, 1739,
		 1742, 1763, 1771, 1779, 1787, 1795, 1803, 1811,
		 1819, 1826, 1834, 1842, 1850, 1858, 1866, 1873,
		/* B */
		 1026, 1025, 1028, 1029, 1031, 1034, 1037, 1038,
		 1043, 1046, 1049, 1050, 1053, 1055, 1058, 1063,
		 1066, 1068, 1069, 1073, 1075, 1079, 1082, 1085,
		 1087, 1089, 1092, 1094, 1097, 1099, 1101, 1103,
		 1105, 1109, 1111, 1114, 1118, 1121, 1123, 1124,
		 1127, 1129, 1133, 1135, 1138, 1140, 1141, 1144,
		 1148, 1151, 1154, 1156, 1157, 1159, 1162, 1165,
		 1167, 1170, 1172, 1174, 1178, 1180, 1182, 1184,
		 1186, 1190, 1194, 1195, 1196, 1200, 1203, 1205,
		 1206, 1209, 1212, 1214, 1215, 1216, 1220, 1223,
		 1225, 1228, 1230, 1233, 1235, 1237, 1241, 1243,
		 1245, 1247, 1250, 1253, 1254, 1255, 1257, 1260,
		 1262, 1265, 1266, 1270, 1271, 1274, 1276, 1278,
		 1279, 1280, 1283, 1286, 1288, 1291, 1295, 1297,
		 1298, 1300, 1303, 1305, 1307, 1310, 1312, 1314,
		 1317, 1320, 1322, 1323, 1324, 1325, 1327, 1329,
		 1333, 1336, 1337, 1338, 1340, 1343, 1344, 1347,
		 1349, 1352, 1354, 1356, 1357, 1359, 1361, 1363,
		 1365, 1368, 1369, 1370, 1372, 1375, 1377, 1380,
		 1383, 1386, 1386, 1389, 1391, 1392, 1392, 1394,
		 1397, 1399, 1401, 1404, 1407, 1410, 1411, 1414,
		 1417, 1419, 1421, 1425, 1428, 1431, 1432, 1437,
		 1438, 1440, 1441, 1445, 1448, 1451, 1454, 1459,
		 1460, 1462, 1465, 1470, 1470, 1472, 1473, 1476,
		 1479, 1484, 1489, 1491, 1496, 1497, 1501, 1503,
		 1508, 1511, 1513, 1515, 1519, 1523, 1525, 1530,
		 1537, 1542, 1544, 1545, 1550, 1553, 1556, 1561,
		 1565, 1567, 1570, 1577, 1583, 1588, 1590, 1593,
		 1596, 1599, 1599, 1603, 1607, 1615, 1622, 1633,
		 1638, 1641, 1647, 1656, 1668, 1675, 1681, 1670,
		 1659, 1674, 1682, 1690, 1698, 1706, 1714, 1721,
		 1729, 1737, 1745, 1753, 1761, 1769, 1777, 1785
	}
	},
	.linear_tbl = {
		/* R */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1344, 1360, 1376, 1392,
		 1408, 1424, 1440, 1456, 1472, 1488, 1504, 1520,
		 1536, 1552, 1568, 1584, 1600, 1616, 1632, 1648,
		 1664, 1680, 1696, 1712, 1728, 1744, 1760, 1776,
		 1792, 1808, 1824, 1840, 1856, 1872, 1888, 1904,
		 1920, 1936, 1952, 1968, 1984, 2000, 2016, 2032,
		 2048, 2064, 2080, 2096, 2112, 2128, 2144, 2160,
		 2176, 2192, 2208, 2224, 2240, 2256, 2272, 2288,
		 2304, 2320, 2336, 2352, 2368, 2384, 2400, 2416,
		 2432, 2448, 2464, 2480, 2496, 2512, 2528, 2544,
		 2560, 2576, 2592, 2608, 2624, 2640, 2656, 2672,
		 2688, 2704, 2720, 2736, 2752, 2768, 2784, 2800,
		 2816, 2832, 2848, 2864, 2880, 2896, 2912, 2928,
		 2944, 2960, 2976, 2992, 3008, 3024, 3040, 3056,
		 3072, 3088, 3104, 3120, 3136, 3152, 3168, 3184,
		 3200, 3216, 3232, 3248, 3264, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* G */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* B */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080
	},
	.disc_tbl = {
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0
	},
	.isp_cem_table = {
		0x40, 0x3F, 0x48, 0x3F, 0x40, 0x47, 0x48, 0x47,
		0x50, 0x3F, 0x58, 0x3F, 0x50, 0x47, 0x58, 0x47,
		0x60, 0x3F, 0x68, 0x3F, 0x60, 0x47, 0x68, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x4F, 0x48, 0x4F, 0x40, 0x57, 0x48, 0x57,
		0x50, 0x4F, 0x58, 0x4F, 0x50, 0x57, 0x58, 0x57,
		0x60, 0x4F, 0x68, 0x4F, 0x60, 0x57, 0x68, 0x57,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x5F, 0x48, 0x5F, 0x40, 0x67, 0x48, 0x67,
		0x50, 0x5F, 0x58, 0x5F, 0x50, 0x67, 0x58, 0x67,
		0x60, 0x5F, 0x68, 0x5F, 0x60, 0x67, 0x68, 0x67,
		0x70, 0x5F, 0x78, 0x60, 0x70, 0x67, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x6F, 0x48, 0x6F, 0x40, 0x77, 0x48, 0x77,
		0x50, 0x6F, 0x58, 0x6F, 0x50, 0x77, 0x58, 0x77,
		0x60, 0x6F, 0x68, 0x6F, 0x60, 0x77, 0x68, 0x77,
		0x70, 0x6F, 0x78, 0x6F, 0x70, 0x77, 0x78, 0x77,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x7F, 0x48, 0x7F, 0x3F, 0x87, 0x47, 0x87,
		0x50, 0x7F, 0x58, 0x7F, 0x4F, 0x87, 0x57, 0x87,
		0x60, 0x7F, 0x68, 0x7F, 0x5F, 0x87, 0x67, 0x87,
		0x70, 0x7F, 0x78, 0x7F, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x8F, 0x88, 0x8F, 0x80, 0x97, 0x88, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0x9F, 0x88, 0x9F, 0x80, 0xA7, 0x88, 0xA7,
		0x90, 0x9F, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xAF, 0x88, 0xAF, 0x80, 0xB7, 0x88, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x90, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0x35, 0x54, 0x36, 0x4B, 0x3D, 0x53, 0x3E,
		0x5C, 0x38, 0x62, 0x3A, 0x5B, 0x40, 0x63, 0x41,
		0x66, 0x3D, 0x69, 0x3F, 0x67, 0x44, 0x6B, 0x46,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x99, 0x40, 0x90, 0x48, 0x99, 0x48,
		0xA2, 0x41, 0xAC, 0x42, 0xA3, 0x4A, 0xAC, 0x4B,
		0xB5, 0x44, 0xBD, 0x45, 0xB4, 0x4C, 0xBC, 0x4C,
		0xC4, 0x45, 0x00, 0x00, 0xC3, 0x4D, 0x00, 0x00,
		0x49, 0x44, 0x51, 0x46, 0x48, 0x4C, 0x50, 0x4E,
		0x59, 0x47, 0x61, 0x49, 0x58, 0x4F, 0x60, 0x51,
		0x68, 0x4B, 0x6C, 0x4D, 0x67, 0x52, 0x6C, 0x52,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x9A, 0x51, 0x93, 0x52, 0x9F, 0x52,
		0xA3, 0x52, 0xAD, 0x52, 0xAB, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xBE, 0x52, 0xC0, 0x52, 0xCB, 0x52,
		0xC7, 0x52, 0x00, 0x00, 0xD3, 0x52, 0x00, 0x00,
		0x47, 0x54, 0x4E, 0x56, 0x45, 0x5C, 0x4D, 0x5E,
		0x56, 0x57, 0x5B, 0x55, 0x55, 0x5F, 0x5B, 0x5F,
		0x60, 0x52, 0x69, 0x52, 0x5B, 0x58, 0x60, 0x52,
		0x6E, 0x52, 0x74, 0x52, 0x6C, 0x52, 0x71, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x99, 0x52, 0xA9, 0x52, 0xA3, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xC3, 0x52, 0xC7, 0x52, 0xD7, 0x52,
		0xD0, 0x52, 0xDA, 0x52, 0xE5, 0x52, 0xEB, 0x54,
		0xE4, 0x52, 0x00, 0x00, 0xEB, 0x59, 0x00, 0x00,
		0x44, 0x64, 0x4B, 0x65, 0x42, 0x6C, 0x4A, 0x6D,
		0x53, 0x67, 0x5B, 0x68, 0x52, 0x6F, 0x5A, 0x70,
		0x5B, 0x64, 0x5B, 0x5C, 0x5B, 0x6F, 0x5B, 0x6B,
		0x5F, 0x51, 0x6D, 0x4F, 0x5A, 0x63, 0x6D, 0x64,
		0x80, 0x53, 0x97, 0x56, 0x80, 0x6C, 0x95, 0x6D,
		0xAF, 0x57, 0xC5, 0x58, 0xA8, 0x6D, 0xB6, 0x6E,
		0xD7, 0x58, 0xDC, 0x5D, 0xD1, 0x6B, 0xDB, 0x6D,
		0xE5, 0x5F, 0xEB, 0x61, 0xE5, 0x6F, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x75, 0x48, 0x76, 0x3F, 0x81, 0x47, 0x82,
		0x50, 0x77, 0x58, 0x79, 0x4F, 0x83, 0x57, 0x85,
		0x5B, 0x79, 0x5B, 0x79, 0x5B, 0x87, 0x5B, 0x8B,
		0x5C, 0x79, 0x6C, 0x7C, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x8D, 0x80, 0x80, 0x8F, 0x92, 0x92,
		0xA5, 0x80, 0xB6, 0x80, 0xA3, 0x91, 0xB6, 0x92,
		0xD1, 0x80, 0xDB, 0x80, 0xD1, 0x94, 0xDB, 0x92,
		0xE5, 0x80, 0xEB, 0x80, 0xE5, 0x90, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x3F, 0x8D, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5B, 0x91, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x5F, 0xA0, 0x6D, 0xA6, 0x5E, 0xB2, 0x71, 0xB6,
		0x83, 0xA7, 0x92, 0xA4, 0x86, 0xC7, 0x97, 0xBD,
		0xA6, 0xA6, 0xBC, 0xA8, 0xA5, 0xB8, 0xB4, 0xB4,
		0xCC, 0xA6, 0xD8, 0xA3, 0xC4, 0xB3, 0xD1, 0xB0,
		0xE5, 0xA1, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBE,
		0x5E, 0xC9, 0x70, 0xD6, 0x5D, 0xE2, 0x74, 0xD9,
		0x87, 0xD9, 0x95, 0xC6, 0x87, 0xD6, 0x94, 0xCF,
		0xA3, 0xC3, 0xB0, 0xC0, 0xA0, 0xCA, 0xAA, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB7, 0x5B, 0xCE, 0x5B, 0xC2, 0x5B, 0xDE,
		0x65, 0xE2, 0x77, 0xDD, 0x69, 0xE4, 0x79, 0xDC,
		0x87, 0xD6, 0x92, 0xD0, 0x87, 0xD6, 0x90, 0xD1,
		0x9C, 0xCB, 0xA5, 0xC7, 0x99, 0xCD, 0xA1, 0xC9,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA9, 0xC5, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xCD, 0x5D, 0xEA, 0x00, 0x00, 0x00, 0x00,
		0x6D, 0xE2, 0x7B, 0xDB, 0x00, 0x00, 0x00, 0x00,
		0x87, 0xD6, 0x8F, 0xD2, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xCE, 0x9F, 0xCA, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC7, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x55, 0x24, 0x5C, 0x24, 0x4D, 0x24, 0x55, 0x24,
		0x64, 0x24, 0x66, 0x24, 0x5D, 0x24, 0x66, 0x24,
		0x64, 0x24, 0x63, 0x24, 0x67, 0x24, 0x64, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8C, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x98, 0x24, 0xA5, 0x24, 0x9C, 0x24, 0xAD, 0x24,
		0xB7, 0x24, 0xC7, 0x29, 0xC3, 0x24, 0xC7, 0x37,
		0xC7, 0x3C, 0xC7, 0x45, 0xC7, 0x44, 0xC7, 0x4C,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x44, 0x24, 0x4C, 0x24, 0x38, 0x24, 0x41, 0x24,
		0x55, 0x24, 0x5F, 0x24, 0x4A, 0x24, 0x55, 0x24,
		0x67, 0x24, 0x64, 0x24, 0x60, 0x22, 0x66, 0x21,
		0x63, 0x24, 0x70, 0x24, 0x64, 0x22, 0x6D, 0x23,
		0x80, 0x24, 0x90, 0x24, 0x80, 0x25, 0x93, 0x26,
		0xA0, 0x24, 0xB7, 0x24, 0xA8, 0x27, 0xC4, 0x28,
		0xC7, 0x31, 0xC7, 0x42, 0xC4, 0x43, 0xC7, 0x4C,
		0xC7, 0x4C, 0xC7, 0x53, 0xC7, 0x54, 0xC7, 0x5A,
		0xC7, 0x58, 0x00, 0x00, 0xC7, 0x5D, 0x00, 0x00,
		0x37, 0x33, 0x37, 0x2A, 0x37, 0x42, 0x37, 0x3C,
		0x3C, 0x24, 0x48, 0x24, 0x37, 0x33, 0x37, 0x26,
		0x54, 0x22, 0x62, 0x1D, 0x42, 0x21, 0x53, 0x20,
		0x63, 0x1E, 0x68, 0x21, 0x6C, 0x35, 0x69, 0x36,
		0x80, 0x27, 0x96, 0x2A, 0x80, 0x3D, 0x96, 0x3F,
		0xB2, 0x2C, 0xBF, 0x44, 0xB0, 0x4A, 0xBC, 0x53,
		0xC4, 0x4E, 0xC7, 0x56, 0xC4, 0x5A, 0xC7, 0x5E,
		0xC7, 0x5C, 0xC7, 0x60, 0xC7, 0x62, 0xC7, 0x65,
		0xC7, 0x62, 0x00, 0x00, 0xC7, 0x67, 0x00, 0x00,
		0x37, 0x4F, 0x37, 0x4B, 0x37, 0x5B, 0x37, 0x5A,
		0x37, 0x46, 0x37, 0x3F, 0x37, 0x57, 0x37, 0x54,
		0x35, 0x31, 0x39, 0x1F, 0x36, 0x4E, 0x34, 0x44,
		0x66, 0x48, 0x73, 0x56, 0x54, 0x52, 0x6F, 0x62,
		0x80, 0x58, 0x96, 0x5A, 0x80, 0x70, 0x93, 0x70,
		0xA5, 0x64, 0xBC, 0x62, 0xA4, 0x71, 0xBA, 0x6D,
		0xC3, 0x64, 0xC7, 0x66, 0xC3, 0x6F, 0xC7, 0x71,
		0xC7, 0x69, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x7B, 0x37, 0x7D,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x80, 0x37, 0x85,
		0x39, 0x68, 0x3F, 0x6A, 0x3E, 0x8B, 0x4D, 0x90,
		0x58, 0x73, 0x6B, 0x7B, 0x5E, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x92, 0x80, 0x81, 0x8F, 0x92, 0x92,
		0xA4, 0x80, 0xBA, 0x80, 0xA4, 0x92, 0xB9, 0x93,
		0xC3, 0x80, 0xC7, 0x80, 0xC3, 0x90, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x8D, 0x37, 0x92, 0x37, 0x9A, 0x37, 0x9E,
		0x37, 0x97, 0x37, 0x9C, 0x37, 0xA3, 0x37, 0xAB,
		0x3F, 0xA0, 0x4D, 0xA1, 0x3F, 0xB0, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA3, 0x5D, 0xB6, 0x72, 0xBC,
		0x86, 0xA9, 0x93, 0xA4, 0x8B, 0xC8, 0x98, 0xBC,
		0xA4, 0xA4, 0xBA, 0xA6, 0xA6, 0xB8, 0xB9, 0xB9,
		0xC3, 0xA1, 0xC7, 0x9C, 0xC3, 0xB2, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA3, 0x37, 0xA8, 0x37, 0xAC, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x3E, 0xC1, 0x4A, 0xC6, 0x3E, 0xD2, 0x51, 0xD3,
		0x5F, 0xCB, 0x75, 0xD4, 0x66, 0xD5, 0x79, 0xD8,
		0x8F, 0xDF, 0x9D, 0xD2, 0x8F, 0xDB, 0x9B, 0xD7,
		0xAA, 0xCD, 0xB8, 0xCB, 0xA8, 0xD5, 0xB4, 0xD4,
		0xC3, 0xC3, 0xC7, 0xB9, 0xC3, 0xD3, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x40, 0xDA,
		0x45, 0xDA, 0x59, 0xDA, 0x51, 0xDA, 0x61, 0xDA,
		0x6C, 0xDA, 0x7D, 0xDA, 0x71, 0xDA, 0x7F, 0xDA,
		0x8E, 0xDA, 0x99, 0xDA, 0x8E, 0xDA, 0x98, 0xDA,
		0xA5, 0xDA, 0xB1, 0xDA, 0xA2, 0xDA, 0xAC, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB6, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x49, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x58, 0xDA, 0x67, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x74, 0xDA, 0x81, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xDA, 0x96, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x9F, 0xDA, 0xA8, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xDA, 0xBA, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x37,
		0x68, 0x33, 0x6A, 0x32, 0x63, 0x35, 0x6A, 0x32,
		0x69, 0x32, 0x68, 0x33, 0x6A, 0x32, 0x69, 0x32,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8C, 0x21, 0x80, 0x27, 0x8E, 0x20,
		0x9B, 0x1A, 0xA3, 0x29, 0x9F, 0x18, 0xA3, 0x38,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3C, 0x58, 0x3A, 0x4D, 0x3F, 0x52, 0x3D,
		0x5E, 0x37, 0x64, 0x34, 0x57, 0x3B, 0x5E, 0x37,
		0x6B, 0x31, 0x69, 0x32, 0x66, 0x34, 0x6A, 0x32,
		0x68, 0x33, 0x72, 0x2E, 0x69, 0x32, 0x6F, 0x2F,
		0x80, 0x27, 0x91, 0x1F, 0x80, 0x2C, 0x92, 0x29,
		0xA1, 0x22, 0xA3, 0x45, 0xA1, 0x37, 0xA3, 0x52,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x46, 0x43, 0x4A, 0x41, 0x3E, 0x47, 0x41, 0x45,
		0x50, 0x3E, 0x56, 0x3B, 0x46, 0x43, 0x4D, 0x41,
		0x5E, 0x37, 0x68, 0x33, 0x56, 0x3F, 0x61, 0x3F,
		0x6A, 0x36, 0x6E, 0x39, 0x6D, 0x3B, 0x6C, 0x42,
		0x80, 0x36, 0x94, 0x33, 0x80, 0x45, 0x91, 0x4D,
		0xA1, 0x49, 0xA3, 0x5E, 0xA0, 0x5B, 0xA3, 0x66,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x33, 0x4C, 0x36, 0x4B, 0x26, 0x52, 0x28, 0x51,
		0x3E, 0x4B, 0x46, 0x4C, 0x30, 0x53, 0x3A, 0x55,
		0x4D, 0x4B, 0x58, 0x4A, 0x45, 0x58, 0x5C, 0x63,
		0x68, 0x4E, 0x73, 0x57, 0x68, 0x67, 0x73, 0x69,
		0x80, 0x5C, 0x91, 0x63, 0x80, 0x6F, 0x91, 0x73,
		0xA0, 0x68, 0xA3, 0x6E, 0xA0, 0x72, 0xA3, 0x74,
		0xA3, 0x71, 0xA8, 0x71, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x5C, 0x13, 0x5C, 0x13, 0x78, 0x13, 0x7C,
		0x1B, 0x5E, 0x26, 0x62, 0x1D, 0x80, 0x2B, 0x86,
		0x32, 0x66, 0x4B, 0x6E, 0x3B, 0x8C, 0x4F, 0x8F,
		0x5B, 0x75, 0x72, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x91, 0x80, 0x82, 0x92, 0x91, 0x91,
		0xA0, 0x80, 0xA3, 0x80, 0xA0, 0x90, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x93, 0x13, 0x9B, 0x13, 0xA7, 0x14, 0xAC,
		0x1F, 0x9F, 0x2E, 0x9F, 0x24, 0xAC, 0x34, 0xAC,
		0x3D, 0xA0, 0x4E, 0xA0, 0x44, 0xAC, 0x53, 0xAC,
		0x5E, 0xA1, 0x71, 0xA2, 0x63, 0xAC, 0x76, 0xAC,
		0x86, 0xA6, 0x93, 0xA2, 0x87, 0xAC, 0x92, 0xAC,
		0xA0, 0xA0, 0xA3, 0x97, 0x9E, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x24, 0xAC, 0x30, 0xAC, 0x37, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x67, 0xAC,
		0x6C, 0xAC, 0x7A, 0xAC, 0x72, 0xAC, 0x7C, 0xAC,
		0x87, 0xAC, 0x8F, 0xAC, 0x87, 0xAC, 0x8D, 0xAC,
		0x98, 0xAC, 0xA1, 0xAC, 0x95, 0xAC, 0x9C, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x91, 0x50, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x71, 0x61,
		0x75, 0x5D, 0x78, 0x60, 0x78, 0x64, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x5D, 0x6A,
		0x67, 0x66, 0x6E, 0x68, 0x64, 0x6D, 0x6C, 0x70,
		0x76, 0x6B, 0x7A, 0x6E, 0x73, 0x73, 0x7B, 0x75,
		0x80, 0x70, 0x89, 0x70, 0x80, 0x78, 0x89, 0x79,
		0x92, 0x72, 0x99, 0x73, 0x90, 0x79, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x3F, 0x7B, 0x47, 0x7E,
		0x52, 0x70, 0x5A, 0x73, 0x4F, 0x80, 0x57, 0x83,
		0x61, 0x75, 0x69, 0x78, 0x5F, 0x85, 0x67, 0x87,
		0x70, 0x7A, 0x78, 0x7D, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x81, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8B, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x7A, 0x98,
		0x82, 0x8F, 0x88, 0x8F, 0x83, 0x97, 0x89, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x45, 0x3B, 0x4C, 0x3B, 0x44, 0x43, 0x4C, 0x43,
		0x54, 0x3C, 0x5C, 0x3D, 0x54, 0x44, 0x5C, 0x45,
		0x62, 0x3E, 0x68, 0x3F, 0x63, 0x46, 0x69, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA1, 0x40, 0xA9, 0x41, 0xA1, 0x48, 0xA9, 0x49,
		0xB2, 0x41, 0xBA, 0x42, 0xB2, 0x49, 0xB9, 0x49,
		0xC1, 0x42, 0x00, 0x00, 0xC1, 0x4A, 0x00, 0x00,
		0x43, 0x4B, 0x4B, 0x4B, 0x43, 0x53, 0x4B, 0x53,
		0x53, 0x4C, 0x5B, 0x4D, 0x53, 0x54, 0x5B, 0x55,
		0x63, 0x4D, 0x69, 0x4F, 0x63, 0x55, 0x6A, 0x56,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x99, 0x58,
		0xA1, 0x51, 0xA9, 0x51, 0xA1, 0x59, 0xA9, 0x59,
		0xB1, 0x51, 0xB9, 0x51, 0xB1, 0x59, 0xB9, 0x59,
		0xC0, 0x51, 0x00, 0x00, 0xC0, 0x59, 0x00, 0x00,
		0x42, 0x5B, 0x4A, 0x5B, 0x41, 0x63, 0x49, 0x63,
		0x52, 0x5C, 0x5A, 0x5D, 0x51, 0x64, 0x59, 0x65,
		0x62, 0x5D, 0x6A, 0x5E, 0x61, 0x65, 0x69, 0x66,
		0x71, 0x5F, 0x78, 0x60, 0x71, 0x66, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x99, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA1, 0x61, 0xA9, 0x61, 0xA0, 0x69, 0xA8, 0x68,
		0xB0, 0x61, 0xB8, 0x61, 0xB0, 0x68, 0xB8, 0x68,
		0xC0, 0x61, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x41, 0x6B, 0x49, 0x6B, 0x40, 0x73, 0x48, 0x73,
		0x51, 0x6C, 0x59, 0x6D, 0x50, 0x74, 0x58, 0x75,
		0x61, 0x6D, 0x69, 0x6E, 0x60, 0x75, 0x68, 0x76,
		0x6D, 0x6A, 0x75, 0x69, 0x6D, 0x75, 0x6E, 0x6B,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x6B, 0x94, 0x6C,
		0x98, 0x69, 0xA4, 0x69, 0xA7, 0x6D, 0xB8, 0x6D,
		0xAF, 0x69, 0xBA, 0x69, 0xC9, 0x6D, 0xDC, 0x6D,
		0xC5, 0x69, 0xCF, 0x69, 0xE8, 0x6E, 0xF5, 0x6F,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x7B, 0x48, 0x7C, 0x3F, 0x85, 0x47, 0x85,
		0x50, 0x7C, 0x58, 0x7D, 0x4F, 0x86, 0x57, 0x86,
		0x60, 0x7D, 0x68, 0x7E, 0x5F, 0x87, 0x67, 0x87,
		0x6D, 0x7E, 0x6E, 0x7E, 0x6D, 0x89, 0x6F, 0x90,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x93, 0x92, 0x92,
		0xA3, 0x80, 0xB7, 0x80, 0xA4, 0x92, 0xB2, 0x90,
		0xCA, 0x80, 0xD4, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x6F, 0xA2, 0x6D, 0x9B, 0x70, 0xB1,
		0x81, 0xAA, 0x91, 0xA1, 0x81, 0xAC, 0x8D, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA5, 0x74, 0xB2, 0x6D, 0xAF, 0x77, 0xB0,
		0x81, 0xAC, 0x8A, 0xA7, 0x81, 0xAC, 0x89, 0xA8,
		0x92, 0xA3, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6F, 0xB4, 0x79, 0xB0, 0x71, 0xB8, 0x79, 0xB8,
		0x81, 0xAF, 0x89, 0xAF, 0x81, 0xB7, 0x89, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x91, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x69, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x71, 0xBF, 0x79, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x82, 0xBF, 0x89, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x54, 0x30, 0x5B, 0x33, 0x51, 0x38, 0x59, 0x3A,
		0x63, 0x35, 0x67, 0x38, 0x60, 0x3B, 0x67, 0x3B,
		0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x8A, 0x3B,
		0x92, 0x3B, 0x9B, 0x3B, 0x94, 0x3B, 0xA1, 0x3B,
		0xA8, 0x3B, 0xB5, 0x3B, 0xB0, 0x3B, 0xBF, 0x3B,
		0xC4, 0x3B, 0xCE, 0x3B, 0xCC, 0x3B, 0xD8, 0x3B,
		0xD7, 0x3B, 0x00, 0x00, 0xD9, 0x42, 0x00, 0x00,
		0x4C, 0x3B, 0x52, 0x3B, 0x49, 0x43, 0x49, 0x3B,
		0x5A, 0x3B, 0x61, 0x3B, 0x51, 0x3B, 0x5A, 0x3B,
		0x68, 0x3B, 0x69, 0x3B, 0x63, 0x3B, 0x69, 0x3B,
		0x6A, 0x3B, 0x74, 0x3B, 0x6A, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8E, 0x3B,
		0x98, 0x3B, 0xA8, 0x3B, 0x9E, 0x3B, 0xB2, 0x3B,
		0xBA, 0x3B, 0xCA, 0x3B, 0xC8, 0x3B, 0xD8, 0x3B,
		0xD8, 0x3B, 0xD9, 0x44, 0xD9, 0x45, 0xD9, 0x4E,
		0xD9, 0x4B, 0x00, 0x00, 0xD9, 0x52, 0x00, 0x00,
		0x49, 0x4E, 0x49, 0x48, 0x49, 0x57, 0x49, 0x53,
		0x49, 0x40, 0x4F, 0x3B, 0x49, 0x4E, 0x49, 0x45,
		0x5A, 0x3B, 0x65, 0x3A, 0x4C, 0x3B, 0x58, 0x39,
		0x68, 0x3B, 0x6E, 0x3B, 0x67, 0x36, 0x69, 0x37,
		0x80, 0x3C, 0x91, 0x3C, 0x80, 0x3D, 0x96, 0x40,
		0xA7, 0x3D, 0xC2, 0x3D, 0xB4, 0x42, 0xCD, 0x43,
		0xD6, 0x3D, 0xD7, 0x49, 0xCE, 0x52, 0xD4, 0x56,
		0xD9, 0x50, 0xD9, 0x55, 0xD9, 0x59, 0xD9, 0x5D,
		0xD9, 0x59, 0x00, 0x00, 0xD9, 0x60, 0x00, 0x00,
		0x46, 0x5F, 0x49, 0x5E, 0x44, 0x67, 0x49, 0x68,
		0x49, 0x5A, 0x49, 0x55, 0x49, 0x66, 0x49, 0x64,
		0x49, 0x4E, 0x48, 0x3E, 0x49, 0x60, 0x48, 0x59,
		0x58, 0x39, 0x71, 0x53, 0x48, 0x4C, 0x6F, 0x62,
		0x80, 0x57, 0x96, 0x58, 0x80, 0x6A, 0x97, 0x6C,
		0xB1, 0x5A, 0xBC, 0x60, 0xA4, 0x70, 0xBB, 0x6D,
		0xCD, 0x5F, 0xD4, 0x61, 0xCC, 0x6C, 0xD4, 0x6F,
		0xD9, 0x63, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x42, 0x6F, 0x49, 0x71, 0x3F, 0x7E, 0x47, 0x80,
		0x49, 0x71, 0x49, 0x71, 0x49, 0x82, 0x49, 0x85,
		0x49, 0x71, 0x4B, 0x72, 0x49, 0x8A, 0x4F, 0x8F,
		0x51, 0x73, 0x6B, 0x7A, 0x5D, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8A, 0x8F, 0x8F,
		0xA3, 0x80, 0xBB, 0x80, 0xA3, 0x91, 0xBA, 0x93,
		0xCC, 0x80, 0xD4, 0x80, 0xCB, 0x92, 0xD4, 0x90,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x3F, 0x8C, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x49, 0x91, 0x49, 0x95, 0x49, 0x9A, 0x49, 0xA0,
		0x49, 0x9A, 0x4F, 0xA0, 0x49, 0xA8, 0x4E, 0xB1,
		0x5C, 0xA3, 0x6E, 0xA7, 0x5A, 0xBA, 0x72, 0xB7,
		0x85, 0xAD, 0x93, 0xA5, 0x88, 0xBD, 0x95, 0xB6,
		0xA3, 0xA3, 0xBA, 0xA7, 0xA4, 0xB6, 0xBA, 0xBA,
		0xCB, 0xA5, 0xD4, 0xA1, 0xCC, 0xB9, 0xD4, 0xB2,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x4E, 0xC2, 0x49, 0xC3, 0x4D, 0xD9,
		0x5A, 0xD3, 0x72, 0xDA, 0x60, 0xE2, 0x75, 0xEB,
		0x8D, 0xE9, 0x9D, 0xD8, 0x8F, 0xF7, 0x9E, 0xE9,
		0xAB, 0xD0, 0xB9, 0xCC, 0xAB, 0xE0, 0xB6, 0xD8,
		0xCB, 0xCB, 0xD4, 0xC3, 0xC5, 0xD6, 0xD4, 0xD4,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCC,
		0x49, 0xD3, 0x50, 0xEA, 0x49, 0xE6, 0x57, 0xF0,
		0x66, 0xEA, 0x79, 0xEF, 0x6A, 0xF1, 0x7C, 0xF1,
		0x8F, 0xF4, 0x9C, 0xEE, 0x8E, 0xF2, 0x9B, 0xF1,
		0xAA, 0xE9, 0xB6, 0xE4, 0xA8, 0xF1, 0xB4, 0xED,
		0xC1, 0xE2, 0xCF, 0xDF, 0xBE, 0xE8, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD9, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0xF1, 0x5E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x6E, 0xF1, 0x7E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xF1, 0x99, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xF1, 0xB0, 0xEF, 0x00, 0x00, 0x00, 0x00,
		0xB9, 0xEA, 0xC1, 0xE6, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0x13, 0x54, 0x0F, 0x46, 0x16, 0x4D, 0x13,
		0x5D, 0x0D, 0x60, 0x0D, 0x55, 0x0F, 0x60, 0x0D,
		0x5E, 0x0D, 0x5C, 0x0D, 0x60, 0x0D, 0x5E, 0x0D,
		0x63, 0x0D, 0x71, 0x0D, 0x5F, 0x0D, 0x6F, 0x0E,
		0x80, 0x0D, 0x8F, 0x0D, 0x80, 0x0E, 0x91, 0x0E,
		0x9E, 0x0D, 0xAE, 0x0D, 0xA2, 0x0E, 0xB5, 0x15,
		0xB5, 0x27, 0xB5, 0x3F, 0xB5, 0x37, 0xB5, 0x49,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x3D, 0x1A, 0x45, 0x17, 0x34, 0x1F, 0x3B, 0x1C,
		0x4D, 0x13, 0x57, 0x0E, 0x43, 0x18, 0x4C, 0x12,
		0x61, 0x0D, 0x5D, 0x0C, 0x58, 0x0C, 0x60, 0x0F,
		0x5B, 0x0C, 0x6D, 0x0E, 0x5D, 0x09, 0x69, 0x0D,
		0x80, 0x11, 0x93, 0x12, 0x80, 0x13, 0x96, 0x16,
		0xA6, 0x13, 0xB3, 0x2B, 0xAF, 0x18, 0xB3, 0x3E,
		0xB5, 0x44, 0xB5, 0x52, 0xB5, 0x50, 0xB5, 0x59,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x29, 0x24, 0x2F, 0x21, 0x25, 0x32, 0x25, 0x2B,
		0x37, 0x1D, 0x3F, 0x16, 0x29, 0x24, 0x30, 0x1E,
		0x4A, 0x0D, 0x5C, 0x0C, 0x38, 0x12, 0x55, 0x24,
		0x5E, 0x0D, 0x66, 0x19, 0x6C, 0x34, 0x6C, 0x42,
		0x80, 0x21, 0x98, 0x25, 0x80, 0x45, 0x94, 0x47,
		0xAC, 0x37, 0xB3, 0x50, 0xAB, 0x50, 0xB3, 0x5B,
		0xB5, 0x59, 0xB5, 0x60, 0xB5, 0x62, 0xB5, 0x67,
		0xB5, 0x65, 0xBA, 0x65, 0xB5, 0x6A, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x25, 0x43, 0x25, 0x3E, 0x25, 0x52, 0x25, 0x50,
		0x25, 0x38, 0x24, 0x2D, 0x25, 0x4D, 0x25, 0x49,
		0x23, 0x1E, 0x42, 0x2C, 0x23, 0x41, 0x34, 0x44,
		0x68, 0x4D, 0x71, 0x4F, 0x58, 0x55, 0x74, 0x6E,
		0x80, 0x55, 0x94, 0x5E, 0x80, 0x74, 0x93, 0x71,
		0xAA, 0x61, 0xB2, 0x67, 0xA9, 0x6F, 0xB2, 0x70,
		0xB5, 0x6A, 0xB5, 0x6C, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6F, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x62, 0x25, 0x62, 0x25, 0x79, 0x25, 0x7D,
		0x25, 0x62, 0x28, 0x63, 0x25, 0x80, 0x2C, 0x86,
		0x2C, 0x64, 0x3F, 0x6A, 0x3A, 0x8C, 0x4D, 0x90,
		0x5A, 0x73, 0x70, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x92, 0x80, 0x82, 0x96, 0x92, 0x92,
		0xA8, 0x80, 0xB2, 0x80, 0xA8, 0x94, 0xB2, 0x90,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x90, 0x25, 0x96, 0x25, 0xA0, 0x25, 0xA5,
		0x25, 0x9C, 0x2E, 0x9F, 0x25, 0xAC, 0x2E, 0xB0,
		0x3D, 0xA0, 0x4D, 0xA1, 0x3D, 0xB2, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA4, 0x5D, 0xB5, 0x72, 0xBB,
		0x87, 0xAB, 0x94, 0xA4, 0x8B, 0xC6, 0x98, 0xBB,
		0xA7, 0xA7, 0xB2, 0xA1, 0xA5, 0xB8, 0xB2, 0xB2,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAC, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x2F, 0xC0, 0x2E, 0xC3, 0x3C, 0xC3,
		0x3F, 0xC0, 0x4F, 0xC0, 0x49, 0xC3, 0x5A, 0xC3,
		0x63, 0xC1, 0x77, 0xC2, 0x6B, 0xC3, 0x7B, 0xC3,
		0x8B, 0xC3, 0x97, 0xC2, 0x8B, 0xC3, 0x95, 0xC3,
		0xA3, 0xC1, 0xB1, 0xC1, 0x9F, 0xC3, 0xAA, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x50, 0xC3,
		0x54, 0xC3, 0x62, 0xC3, 0x5C, 0xC3, 0x68, 0xC3,
		0x71, 0xC3, 0x7D, 0xC3, 0x74, 0xC3, 0x7F, 0xC3,
		0x8B, 0xC3, 0x93, 0xC3, 0x8B, 0xC3, 0x91, 0xC3,
		0x9C, 0xC3, 0xA5, 0xC3, 0x99, 0xC3, 0xA1, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA8, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x57, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x62, 0xC3, 0x6D, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC3, 0x81, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x8B, 0xC3, 0x91, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xC3, 0x9E, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC3, 0xAB, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x8A, 0x4F,
		0x91, 0x4F, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x6F, 0x5C,
		0x74, 0x5A, 0x76, 0x59, 0x75, 0x59, 0x74, 0x5A,
		0x80, 0x54, 0x8D, 0x4E, 0x80, 0x54, 0x90, 0x51,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x57, 0x67,
		0x63, 0x62, 0x68, 0x5F, 0x5A, 0x66, 0x60, 0x66,
		0x6F, 0x5C, 0x75, 0x5C, 0x68, 0x67, 0x75, 0x69,
		0x80, 0x5D, 0x90, 0x65, 0x80, 0x6E, 0x90, 0x74,
		0x92, 0x72, 0x99, 0x73, 0x91, 0x78, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x31, 0x7A, 0x33, 0x7D,
		0x4A, 0x6E, 0x4F, 0x6F, 0x37, 0x80, 0x2F, 0x86,
		0x56, 0x72, 0x60, 0x75, 0x25, 0x90, 0x4D, 0x90,
		0x6B, 0x79, 0x74, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x90, 0x80, 0x82, 0x91, 0x90, 0x90,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x05, 0x96, 0x28, 0x96, 0x3F, 0x97, 0x47, 0x97,
		0x3B, 0x96, 0x47, 0x96, 0x4F, 0x97, 0x57, 0x97,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x76, 0x96, 0x70, 0x98, 0x7A, 0x98,
		0x83, 0x96, 0x8C, 0x96, 0x83, 0x97, 0x89, 0x97,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_cem_table1 = {
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x5B, 0x52, 0x64, 0x52,
		0x70, 0x50, 0x78, 0x50, 0x6D, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x98, 0x50, 0x92, 0x52, 0x9B, 0x52,
		0xA0, 0x50, 0xA8, 0x50, 0xA4, 0x52, 0xAD, 0x52,
		0xB0, 0x50, 0xB8, 0x50, 0xB6, 0x52, 0xBF, 0x52,
		0xBF, 0x50, 0x00, 0x00, 0xC8, 0x52, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x5B, 0x5B, 0x5D, 0x52, 0x5B, 0x64, 0x5B, 0x5B,
		0x69, 0x52, 0x74, 0x52, 0x61, 0x52, 0x70, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x96, 0x52, 0xA2, 0x52, 0x9E, 0x52, 0xAD, 0x52,
		0xAD, 0x52, 0xB9, 0x52, 0xBC, 0x52, 0xCC, 0x52,
		0xC4, 0x52, 0xCF, 0x52, 0xDB, 0x52, 0xEA, 0x52,
		0xDA, 0x52, 0x00, 0x00, 0xEB, 0x57, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x5B, 0x6D, 0x5B, 0x67, 0x5B, 0x76, 0x5B, 0x73,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xDF, 0x60, 0xEB, 0x61, 0xDF, 0x70, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x5B, 0x80, 0x5B, 0x80, 0x5B, 0x89, 0x5B, 0x8C,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xDF, 0x80, 0xEB, 0x80, 0xDF, 0x8F, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x5B, 0x92, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xDF, 0x9F, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBC,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xD0, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9E, 0xCB, 0xA9, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB6, 0x5B, 0xC8, 0x5B, 0xBF, 0x5B, 0xD4,
		0x60, 0xDF, 0x70, 0xDF, 0x62, 0xE7, 0x72, 0xE0,
		0x80, 0xD9, 0x8D, 0xD2, 0x80, 0xD9, 0x8B, 0xD3,
		0x99, 0xCD, 0xA4, 0xC8, 0x96, 0xCE, 0x9F, 0xCA,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA8, 0xC6, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xC7, 0x5B, 0xE0, 0x00, 0x00, 0x00, 0x00,
		0x66, 0xE5, 0x74, 0xDF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xD9, 0x8A, 0xD4, 0x00, 0x00, 0x00, 0x00,
		0x94, 0xCF, 0x9C, 0xCB, 0x00, 0x00, 0x00, 0x00,
		0xA4, 0xC8, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x37, 0x37, 0x37, 0x2D, 0x37, 0x40, 0x37, 0x37,
		0x3B, 0x24, 0x46, 0x24, 0x37, 0x2B, 0x3E, 0x24,
		0x52, 0x24, 0x5D, 0x24, 0x4B, 0x24, 0x58, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8B, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x96, 0x24, 0xA2, 0x24, 0x9A, 0x24, 0xA7, 0x24,
		0xAD, 0x24, 0xB9, 0x24, 0xB4, 0x24, 0xC1, 0x24,
		0xC4, 0x24, 0xC7, 0x2E, 0xC7, 0x2C, 0xC7, 0x38,
		0xC7, 0x37, 0x00, 0x00, 0xC7, 0x40, 0x00, 0x00,
		0x37, 0x49, 0x37, 0x42, 0x37, 0x52, 0x37, 0x4C,
		0x37, 0x37, 0x37, 0x29, 0x37, 0x43, 0x37, 0x37,
		0x43, 0x24, 0x52, 0x24, 0x40, 0x30, 0x50, 0x30,
		0x61, 0x24, 0x70, 0x24, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x24, 0x8F, 0x24, 0x80, 0x30, 0x8F, 0x30,
		0x9E, 0x24, 0xAD, 0x24, 0x9F, 0x30, 0xAF, 0x30,
		0xBC, 0x24, 0xC7, 0x29, 0xBF, 0x30, 0xC7, 0x38,
		0xC7, 0x38, 0xC7, 0x42, 0xC7, 0x44, 0xC7, 0x4C,
		0xC7, 0x49, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x37, 0x5B, 0x37, 0x56, 0x37, 0x64, 0x37, 0x61,
		0x37, 0x4F, 0x37, 0x46, 0x37, 0x5B, 0x37, 0x54,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xC7, 0x46, 0xBF, 0x50, 0xC7, 0x54,
		0xC7, 0x50, 0xC7, 0x57, 0xC7, 0x5C, 0xC7, 0x61,
		0xC7, 0x5B, 0x00, 0x00, 0xC7, 0x64, 0x00, 0x00,
		0x37, 0x6D, 0x37, 0x6B, 0x37, 0x76, 0x37, 0x75,
		0x37, 0x67, 0x37, 0x63, 0x37, 0x73, 0x37, 0x71,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xC7, 0x63, 0xBF, 0x70, 0xC7, 0x71,
		0xC7, 0x68, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x89, 0x37, 0x8A,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x8C, 0x37, 0x8E,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xC7, 0x80, 0xBF, 0x8F, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x92, 0x37, 0x94, 0x37, 0x9B, 0x37, 0x9E,
		0x37, 0x98, 0x37, 0x9C, 0x37, 0xA4, 0x37, 0xAB,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xC7, 0x9C, 0xBF, 0xAF, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA4, 0x37, 0xA9, 0x37, 0xAD, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x40, 0xBF, 0x50, 0xBF, 0x40, 0xCF, 0x50, 0xCF,
		0x60, 0xBF, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xC7, 0xB9, 0xBF, 0xCF, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x3F, 0xDA,
		0x43, 0xDA, 0x52, 0xDA, 0x4C, 0xDA, 0x59, 0xDA,
		0x62, 0xDA, 0x71, 0xDA, 0x66, 0xDA, 0x73, 0xDA,
		0x80, 0xDA, 0x8F, 0xDA, 0x80, 0xDA, 0x8C, 0xDA,
		0x9E, 0xDA, 0xAD, 0xDA, 0x99, 0xDA, 0xA6, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB3, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x47, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x52, 0xDA, 0x5E, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x69, 0xDA, 0x74, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xDA, 0x8B, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x96, 0xDA, 0xA2, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xAD, 0xDA, 0xB9, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x3E, 0x47, 0x44, 0x44,
		0x4F, 0x3F, 0x55, 0x3C, 0x4A, 0x41, 0x51, 0x3E,
		0x5C, 0x38, 0x63, 0x35, 0x58, 0x3A, 0x60, 0x36,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8B, 0x22, 0x80, 0x27, 0x8D, 0x21,
		0x99, 0x1B, 0xA3, 0x21, 0x9D, 0x19, 0xA3, 0x2D,
		0xA3, 0x38, 0xA8, 0x40, 0xA3, 0x41, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x38, 0x4A, 0x3E, 0x47, 0x30, 0x4E, 0x36, 0x4B,
		0x44, 0x44, 0x4B, 0x41, 0x3C, 0x48, 0x44, 0x44,
		0x53, 0x3D, 0x5C, 0x38, 0x4D, 0x40, 0x56, 0x3B,
		0x66, 0x33, 0x72, 0x2E, 0x62, 0x36, 0x70, 0x30,
		0x80, 0x27, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xA3, 0x38, 0x9F, 0x30, 0xA3, 0x44,
		0xA3, 0x4A, 0xA8, 0x50, 0xA3, 0x53, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x26, 0x53, 0x2C, 0x50, 0x19, 0x59, 0x1F, 0x56,
		0x33, 0x4C, 0x3B, 0x48, 0x26, 0x53, 0x30, 0x50,
		0x44, 0x44, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xA3, 0x50, 0x9F, 0x50, 0xA3, 0x5C,
		0xA3, 0x5C, 0xA8, 0x60, 0xA3, 0x65, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x13, 0x64, 0x13, 0x61, 0x13, 0x72, 0x13, 0x70,
		0x20, 0x60, 0x30, 0x60, 0x20, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xA3, 0x68, 0x9F, 0x70, 0xA3, 0x74,
		0xA3, 0x6E, 0xA8, 0x70, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x80, 0x13, 0x80, 0x13, 0x8D, 0x13, 0x8F,
		0x20, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xA3, 0x80, 0x9F, 0x8F, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x9B, 0x13, 0x9E, 0x13, 0xA8, 0x17, 0xAC,
		0x20, 0x9F, 0x30, 0x9F, 0x26, 0xAC, 0x35, 0xAC,
		0x40, 0x9F, 0x50, 0x9F, 0x44, 0xAC, 0x53, 0xAC,
		0x60, 0x9F, 0x70, 0x9F, 0x62, 0xAC, 0x71, 0xAC,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8E, 0xAC,
		0x9F, 0x9F, 0xA3, 0x97, 0x9D, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x26, 0xAC, 0x31, 0xAC, 0x38, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x65, 0xAC,
		0x69, 0xAC, 0x74, 0xAC, 0x6E, 0xAC, 0x77, 0xAC,
		0x80, 0xAC, 0x8B, 0xAC, 0x80, 0xAC, 0x88, 0xAC,
		0x96, 0xAC, 0xA1, 0xAC, 0x91, 0xAC, 0x9A, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x6D, 0x6D, 0x74, 0x69, 0x6D, 0x76, 0x70, 0x70,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x70, 0x8F, 0x70,
		0x96, 0x69, 0xA2, 0x69, 0x9F, 0x70, 0xAF, 0x70,
		0xAD, 0x69, 0xB9, 0x69, 0xBF, 0x70, 0xCF, 0x70,
		0xC4, 0x69, 0xCF, 0x69, 0xDF, 0x70, 0xEF, 0x70,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x6D, 0x80, 0x70, 0x80, 0x6D, 0x89, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x70, 0xA0, 0x6D, 0x9B, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8C, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA4, 0x73, 0xB2, 0x6D, 0xAD, 0x76, 0xB1,
		0x80, 0xAC, 0x89, 0xA7, 0x80, 0xAC, 0x88, 0xA8,
		0x92, 0xA4, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6E, 0xB5, 0x77, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x4C, 0x3B, 0x55, 0x3B, 0x49, 0x40, 0x4F, 0x3B,
		0x5D, 0x3B, 0x66, 0x3B, 0x58, 0x3B, 0x62, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x89, 0x3B,
		0x91, 0x3B, 0x99, 0x3B, 0x93, 0x3B, 0x9D, 0x3B,
		0xA2, 0x3B, 0xAA, 0x3B, 0xA7, 0x3B, 0xB0, 0x3B,
		0xB3, 0x3B, 0xBB, 0x3B, 0xBA, 0x3B, 0xC4, 0x3B,
		0xC3, 0x3B, 0x00, 0x00, 0xCD, 0x3B, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x49, 0x49, 0x49, 0x3E, 0x49, 0x52, 0x49, 0x49,
		0x52, 0x3B, 0x5D, 0x3B, 0x49, 0x3C, 0x56, 0x3B,
		0x69, 0x3B, 0x74, 0x3B, 0x64, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8D, 0x3B,
		0x96, 0x3B, 0xA2, 0x3B, 0x9B, 0x3B, 0xA9, 0x3B,
		0xAD, 0x3B, 0xB9, 0x3B, 0xB6, 0x3B, 0xC4, 0x3B,
		0xC4, 0x3B, 0xCF, 0x3B, 0xD2, 0x3B, 0xD9, 0x3F,
		0xD9, 0x3C, 0x00, 0x00, 0xD9, 0x47, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x49, 0x5B, 0x49, 0x54, 0x49, 0x64, 0x49, 0x5F,
		0x49, 0x49, 0x50, 0x40, 0x49, 0x57, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xCF, 0x40, 0xBF, 0x50, 0xCF, 0x50,
		0xD9, 0x44, 0xD9, 0x4C, 0xD9, 0x53, 0xD9, 0x59,
		0xD9, 0x52, 0x00, 0x00, 0xD9, 0x5E, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x49, 0x6D, 0x49, 0x6A, 0x49, 0x76, 0x49, 0x75,
		0x49, 0x64, 0x50, 0x60, 0x49, 0x72, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xD9, 0x62, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x49, 0x80, 0x49, 0x80, 0x49, 0x89, 0x49, 0x8A,
		0x49, 0x80, 0x50, 0x80, 0x49, 0x8D, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x49, 0x92, 0x49, 0x95, 0x49, 0x9B, 0x49, 0xA0,
		0x49, 0x9B, 0x50, 0x9F, 0x49, 0xA8, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x50, 0xBF, 0x49, 0xC3, 0x50, 0xD0,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xCF, 0xBF, 0xBF, 0xCF, 0xCF, 0xCF,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCB,
		0x49, 0xD1, 0x50, 0xE0, 0x49, 0xDF, 0x50, 0xEF,
		0x60, 0xDF, 0x70, 0xDF, 0x60, 0xEF, 0x70, 0xEF,
		0x80, 0xDF, 0x8F, 0xDF, 0x80, 0xEF, 0x8F, 0xEF,
		0x9F, 0xDF, 0xAF, 0xDF, 0x9F, 0xEF, 0xAF, 0xEF,
		0xBF, 0xDF, 0xCF, 0xDF, 0xBC, 0xE9, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD6, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xEB, 0x55, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xF1, 0x71, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xF1, 0x8E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x9C, 0xF1, 0xAA, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xB6, 0xEC, 0xC0, 0xE7, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x26, 0x26, 0x2E, 0x22, 0x25, 0x30, 0x26, 0x26,
		0x36, 0x1E, 0x40, 0x19, 0x2F, 0x21, 0x39, 0x1D,
		0x4A, 0x14, 0x55, 0x0F, 0x44, 0x17, 0x50, 0x11,
		0x63, 0x0D, 0x71, 0x0D, 0x60, 0x10, 0x70, 0x10,
		0x80, 0x0D, 0x8E, 0x0D, 0x80, 0x10, 0x8F, 0x10,
		0x9C, 0x0D, 0xAA, 0x0D, 0x9F, 0x10, 0xAF, 0x10,
		0xB5, 0x14, 0xB5, 0x2A, 0xB5, 0x22, 0xB5, 0x34,
		0xB5, 0x38, 0xB8, 0x40, 0xB5, 0x41, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x25, 0x3C, 0x25, 0x32, 0x25, 0x47, 0x25, 0x3F,
		0x26, 0x26, 0x30, 0x21, 0x25, 0x34, 0x30, 0x30,
		0x40, 0x20, 0x50, 0x20, 0x40, 0x30, 0x50, 0x30,
		0x60, 0x20, 0x70, 0x20, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x20, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xAF, 0x20, 0x9F, 0x30, 0xAF, 0x30,
		0xB5, 0x2F, 0xB5, 0x3F, 0xB5, 0x3C, 0xB5, 0x4A,
		0xB5, 0x4A, 0xB8, 0x50, 0xB5, 0x53, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x25, 0x52, 0x25, 0x4C, 0x25, 0x5E, 0x25, 0x59,
		0x25, 0x43, 0x30, 0x40, 0x25, 0x52, 0x30, 0x50,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xB5, 0x4A, 0xB5, 0x55, 0xB5, 0x57, 0xB5, 0x5F,
		0xB5, 0x5C, 0xB8, 0x60, 0xB5, 0x65, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x25, 0x69, 0x25, 0x66, 0x25, 0x74, 0x25, 0x73,
		0x25, 0x61, 0x30, 0x60, 0x25, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xB5, 0x65, 0xB5, 0x6A, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6E, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x25, 0x8B, 0x25, 0x8C,
		0x25, 0x80, 0x30, 0x80, 0x25, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x96, 0x25, 0x99, 0x25, 0xA1, 0x25, 0xA6,
		0x25, 0x9E, 0x30, 0x9F, 0x25, 0xAD, 0x30, 0xAF,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAD, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x30, 0xBF, 0x2E, 0xC3, 0x3C, 0xC3,
		0x40, 0xBF, 0x50, 0xBF, 0x49, 0xC3, 0x57, 0xC3,
		0x60, 0xBF, 0x70, 0xBF, 0x65, 0xC3, 0x72, 0xC3,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xC3, 0x8D, 0xC3,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9B, 0xC3, 0xA8, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x4F, 0xC3,
		0x52, 0xC3, 0x5E, 0xC3, 0x59, 0xC3, 0x63, 0xC3,
		0x69, 0xC3, 0x74, 0xC3, 0x6C, 0xC3, 0x76, 0xC3,
		0x80, 0xC3, 0x8B, 0xC3, 0x80, 0xC3, 0x89, 0xC3,
		0x96, 0xC3, 0xA1, 0xC3, 0x93, 0xC3, 0x9D, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA6, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x55, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x5D, 0xC3, 0x66, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x6F, 0xC3, 0x77, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xC3, 0x88, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xC3, 0x99, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xC3, 0xAA, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x89, 0x4F,
		0x91, 0x4C, 0x98, 0x50, 0x91, 0x54, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x57, 0x67,
		0x60, 0x60, 0x68, 0x60, 0x5C, 0x65, 0x62, 0x62,
		0x6E, 0x5D, 0x76, 0x59, 0x6A, 0x5F, 0x73, 0x5A,
		0x80, 0x54, 0x8C, 0x4E, 0x80, 0x54, 0x8F, 0x50,
		0x91, 0x5D, 0x98, 0x60, 0x91, 0x65, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x47, 0x6F, 0x38, 0x77, 0x3A, 0x76,
		0x4A, 0x6E, 0x4E, 0x6C, 0x3D, 0x74, 0x40, 0x73,
		0x53, 0x69, 0x5A, 0x66, 0x44, 0x71, 0x50, 0x70,
		0x62, 0x62, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x91, 0x6E, 0x98, 0x70, 0x91, 0x77, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x06, 0x8F, 0x10, 0x8F,
		0x25, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x27, 0x96, 0x32, 0x96, 0x40, 0x98, 0x48, 0x98,
		0x3D, 0x96, 0x48, 0x96, 0x50, 0x98, 0x58, 0x98,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x74, 0x96, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x96, 0x8B, 0x96, 0x80, 0x98, 0x88, 0x98,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_pltm_table = {
		/* pltm - H */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - V */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - P */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - F */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_wdr_table = { 0 }
};
struct isp_cfg_pt gc2385_mipi_isp_cfg = {
	.isp_test_settings = &gc2385_mipi_isp_test_settings,
	.isp_3a_settings = &gc2385_mipi_isp_3a_settings,
	.isp_tunning_settings = &gc2385_mipi_isp_tuning_settings,
	.isp_iso_settings = &gc2385_mipi_isp_iso_settings
};


#endif /* end of _GC2385_MIPI_H_V100_ */struct venc_param gc2385_mipi_venc_settings = {
