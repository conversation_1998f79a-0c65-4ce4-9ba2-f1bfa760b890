/*
 *****************************************************************************
 * imx258
 * 1536x1536@30fps, wdr: 0
 * Hawkview ISP - imx258 config module
 * Copyright (c) 2017 by Allwinnertech Co., Ltd. http://www.allwinnertech.com
 *  Version  |     Author      |     Date     |      Description
 *    2.0    |  Hawkview Tool  |  2017/10/25  |  Automatic generation.
 *
 *****************************************************************************
 */

#ifndef _IMX258_H_V100_
#define _IMX258_H_V100_

#include "../../include/isp_ini_parse.h"

struct isp_test_param imx258_isp_test_settings = {
	.isp_test_mode = 0,
	.isp_test_exptime = 0,
	.exp_line_start = 160,
	.exp_line_step = 32,
	.exp_line_end = 32000,
	.exp_change_interval = 5,
	.isp_test_gain = 0,
	.gain_start = 16,
	.gain_step = 2,
	.gain_end = 256,
	.gain_change_interval = 30,
	.isp_test_focus = 0,
	.focus_start = 10,
	.focus_step = 10,
	.focus_end = 800,
	.focus_change_interval = 5,
	.isp_log_param = 0,
	.isp_gain = 16,
	.isp_exp_line = 39999,
	.isp_color_temp = 2700,
	.ae_forced = 0,
	.lum_forced = 30,
	.manual_en = 1,
	.afs_en = 1,
	.sharp_en = 1,
	.contrast_en = 1,
	.denoise_en = 1,
	.drc_en = 1,
	.cem_en = 0,
	.lsc_en = 1,
	.gamma_en = 1,
	.cm_en = 1,
	.ae_en = 1,
	.af_en = 1,
	.awb_en = 1,
	.hist_en = 1,
	.blc_en = 0,
	.so_en = 1,
	.wb_en = 1,
	.otf_dpc_en = 1,
	.cfa_en = 1,
	.tdf_en = 0,
	.cnr_en = 1,
	.satur_en = 1,
	.defog_en = 0,
	.linear_en = 0,
	.gtm_en = 0,
	.dig_gain_en = 1,
	.pltm_en = 0,
	.wdr_en = 0,
	.ctc_en = 0
};
struct isp_3a_param imx258_isp_3a_settings = {
	.define_ae_table = 1,
	.ae_max_lv = 1650,
	.ae_table_preview_length = 2,
	.ae_table_preview = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  4000,   266,   266
	},
	.ae_table_capture_length = 2,
	.ae_table_capture = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  8000,   266,   266
	},
	.ae_table_video_length = 2,
	.ae_table_video = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  8000,   266,   266
	},
	.ae_win_weight = {
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     8,     8,     4,     4,     4,
		    4,     4,     6,     8,     8,     6,     4,     4,
		    4,     6,     8,     8,     8,     8,     6,     4,
		    4,     8,     8,     8,     8,     8,     8,     4,
		    4,     4,     4,     4,     4,     4,     4,     4
	},
	.ae_hist_mod_en = 1,
	.ae_hist_sel = 0,
	.ae_stat_sel = 1,
	.ae_ki = 50,
	.ae_ConvDataIndex = 3,
	.ae_blowout_pre_en = 0,
	.ae_blowout_attr = 30,
	.ae_delay_frame = 0,
	.exp_delay_frame = 2,
	.gain_delay_frame = 2,
	.exp_comp_step = 4,
	.ae_touch_dist_ind = 0,
	.ae_iso2gain_ratio = 16,
	.ae_fno_step = {
		  141,   145,   152,   163,   175,   190,   209,   233,
		  266,   311,   379,   487,   657,   971,  1825,  3794
	},
	.wdr_cfg = {
		    6,   512,  3072
	},
	.awb_interval = 2,
	.awb_speed = 8,
	.awb_stat_sel = 0,
	.awb_color_temper_low = 1800,
	.awb_color_temper_high = 8000,
	.awb_base_temper = 6500,
	.awb_green_zone_dist = 1024,
	.awb_blue_sky_dist = 98,
	.awb_light_num = 8,
	.awb_light_info = {
		  151,   256,   130,   256,   256,   256,    50,  4000,    64,   100,
		  223,   256,    94,   256,   256,   256,    50,  2500,    64,    90,
		  236,   256,    88,   256,   256,   256,    50,  1900,    64,    90,
		  133,   256,   118,   256,   256,   256,    50,  4100,    64,   100,
		  208,   256,   101,   256,   256,   256,    50,  2800,    64,    90,
		  134,   256,   153,   256,   256,   256,    50,  5000,    64,   100,
		  114,   256,   196,   256,   256,   256,    50,  6500,    64,   100,
		  106,   256,   217,   256,   256,   256,    50,  7500,    64,   100
	},
	.awb_ext_light_num = 0,
	.awb_ext_light_info = {
		0
	},
	.awb_skin_color_num = 0,
	.awb_skin_color_info = {
		0
	},
	.awb_special_color_num = 0,
	.awb_special_color_info = {
		0
	},
	.awb_preset_gain = {
		  256,   256,   256,   256,   151,   405,   210,   340,   210,   340,
		  145,   480,   265,   256,   256,   256,   285,   245,   280,   235,
		  140,   480
	},
	.awb_rgain_favor = 256,
	.awb_bgain_favor = 256,
	.af_use_otp = 0,
	.vcm_min_code = 380,
	.vcm_max_code = 800,
	.af_interval_time = 136,
	.af_speed_ind = 20,
	.af_auto_fine_en = 0,
	.af_single_fine_en = 0,
	.af_fine_step = 10,
	.af_move_cnt = 4,
	.af_still_cnt = 2,
	.af_move_monitor_cnt = 6,
	.af_still_monitor_cnt = 3,
	.af_stable_min = 245,
	.af_stable_max = 265,
	.af_low_light_lv = 10,
	.af_near_tolerance = 15,
	.af_far_tolerance = 25,
	.af_tolerance_off = 0,
	.af_peak_th = 100,
	.af_dir_th = 10,
	.af_change_ratio = 30,
	.af_move_minus = 2,
	.af_still_minus = 1,
	.af_scene_motion_th = 280,
	.af_tolerance_tbl_len = 10,
	.af_std_code_tbl = {
		    0,   100,   200,   245,   280,   320,   350,   500,   800,  1024,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	},
	.af_tolerance_value_tbl = {
		   33,    33,    33,    26,    22,    18,    15,    13,    10,    10,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	}
};
struct isp_dynamic_param imx258_isp_iso_settings = {
	.triger = {
		.sharp_triger = 1,
		.contrast_triger = 1,
		.denoise_triger = 1,
		.sensor_offset_triger = 1,
		.black_level_triger = 1,
		.dpc_triger = 1,
		.defog_value_triger = 0,
		.pltm_dynamic_triger = 1,
		.brightness_triger = 0,
		.gcontrast_triger = 0,
		.saturation_triger = 1,
		.cem_ratio_triger = 0,
		.tdf_triger = 1,
		.color_denoise_triger = 0,
		.ae_cfg_triger = 0,
		.gtm_cfg_triger = 0
	},
	.isp_lum_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_gain_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_dynamic_cfg[0] = {
		.sharp_cfg = {
			1, 8, 350, 350, 256, 512, 256, 0, 256, 0
		},
		.contrast_cfg = {
			1, 16, 36, 12, 105, 512, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			64, 0, 64, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 50
		},
		.cem_ratio = 256,
		.tdf_cfg = {
			16, 24, 8, 0, 96, 0, 256, 0, 160, 0, 320, 0
		},
		.color_denoise = 4,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[1] = {
		.sharp_cfg = {
			2, 16, 350, 350, 256, 440, 256, 0, 256, 0
		},
		.contrast_cfg = {
			2, 16, 36, 12, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			96, 0, 96, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, -11, 50
		},
		.cem_ratio = 237,
		.tdf_cfg = {
			16, 24, 7, 0, 312, 0, 396, 0, 282, 0, 355, 0
		},
		.color_denoise = 8,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[2] = {
		.sharp_cfg = {
			4, 24, 350, 256, 256, 384, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			128, 0, 128, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -11, -33, 50
		},
		.cem_ratio = 218,
		.tdf_cfg = {
			16, 24, 6, 0, 370, 0, 449, 0, 340, 0, 390, 0
		},
		.color_denoise = 12,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[3] = {
		.sharp_cfg = {
			6, 24, 350, 256, 256, 360, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			180, 0, 180, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -33, -33, 50
		},
		.cem_ratio = 199,
		.tdf_cfg = {
			16, 32, 5, 0, 400, 0, 400, 0, 400, 0, 400, 0
		},
		.color_denoise = 16,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[4] = {
		.sharp_cfg = {
			8, 24, 350, 256, 256, 320, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 36, 12, 105, 256, 64, 256, 0, 256, 0
		},
		.denoise_cfg = {
			256, 0, 256, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -55, -44, 50
		},
		.cem_ratio = 180,
		.tdf_cfg = {
			16, 40, 4, 0, 450, 0, 450, 0, 450, 0, 450, 0
		},
		.color_denoise = 20,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[5] = {
		.sharp_cfg = {
			10, 24, 350, 256, 256, 288, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 34, 12, 105, 256, 80, 256, 0, 256, 0
		},
		.denoise_cfg = {
			380, 0, 380, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -77, 0, 50
		},
		.cem_ratio = 160,
		.tdf_cfg = {
			16, 50, 4, 0, 500, 0, 500, 0, 500, 0, 500, 0
		},
		.color_denoise = 66,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[6] = {
		.sharp_cfg = {
			12, 48, 350, 256, 256, 200, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 30, 12, 105, 256, 96, 256, 0, 256, 0
		},
		.denoise_cfg = {
			512, 0, 512, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -99, -99, 50
		},
		.cem_ratio = 140,
		.tdf_cfg = {
			16, 60, 3, 0, 555, 0, 608, 0, 500, 0, 666, 0
		},
		.color_denoise = 88,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[7] = {
		.sharp_cfg = {
			14, 60, 350, 200, 256, 200, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 26, 12, 105, 256, 104, 256, 0, 256, 0
		},
		.denoise_cfg = {
			768, 0, 768, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			16, 16, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -111, -111, 50
		},
		.cem_ratio = 120,
		.tdf_cfg = {
			16, 64, 2, 0, 777, 0, 777, 0, 777, 0, 777, 0
		},
		.color_denoise = 100,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[8] = {
		.sharp_cfg = {
			16, 70, 555, 555, 256, 200, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 22, 12, 105, 256, 112, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1000, 0, 1280, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			33, 33, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -99, -99, 50
		},
		.cem_ratio = 100,
		.tdf_cfg = {
			16, 48, 1, 0, 888, 0, 999, 0, 888, 0, 999, 0
		},
		.color_denoise = 122,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[9] = {
		.sharp_cfg = {
			18, 80, 166, 166, 255, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 18, 10, 105, 200, 128, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1200, 0, 1200, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-240, -240, -240, -240
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			52, 52, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -133, -133, 50
		},
		.cem_ratio = 80,
		.tdf_cfg = {
			16, 48, 0, 0, 1111, 0, 1222, 0, 1111, 0, 1222, 0
		},
		.color_denoise = 144,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[10] = {
		.sharp_cfg = {
			20, 90, 122, 122, 200, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 14, 8, 105, 199, 160, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2200, 0, 2000, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-235, -235, -235, -235
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			66, 66, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -188, -188, 44
		},
		.cem_ratio = 60,
		.tdf_cfg = {
			16, 48, 0, 0, 1600, 0, 1777, 0, 1555, 0, 1777, 0
		},
		.color_denoise = 166,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[11] = {
		.sharp_cfg = {
			22, 100, 88, 88, 188, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 10, 7, 88, 188, 256, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2800, 0, 2240, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-230, -230, -230, -230
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			66, 66, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -200, -200, 33
		},
		.cem_ratio = 40,
		.tdf_cfg = {
			16, 48, 0, 0, 2222, 0, 2222, 0, 2222, 0, 2222, 0
		},
		.color_denoise = 188,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[12] = {
		.sharp_cfg = {
			24, 110, 66, 66, 177, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 8, 6, 77, 177, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			3600, 0, 2360, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-225, -225, -225, -225
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			66, 66, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -200, -200, 22
		},
		.cem_ratio = 20,
		.tdf_cfg = {
			16, 48, 0, 0, 2222, 0, 2222, 0, 2222, 0, 3333, 0
		},
		.color_denoise = 200,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[13] = {
		.sharp_cfg = {
			26, 120, 44, 44, 166, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 4, 3, 66, 166, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			4400, 0, 2560, 0
		},
		.sensor_offset = {
			-256, -256, -256, -256
		},
		.black_level = {
			-220, -220, -220, -220
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			66, 66, 1
		},
		.defog_value = 5,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, -200, -200, 11
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 48, 0, 0, 3333, 0, 3333, 0, 3333, 0, 4444, 0
		},
		.color_denoise = 222,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 66, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	}
};
struct isp_tunning_param imx258_isp_tuning_settings = {
	.flash_gain = 80,
	.flash_delay_frame = 16,
	.flicker_type = 0,
	.flicker_ratio = 15,
	.hor_visual_angle = 60,
	.ver_visual_angle = 40,
	.focus_length = 300,
	.gamma_num = 5,
	.rolloff_ratio = 0,
	.gtm_type = 1,
	.gamma_type = 1,
	.auto_alpha_en = 0,
	.cfa_dir_th = 2048,
	.ctc_th_max = 316,
	.ctc_th_min = 60,
	.ctc_th_slope = 262,
	.ctc_dir_wt = 64,
	.ctc_dir_th = 80,
	.bayer_gain = {
		 1024,  1024,  1024,  1024
	},
	.ff_mod = 1,
	.lsc_center_x = 2048,
	.lsc_center_y = 2048,
	.lsc_trig_cfg = {
		    0,     0,     0,     0,  5500,  6500
	},
	.gamma_trig_cfg = {
		 1300,  1100,   900,   600,   300
	},
	.color_matrix_ini[0] = {
		.matrix = { { 328, 0, -72 }, { -153, 508, -99 },
				{ 0, -173, 429 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[1] = {
		.matrix = { { 353, 0, -97 }, { -128, 475, -91 },
				{ -11, -86, 353 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[2] = {
		.matrix = { { 411, -17, -138 }, { -88, 485, -141 },
				{ -20, -24, 300 } },
		.offset = { 0, 0, 0 }
	},
	.cm_trig_cfg = {
		 2700,  4000,  6500
	},
	.pltm_cfg = {
		    1,     0,    10,     7,  2048,  2048,     0,    15,
		   15,   210,    32,   255,    23,    31,     0
	},
	.isp_bdnf_th = {
		   22,    24,    25,    28,    29,    32,    34,    36,
		   38,    41,    42,    45,    46,    49,    50,    53,
		   55,    57,    59,    62,    63,    66,    67,    70,
		   71,    74,    76,    78,    80,    83,    84,    87,
		   88
	},
	.isp_tdnf_th = {
		    4,     7,     7,     7,     8,    10,    10,    11,
		   11,    13,    14,    14,    15,    15,    17,    18,
		   18,    20,    20,    21,    22,    22,    24,    24,
		   25,    27,    27,    28,    28,    29,    31,    31,
		   32
	},
	.isp_tdnf_ref_noise = {
		    8,    12,    15,    17,    20,    21,    21,    21,
		   22,    22,    22,    22,    24,    24,    25,    27,
		   28,    28,    29,    31,    32,    32,    34,    35,
		   36,    36,    38,    39,    41,    41,    42,    43,
		   45
	},
	.isp_tdnf_k = {
		    4,    13,    20,    24,    27,    29,    30,    30,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31
	},
	.isp_contrast_val = {
		  103,   160,   160,   160,   160,   160,   176,   192,
		  208,   208,   208,   208,   208,   208,   208,   208,
		  208,   208,   180,   160,   144,   128,   112,    96,
		   80,    72,    64,    56,    48,    32,    32,    32,
		   32
	},
	.isp_contrast_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_sharp_val = {
		  144,   132,   128,   128,   128,   128,   124,   117,
		   96,    96,    80,    80,    64,    64,    48,    48,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32
	},
	.isp_sharp_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_tdnf_diff = {
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   254,   254,   254,   254,   254,   254,   253,
		  253,   253,   253,   253,   252,   252,   252,   252,
		  252,   251,   251,   251,   250,   250,   250,   250,
		  249,   249,   249,   248,   248,   248,   247,   247,
		  247,   246,   246,   245,   245,   245,   244,   244,
		  243,   243,   242,   242,   241,   241,   240,   240,
		  240,   239,   238,   238,   237,   237,   236,   236,
		  235,   235,   234,   234,   233,   232,   232,   231,
		  231,   230,   229,   229,   228,   227,   227,   226,
		  225,   225,   224,   223,   222,   222,   221,   220,
		  220,   219,   218,   217,   216,   216,   215,   214,
		  213,   212,   212,   211,   210,   209,   208,   207,
		  207,   206,   205,   204,   203,   202,   201,   200,
		  199,   198,   197,   196,   195,   194,   193,   192,
		  192,   190,   189,   188,   187,   186,   185,   184,
		  183,   182,   181,   180,   179,   178,   177,   176,
		  175,   173,   172,   171,   170,   169,   168,   166,
		  165,   164,   163,   162,   160,   159,   158,   157,
		  156,   154,   153,   152,   150,   149,   148,   147,
		  145,   144,   143,   141,   140,   139,   137,   136,
		  135,   133,   132,   130,   129,   128,   126,   125,
		  123,   122,   120,   119,   117,   116,   114,   113,
		  112,   110,   108,   107,   105,   104,   102,   101,
		   99,    98,    96,    95,    93,    91,    90,    88,
		   87,    85,    83,    82,    80,    78,    77,    75,
		   73,    72,    70,    68,    66,    65,    63,    61,
		   60,    58,    56,    54,    52,    51,    49,    47,
		   45,    43,    42,    40,    38,    36,    34,    32,
		   31,    29,    27,    25,    23,    21,    19,    17,
		   15,    13,    11,     9,     7,     5,     3,     1
	},
	.isp_contrat_pe = {
		    0,     2,     4,     6,     8,    10,    12,    14,
		   16,    26,    36,    46,    56,    66,    76,    86,
		   96,   100,   104,   108,   112,   116,   120,   124,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   130,   132,   134,   136,   138,   140,   142,
		  144,   146,   148,   150,   152,   154,   156,   158,
		  160,   164,   168,   172,   176,   180,   184,   188,
		  192,   195,   197,   200,   202,   205,   207,   209,
		  212,   209,   207,   205,   202,   200,   197,   195,
		  192,   188,   184,   180,   176,   172,   168,   164,
		  160,   158,   156,   154,   152,   150,   148,   146,
		  144,   142,   140,   138,   136,   134,   132,   130,
		  128,   126,   124,   122,   120,   118,   116,   114,
		  112,   110,   108,   106,   104,   102,   100,    98,
		   96,    96,    96,    96,    96,    96,    96,    96,
		   96,    96,    96,    96,    96,    96,    96,    96
	},
	.gamma_tbl_ini = {
	{
		/* gamma - 0 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 1 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 2 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 3 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 4 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	}
	},
	.lsc_tbl = {
	{
		/* 0 */
		 1024,  1041,  1058,  1076,  1096,  1116,  1135,  1156,
		 1178,  1201,  1225,  1249,  1274,  1299,  1326,  1352,
		 1379,  1404,  1429,  1457,  1484,  1513,  1543,  1580,
		 1615,  1648,  1680,  1712,  1743,  1776,  1814,  1852,
		 1892,  1936,  1982,  2023,  2069,  2118,  2174,  2227,
		 2279,  2329,  2387,  2450,  2515,  2583,  2659,  2739,
		 2825,  2909,  2997,  3082,  3181,  3288,  3401,  3523,
		 3655,  3799,  3943,  4044,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1024,  1038,  1051,  1065,  1081,  1096,  1112,  1129,
		 1148,  1167,  1188,  1208,  1228,  1247,  1268,  1289,
		 1311,  1330,  1348,  1370,  1392,  1413,  1435,  1463,
		 1490,  1515,  1541,  1567,  1591,  1616,  1645,  1674,
		 1705,  1739,  1775,  1805,  1837,  1873,  1914,  1955,
		 1997,  2039,  2084,  2132,  2181,  2233,  2289,  2349,
		 2412,  2475,  2540,  2606,  2681,  2758,  2838,  2932,
		 3033,  3139,  3241,  3342,  3443,  3543,  3644,  3744,
		 3845,  3945,  4045,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1040,  1055,  1069,  1085,  1100,  1115,  1130,
		 1149,  1168,  1189,  1211,  1232,  1252,  1274,  1295,
		 1318,  1337,  1356,  1379,  1398,  1417,  1436,  1464,
		 1490,  1515,  1539,  1566,  1590,  1617,  1647,  1678,
		 1709,  1744,  1779,  1809,  1841,  1879,  1919,  1958,
		 1999,  2042,  2086,  2133,  2179,  2231,  2286,  2348,
		 2410,  2476,  2545,  2618,  2695,  2775,  2859,  2956,
		 3057,  3164,  3277,  3378,  3479,  3580,  3680,  3781,
		 3881,  3981,  4081,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 1 */
		 1024,  1042,  1057,  1075,  1096,  1116,  1137,  1157,
		 1178,  1199,  1222,  1247,  1272,  1294,  1318,  1343,
		 1368,  1395,  1423,  1448,  1473,  1501,  1528,  1557,
		 1586,  1618,  1652,  1688,  1721,  1758,  1794,  1830,
		 1867,  1910,  1957,  2003,  2049,  2093,  2141,  2194,
		 2247,  2296,  2355,  2418,  2481,  2544,  2614,  2685,
		 2768,  2844,  2926,  3014,  3109,  3199,  3293,  3413,
		 3545,  3691,  3827,  3922,  4017,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1024,  1037,  1050,  1064,  1080,  1097,  1114,  1130,
		 1147,  1166,  1185,  1204,  1223,  1240,  1259,  1279,
		 1301,  1321,  1342,  1362,  1383,  1405,  1427,  1453,
		 1477,  1501,  1528,  1555,  1580,  1605,  1635,  1663,
		 1691,  1721,  1756,  1790,  1826,  1861,  1897,  1937,
		 1976,  2012,  2055,  2105,  2155,  2205,  2261,  2318,
		 2381,  2440,  2506,  2578,  2652,  2718,  2788,  2873,
		 2967,  3071,  3183,  3279,  3374,  3470,  3565,  3660,
		 3755,  3850,  3945,  4039,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1037,  1049,  1062,  1077,  1093,  1109,  1124,
		 1142,  1159,  1178,  1199,  1219,  1237,  1258,  1278,
		 1298,  1317,  1336,  1354,  1371,  1392,  1415,  1440,
		 1464,  1486,  1510,  1536,  1562,  1587,  1617,  1646,
		 1674,  1703,  1738,  1773,  1807,  1842,  1878,  1918,
		 1956,  1994,  2040,  2092,  2140,  2189,  2241,  2295,
		 2358,  2416,  2477,  2544,  2615,  2677,  2748,  2831,
		 2922,  3020,  3132,  3228,  3324,  3419,  3514,  3610,
		 3705,  3800,  3894,  3989,  4084,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 2 */
		 1025,  1045,  1057,  1070,  1087,  1100,  1116,  1134,
		 1152,  1171,  1190,  1210,  1227,  1250,  1270,  1288,
		 1307,  1326,  1347,  1366,  1385,  1409,  1435,  1462,
		 1485,  1508,  1535,  1565,  1594,  1620,  1646,  1675,
		 1707,  1741,  1774,  1810,  1847,  1881,  1923,  1971,
		 2017,  2057,  2101,  2151,  2207,  2268,  2327,  2386,
		 2452,  2518,  2588,  2661,  2744,  2821,  2900,  2996,
		 3096,  3204,  3335,  3415,  3494,  3573,  3653,  3732,
		 3811,  3889,  3968,  4047,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1037,  1048,  1062,  1079,  1092,  1107,  1123,
		 1138,  1154,  1172,  1190,  1206,  1226,  1246,  1266,
		 1285,  1304,  1324,  1344,  1364,  1384,  1405,  1428,
		 1451,  1472,  1494,  1521,  1546,  1568,  1591,  1619,
		 1649,  1678,  1705,  1737,  1771,  1804,  1844,  1890,
		 1932,  1965,  2002,  2045,  2094,  2144,  2195,  2248,
		 2308,  2368,  2434,  2502,  2574,  2637,  2708,  2796,
		 2888,  2984,  3103,  3183,  3262,  3342,  3421,  3500,
		 3579,  3658,  3737,  3816,  3894,  3973,  4051,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1020,  1033,  1045,  1059,  1075,  1086,  1102,  1119,
		 1134,  1152,  1172,  1191,  1206,  1225,  1243,  1259,
		 1274,  1290,  1307,  1327,  1346,  1367,  1387,  1409,
		 1429,  1446,  1467,  1493,  1518,  1541,  1565,  1592,
		 1622,  1652,  1679,  1709,  1740,  1769,  1806,  1846,
		 1884,  1917,  1955,  2000,  2048,  2100,  2151,  2205,
		 2265,  2326,  2389,  2456,  2528,  2593,  2663,  2750,
		 2841,  2936,  3051,  3130,  3210,  3290,  3369,  3448,
		 3527,  3606,  3685,  3764,  3843,  3921,  3999,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 3 */
		 1024,  1039,  1051,  1066,  1085,  1102,  1118,  1135,
		 1154,  1171,  1189,  1210,  1230,  1250,  1268,  1289,
		 1309,  1331,  1350,  1372,  1393,  1414,  1436,  1464,
		 1490,  1514,  1539,  1567,  1594,  1622,  1651,  1678,
		 1709,  1743,  1779,  1818,  1855,  1890,  1927,  1974,
		 2018,  2060,  2105,  2159,  2211,  2263,  2321,  2384,
		 2451,  2519,  2587,  2662,  2738,  2814,  2894,  2987,
		 3091,  3205,  3318,  3395,  3473,  3551,  3629,  3706,
		 3784,  3861,  3938,  4015,  4092,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1037,  1047,  1060,  1078,  1094,  1109,  1125,
		 1141,  1158,  1176,  1196,  1215,  1233,  1250,  1270,
		 1289,  1310,  1329,  1351,  1370,  1389,  1409,  1436,
		 1460,  1482,  1505,  1529,  1552,  1577,  1603,  1629,
		 1655,  1686,  1720,  1755,  1787,  1820,  1854,  1896,
		 1935,  1972,  2011,  2057,  2103,  2151,  2201,  2255,
		 2314,  2376,  2435,  2498,  2567,  2637,  2707,  2786,
		 2880,  2984,  3083,  3161,  3239,  3317,  3395,  3472,
		 3550,  3627,  3705,  3782,  3859,  3936,  4013,  4090,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1039,  1049,  1060,  1078,  1094,  1108,  1123,
		 1139,  1155,  1172,  1190,  1207,  1224,  1241,  1259,
		 1276,  1294,  1311,  1330,  1348,  1364,  1382,  1406,
		 1429,  1449,  1469,  1493,  1515,  1540,  1565,  1588,
		 1613,  1641,  1672,  1706,  1738,  1768,  1800,  1842,
		 1881,  1916,  1954,  1999,  2045,  2091,  2139,  2190,
		 2248,  2307,  2366,  2430,  2496,  2561,  2626,  2702,
		 2788,  2885,  2988,  3066,  3144,  3222,  3300,  3378,
		 3455,  3533,  3610,  3687,  3765,  3842,  3919,  3995,
		 4072,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 4 */
		 1022,  1043,  1061,  1079,  1100,  1122,  1140,  1157,
		 1173,  1189,  1207,  1230,  1256,  1280,  1299,  1320,
		 1341,  1364,  1387,  1412,  1439,  1469,  1497,  1522,
		 1545,  1571,  1600,  1632,  1664,  1694,  1724,  1758,
		 1795,  1835,  1875,  1917,  1958,  2005,  2048,  2095,
		 2140,  2195,  2251,  2312,  2375,  2449,  2518,  2584,
		 2646,  2720,  2812,  2899,  2984,  3063,  3157,  3266,
		 3390,  3518,  3635,  3724,  3812,  3901,  3990,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1026,  1035,  1051,  1066,  1081,  1096,  1109,  1122,
		 1135,  1149,  1164,  1184,  1207,  1227,  1244,  1262,
		 1281,  1299,  1317,  1337,  1355,  1376,  1397,  1420,
		 1442,  1464,  1487,  1512,  1537,  1560,  1585,  1612,
		 1641,  1673,  1703,  1735,  1768,  1808,  1844,  1883,
		 1918,  1962,  2007,  2057,  2105,  2163,  2219,  2272,
		 2322,  2381,  2453,  2524,  2593,  2656,  2725,  2811,
		 2908,  3013,  3114,  3203,  3293,  3382,  3471,  3559,
		 3648,  3737,  3825,  3913,  4001,  4089,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1030,  1042,  1055,  1069,  1084,  1097,  1110,
		 1120,  1131,  1143,  1162,  1182,  1198,  1211,  1226,
		 1244,  1258,  1271,  1288,  1307,  1328,  1349,  1370,
		 1388,  1407,  1428,  1452,  1476,  1500,  1524,  1551,
		 1579,  1610,  1638,  1664,  1690,  1723,  1756,  1790,
		 1820,  1857,  1897,  1943,  1988,  2041,  2092,  2143,
		 2191,  2246,  2313,  2380,  2444,  2503,  2568,  2648,
		 2740,  2838,  2932,  3021,  3110,  3199,  3288,  3377,
		 3466,  3555,  3643,  3732,  3820,  3908,  3996,  4084,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 5 */
		 1022,  1043,  1061,  1079,  1100,  1122,  1140,  1157,
		 1173,  1189,  1207,  1230,  1256,  1280,  1299,  1320,
		 1341,  1364,  1387,  1412,  1439,  1469,  1497,  1522,
		 1545,  1571,  1600,  1632,  1664,  1694,  1724,  1758,
		 1795,  1835,  1875,  1917,  1958,  2005,  2048,  2095,
		 2140,  2195,  2251,  2312,  2375,  2449,  2518,  2584,
		 2646,  2720,  2812,  2899,  2984,  3063,  3157,  3266,
		 3390,  3518,  3635,  3724,  3812,  3901,  3990,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1026,  1035,  1051,  1066,  1081,  1096,  1109,  1122,
		 1135,  1149,  1164,  1184,  1207,  1227,  1244,  1262,
		 1281,  1299,  1317,  1337,  1355,  1376,  1397,  1420,
		 1442,  1464,  1487,  1512,  1537,  1560,  1585,  1612,
		 1641,  1673,  1703,  1735,  1768,  1808,  1844,  1883,
		 1918,  1962,  2007,  2057,  2105,  2163,  2219,  2272,
		 2322,  2381,  2453,  2524,  2593,  2656,  2725,  2811,
		 2908,  3013,  3114,  3203,  3293,  3382,  3471,  3559,
		 3648,  3737,  3825,  3913,  4001,  4089,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1030,  1042,  1055,  1069,  1084,  1097,  1110,
		 1120,  1131,  1143,  1162,  1182,  1198,  1211,  1226,
		 1244,  1258,  1271,  1288,  1307,  1328,  1349,  1370,
		 1388,  1407,  1428,  1452,  1476,  1500,  1524,  1551,
		 1579,  1610,  1638,  1664,  1690,  1723,  1756,  1790,
		 1820,  1857,  1897,  1943,  1988,  2041,  2092,  2143,
		 2191,  2246,  2313,  2380,  2444,  2503,  2568,  2648,
		 2740,  2838,  2932,  3021,  3110,  3199,  3288,  3377,
		 3466,  3555,  3643,  3732,  3820,  3908,  3996,  4084,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 6 */
		 1024,  1041,  1058,  1076,  1096,  1116,  1135,  1156,
		 1178,  1201,  1225,  1249,  1274,  1299,  1326,  1352,
		 1379,  1404,  1429,  1457,  1484,  1513,  1543,  1580,
		 1615,  1648,  1680,  1712,  1743,  1776,  1814,  1852,
		 1892,  1936,  1982,  2023,  2069,  2118,  2174,  2227,
		 2279,  2329,  2387,  2450,  2515,  2583,  2659,  2739,
		 2825,  2909,  2997,  3082,  3181,  3288,  3401,  3523,
		 3655,  3799,  3943,  4044,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1024,  1038,  1051,  1065,  1081,  1096,  1112,  1129,
		 1148,  1167,  1188,  1208,  1228,  1247,  1268,  1289,
		 1311,  1330,  1348,  1370,  1392,  1413,  1435,  1463,
		 1490,  1515,  1541,  1567,  1591,  1616,  1645,  1674,
		 1705,  1739,  1775,  1805,  1837,  1873,  1914,  1955,
		 1997,  2039,  2084,  2132,  2181,  2233,  2289,  2349,
		 2412,  2475,  2540,  2606,  2681,  2758,  2838,  2932,
		 3033,  3139,  3241,  3342,  3443,  3543,  3644,  3744,
		 3845,  3945,  4045,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1040,  1055,  1069,  1085,  1100,  1115,  1130,
		 1149,  1168,  1189,  1211,  1232,  1252,  1274,  1295,
		 1318,  1337,  1356,  1379,  1398,  1417,  1436,  1464,
		 1490,  1515,  1539,  1566,  1590,  1617,  1647,  1678,
		 1709,  1744,  1779,  1809,  1841,  1879,  1919,  1958,
		 1999,  2042,  2086,  2133,  2179,  2231,  2286,  2348,
		 2410,  2476,  2545,  2618,  2695,  2775,  2859,  2956,
		 3057,  3164,  3277,  3378,  3479,  3580,  3680,  3781,
		 3881,  3981,  4081,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 7 */
		 1024,  1042,  1057,  1075,  1096,  1116,  1137,  1157,
		 1178,  1199,  1222,  1247,  1272,  1294,  1318,  1343,
		 1368,  1395,  1423,  1448,  1473,  1501,  1528,  1557,
		 1586,  1618,  1652,  1688,  1721,  1758,  1794,  1830,
		 1867,  1910,  1957,  2003,  2049,  2093,  2141,  2194,
		 2247,  2296,  2355,  2418,  2481,  2544,  2614,  2685,
		 2768,  2844,  2926,  3014,  3109,  3199,  3293,  3413,
		 3545,  3691,  3827,  3922,  4017,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1024,  1037,  1050,  1064,  1080,  1097,  1114,  1130,
		 1147,  1166,  1185,  1204,  1223,  1240,  1259,  1279,
		 1301,  1321,  1342,  1362,  1383,  1405,  1427,  1453,
		 1477,  1501,  1528,  1555,  1580,  1605,  1635,  1663,
		 1691,  1721,  1756,  1790,  1826,  1861,  1897,  1937,
		 1976,  2012,  2055,  2105,  2155,  2205,  2261,  2318,
		 2381,  2440,  2506,  2578,  2652,  2718,  2788,  2873,
		 2967,  3071,  3183,  3279,  3374,  3470,  3565,  3660,
		 3755,  3850,  3945,  4039,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1037,  1049,  1062,  1077,  1093,  1109,  1124,
		 1142,  1159,  1178,  1199,  1219,  1237,  1258,  1278,
		 1298,  1317,  1336,  1354,  1371,  1392,  1415,  1440,
		 1464,  1486,  1510,  1536,  1562,  1587,  1617,  1646,
		 1674,  1703,  1738,  1773,  1807,  1842,  1878,  1918,
		 1956,  1994,  2040,  2092,  2140,  2189,  2241,  2295,
		 2358,  2416,  2477,  2544,  2615,  2677,  2748,  2831,
		 2922,  3020,  3132,  3228,  3324,  3419,  3514,  3610,
		 3705,  3800,  3894,  3989,  4084,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 8 */
		 1025,  1045,  1057,  1070,  1087,  1100,  1116,  1134,
		 1152,  1171,  1190,  1210,  1227,  1250,  1270,  1288,
		 1307,  1326,  1347,  1366,  1385,  1409,  1435,  1462,
		 1485,  1508,  1535,  1565,  1594,  1620,  1646,  1675,
		 1707,  1741,  1774,  1810,  1847,  1881,  1923,  1971,
		 2017,  2057,  2101,  2151,  2207,  2268,  2327,  2386,
		 2452,  2518,  2588,  2661,  2744,  2821,  2900,  2996,
		 3096,  3204,  3335,  3415,  3494,  3573,  3653,  3732,
		 3811,  3889,  3968,  4047,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1037,  1048,  1062,  1079,  1092,  1107,  1123,
		 1138,  1154,  1172,  1190,  1206,  1226,  1246,  1266,
		 1285,  1304,  1324,  1344,  1364,  1384,  1405,  1428,
		 1451,  1472,  1494,  1521,  1546,  1568,  1591,  1619,
		 1649,  1678,  1705,  1737,  1771,  1804,  1844,  1890,
		 1932,  1965,  2002,  2045,  2094,  2144,  2195,  2248,
		 2308,  2368,  2434,  2502,  2574,  2637,  2708,  2796,
		 2888,  2984,  3103,  3183,  3262,  3342,  3421,  3500,
		 3579,  3658,  3737,  3816,  3894,  3973,  4051,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1020,  1033,  1045,  1059,  1075,  1086,  1102,  1119,
		 1134,  1152,  1172,  1191,  1206,  1225,  1243,  1259,
		 1274,  1290,  1307,  1327,  1346,  1367,  1387,  1409,
		 1429,  1446,  1467,  1493,  1518,  1541,  1565,  1592,
		 1622,  1652,  1679,  1709,  1740,  1769,  1806,  1846,
		 1884,  1917,  1955,  2000,  2048,  2100,  2151,  2205,
		 2265,  2326,  2389,  2456,  2528,  2593,  2663,  2750,
		 2841,  2936,  3051,  3130,  3210,  3290,  3369,  3448,
		 3527,  3606,  3685,  3764,  3843,  3921,  3999,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 9 */
		 1024,  1039,  1051,  1066,  1085,  1102,  1118,  1135,
		 1154,  1171,  1189,  1210,  1230,  1250,  1268,  1289,
		 1309,  1331,  1350,  1372,  1393,  1414,  1436,  1464,
		 1490,  1514,  1539,  1567,  1594,  1622,  1651,  1678,
		 1709,  1743,  1779,  1818,  1855,  1890,  1927,  1974,
		 2018,  2060,  2105,  2159,  2211,  2263,  2321,  2384,
		 2451,  2519,  2587,  2662,  2738,  2814,  2894,  2987,
		 3091,  3205,  3318,  3395,  3473,  3551,  3629,  3706,
		 3784,  3861,  3938,  4015,  4092,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1037,  1047,  1060,  1078,  1094,  1109,  1125,
		 1141,  1158,  1176,  1196,  1215,  1233,  1250,  1270,
		 1289,  1310,  1329,  1351,  1370,  1389,  1409,  1436,
		 1460,  1482,  1505,  1529,  1552,  1577,  1603,  1629,
		 1655,  1686,  1720,  1755,  1787,  1820,  1854,  1896,
		 1935,  1972,  2011,  2057,  2103,  2151,  2201,  2255,
		 2314,  2376,  2435,  2498,  2567,  2637,  2707,  2786,
		 2880,  2984,  3083,  3161,  3239,  3317,  3395,  3472,
		 3550,  3627,  3705,  3782,  3859,  3936,  4013,  4090,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1023,  1039,  1049,  1060,  1078,  1094,  1108,  1123,
		 1139,  1155,  1172,  1190,  1207,  1224,  1241,  1259,
		 1276,  1294,  1311,  1330,  1348,  1364,  1382,  1406,
		 1429,  1449,  1469,  1493,  1515,  1540,  1565,  1588,
		 1613,  1641,  1672,  1706,  1738,  1768,  1800,  1842,
		 1881,  1916,  1954,  1999,  2045,  2091,  2139,  2190,
		 2248,  2307,  2366,  2430,  2496,  2561,  2626,  2702,
		 2788,  2885,  2988,  3066,  3144,  3222,  3300,  3378,
		 3455,  3533,  3610,  3687,  3765,  3842,  3919,  3995,
		 4072,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 10 */
		 1022,  1043,  1061,  1079,  1100,  1122,  1140,  1157,
		 1173,  1189,  1207,  1230,  1256,  1280,  1299,  1320,
		 1341,  1364,  1387,  1412,  1439,  1469,  1497,  1522,
		 1545,  1571,  1600,  1632,  1664,  1694,  1724,  1758,
		 1795,  1835,  1875,  1917,  1958,  2005,  2048,  2095,
		 2140,  2195,  2251,  2312,  2375,  2449,  2518,  2584,
		 2646,  2720,  2812,  2899,  2984,  3063,  3157,  3266,
		 3390,  3518,  3635,  3724,  3812,  3901,  3990,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1026,  1035,  1051,  1066,  1081,  1096,  1109,  1122,
		 1135,  1149,  1164,  1184,  1207,  1227,  1244,  1262,
		 1281,  1299,  1317,  1337,  1355,  1376,  1397,  1420,
		 1442,  1464,  1487,  1512,  1537,  1560,  1585,  1612,
		 1641,  1673,  1703,  1735,  1768,  1808,  1844,  1883,
		 1918,  1962,  2007,  2057,  2105,  2163,  2219,  2272,
		 2322,  2381,  2453,  2524,  2593,  2656,  2725,  2811,
		 2908,  3013,  3114,  3203,  3293,  3382,  3471,  3559,
		 3648,  3737,  3825,  3913,  4001,  4089,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1030,  1042,  1055,  1069,  1084,  1097,  1110,
		 1120,  1131,  1143,  1162,  1182,  1198,  1211,  1226,
		 1244,  1258,  1271,  1288,  1307,  1328,  1349,  1370,
		 1388,  1407,  1428,  1452,  1476,  1500,  1524,  1551,
		 1579,  1610,  1638,  1664,  1690,  1723,  1756,  1790,
		 1820,  1857,  1897,  1943,  1988,  2041,  2092,  2143,
		 2191,  2246,  2313,  2380,  2444,  2503,  2568,  2648,
		 2740,  2838,  2932,  3021,  3110,  3199,  3288,  3377,
		 3466,  3555,  3643,  3732,  3820,  3908,  3996,  4084,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	},
	{
		/* 11 */
		 1022,  1043,  1061,  1079,  1100,  1122,  1140,  1157,
		 1173,  1189,  1207,  1230,  1256,  1280,  1299,  1320,
		 1341,  1364,  1387,  1412,  1439,  1469,  1497,  1522,
		 1545,  1571,  1600,  1632,  1664,  1694,  1724,  1758,
		 1795,  1835,  1875,  1917,  1958,  2005,  2048,  2095,
		 2140,  2195,  2251,  2312,  2375,  2449,  2518,  2584,
		 2646,  2720,  2812,  2899,  2984,  3063,  3157,  3266,
		 3390,  3518,  3635,  3724,  3812,  3901,  3990,  4078,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1026,  1035,  1051,  1066,  1081,  1096,  1109,  1122,
		 1135,  1149,  1164,  1184,  1207,  1227,  1244,  1262,
		 1281,  1299,  1317,  1337,  1355,  1376,  1397,  1420,
		 1442,  1464,  1487,  1512,  1537,  1560,  1585,  1612,
		 1641,  1673,  1703,  1735,  1768,  1808,  1844,  1883,
		 1918,  1962,  2007,  2057,  2105,  2163,  2219,  2272,
		 2322,  2381,  2453,  2524,  2593,  2656,  2725,  2811,
		 2908,  3013,  3114,  3203,  3293,  3382,  3471,  3559,
		 3648,  3737,  3825,  3913,  4001,  4089,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 1025,  1030,  1042,  1055,  1069,  1084,  1097,  1110,
		 1120,  1131,  1143,  1162,  1182,  1198,  1211,  1226,
		 1244,  1258,  1271,  1288,  1307,  1328,  1349,  1370,
		 1388,  1407,  1428,  1452,  1476,  1500,  1524,  1551,
		 1579,  1610,  1638,  1664,  1690,  1723,  1756,  1790,
		 1820,  1857,  1897,  1943,  1988,  2041,  2092,  2143,
		 2191,  2246,  2313,  2380,  2444,  2503,  2568,  2648,
		 2740,  2838,  2932,  3021,  3110,  3199,  3288,  3377,
		 3466,  3555,  3643,  3732,  3820,  3908,  3996,  4084,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095,
		 4095,  4095,  4095,  4095,  4095,  4095,  4095,  4095
	}

	},
	.linear_tbl = {
		/* R */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1344, 1360, 1376, 1392,
		 1408, 1424, 1440, 1456, 1472, 1488, 1504, 1520,
		 1536, 1552, 1568, 1584, 1600, 1616, 1632, 1648,
		 1664, 1680, 1696, 1712, 1728, 1744, 1760, 1776,
		 1792, 1808, 1824, 1840, 1856, 1872, 1888, 1904,
		 1920, 1936, 1952, 1968, 1984, 2000, 2016, 2032,
		 2048, 2064, 2080, 2096, 2112, 2128, 2144, 2160,
		 2176, 2192, 2208, 2224, 2240, 2256, 2272, 2288,
		 2304, 2320, 2336, 2352, 2368, 2384, 2400, 2416,
		 2432, 2448, 2464, 2480, 2496, 2512, 2528, 2544,
		 2560, 2576, 2592, 2608, 2624, 2640, 2656, 2672,
		 2688, 2704, 2720, 2736, 2752, 2768, 2784, 2800,
		 2816, 2832, 2848, 2864, 2880, 2896, 2912, 2928,
		 2944, 2960, 2976, 2992, 3008, 3024, 3040, 3056,
		 3072, 3088, 3104, 3120, 3136, 3152, 3168, 3184,
		 3200, 3216, 3232, 3248, 3264, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* G */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* B */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080
	},
	.disc_tbl = {
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0
	},
	.isp_cem_table = {
		0x40, 0x3F, 0x48, 0x3F, 0x40, 0x47, 0x48, 0x47,
		0x50, 0x3F, 0x58, 0x3F, 0x50, 0x47, 0x58, 0x47,
		0x60, 0x3F, 0x68, 0x3F, 0x60, 0x47, 0x68, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x4F, 0x48, 0x4F, 0x40, 0x57, 0x48, 0x57,
		0x50, 0x4F, 0x58, 0x4F, 0x50, 0x57, 0x58, 0x57,
		0x60, 0x4F, 0x68, 0x4F, 0x60, 0x57, 0x68, 0x57,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x5F, 0x48, 0x5F, 0x40, 0x67, 0x48, 0x67,
		0x50, 0x5F, 0x58, 0x5F, 0x50, 0x67, 0x58, 0x67,
		0x60, 0x5F, 0x68, 0x5F, 0x60, 0x67, 0x68, 0x67,
		0x70, 0x5F, 0x78, 0x60, 0x70, 0x67, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x6F, 0x48, 0x6F, 0x40, 0x77, 0x48, 0x77,
		0x50, 0x6F, 0x58, 0x6F, 0x50, 0x77, 0x58, 0x77,
		0x60, 0x6F, 0x68, 0x6F, 0x60, 0x77, 0x68, 0x77,
		0x70, 0x6F, 0x78, 0x6F, 0x70, 0x77, 0x78, 0x77,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x7F, 0x48, 0x7F, 0x3F, 0x87, 0x47, 0x87,
		0x50, 0x7F, 0x58, 0x7F, 0x4F, 0x87, 0x57, 0x87,
		0x60, 0x7F, 0x68, 0x7F, 0x5F, 0x87, 0x67, 0x87,
		0x70, 0x7F, 0x78, 0x7F, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x8F, 0x88, 0x8F, 0x80, 0x97, 0x88, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0x9F, 0x88, 0x9F, 0x80, 0xA7, 0x88, 0xA7,
		0x90, 0x9F, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xAF, 0x88, 0xAF, 0x80, 0xB7, 0x88, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x90, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0x35, 0x54, 0x36, 0x4B, 0x3D, 0x53, 0x3E,
		0x5C, 0x38, 0x62, 0x3A, 0x5B, 0x40, 0x63, 0x41,
		0x66, 0x3D, 0x69, 0x3F, 0x67, 0x44, 0x6B, 0x46,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x99, 0x40, 0x90, 0x48, 0x99, 0x48,
		0xA2, 0x41, 0xAC, 0x42, 0xA3, 0x4A, 0xAC, 0x4B,
		0xB5, 0x44, 0xBD, 0x45, 0xB4, 0x4C, 0xBC, 0x4C,
		0xC4, 0x45, 0x00, 0x00, 0xC3, 0x4D, 0x00, 0x00,
		0x49, 0x44, 0x51, 0x46, 0x48, 0x4C, 0x50, 0x4E,
		0x59, 0x47, 0x61, 0x49, 0x58, 0x4F, 0x60, 0x51,
		0x68, 0x4B, 0x6C, 0x4D, 0x67, 0x52, 0x6C, 0x52,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x9A, 0x51, 0x93, 0x52, 0x9F, 0x52,
		0xA3, 0x52, 0xAD, 0x52, 0xAB, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xBE, 0x52, 0xC0, 0x52, 0xCB, 0x52,
		0xC7, 0x52, 0x00, 0x00, 0xD3, 0x52, 0x00, 0x00,
		0x47, 0x54, 0x4E, 0x56, 0x45, 0x5C, 0x4D, 0x5E,
		0x56, 0x57, 0x5B, 0x55, 0x55, 0x5F, 0x5B, 0x5F,
		0x60, 0x52, 0x69, 0x52, 0x5B, 0x58, 0x60, 0x52,
		0x6E, 0x52, 0x74, 0x52, 0x6C, 0x52, 0x71, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x99, 0x52, 0xA9, 0x52, 0xA3, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xC3, 0x52, 0xC7, 0x52, 0xD7, 0x52,
		0xD0, 0x52, 0xDA, 0x52, 0xE5, 0x52, 0xEB, 0x54,
		0xE4, 0x52, 0x00, 0x00, 0xEB, 0x59, 0x00, 0x00,
		0x44, 0x64, 0x4B, 0x65, 0x42, 0x6C, 0x4A, 0x6D,
		0x53, 0x67, 0x5B, 0x68, 0x52, 0x6F, 0x5A, 0x70,
		0x5B, 0x64, 0x5B, 0x5C, 0x5B, 0x6F, 0x5B, 0x6B,
		0x5F, 0x51, 0x6D, 0x4F, 0x5A, 0x63, 0x6D, 0x64,
		0x80, 0x53, 0x97, 0x56, 0x80, 0x6C, 0x95, 0x6D,
		0xAF, 0x57, 0xC5, 0x58, 0xA8, 0x6D, 0xB6, 0x6E,
		0xD7, 0x58, 0xDC, 0x5D, 0xD1, 0x6B, 0xDB, 0x6D,
		0xE5, 0x5F, 0xEB, 0x61, 0xE5, 0x6F, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x75, 0x48, 0x76, 0x3F, 0x81, 0x47, 0x82,
		0x50, 0x77, 0x58, 0x79, 0x4F, 0x83, 0x57, 0x85,
		0x5B, 0x79, 0x5B, 0x79, 0x5B, 0x87, 0x5B, 0x8B,
		0x5C, 0x79, 0x6C, 0x7C, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x8D, 0x80, 0x80, 0x8F, 0x92, 0x92,
		0xA5, 0x80, 0xB6, 0x80, 0xA3, 0x91, 0xB6, 0x92,
		0xD1, 0x80, 0xDB, 0x80, 0xD1, 0x94, 0xDB, 0x92,
		0xE5, 0x80, 0xEB, 0x80, 0xE5, 0x90, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x3F, 0x8D, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5B, 0x91, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x5F, 0xA0, 0x6D, 0xA6, 0x5E, 0xB2, 0x71, 0xB6,
		0x83, 0xA7, 0x92, 0xA4, 0x86, 0xC7, 0x97, 0xBD,
		0xA6, 0xA6, 0xBC, 0xA8, 0xA5, 0xB8, 0xB4, 0xB4,
		0xCC, 0xA6, 0xD8, 0xA3, 0xC4, 0xB3, 0xD1, 0xB0,
		0xE5, 0xA1, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBE,
		0x5E, 0xC9, 0x70, 0xD6, 0x5D, 0xE2, 0x74, 0xD9,
		0x87, 0xD9, 0x95, 0xC6, 0x87, 0xD6, 0x94, 0xCF,
		0xA3, 0xC3, 0xB0, 0xC0, 0xA0, 0xCA, 0xAA, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB7, 0x5B, 0xCE, 0x5B, 0xC2, 0x5B, 0xDE,
		0x65, 0xE2, 0x77, 0xDD, 0x69, 0xE4, 0x79, 0xDC,
		0x87, 0xD6, 0x92, 0xD0, 0x87, 0xD6, 0x90, 0xD1,
		0x9C, 0xCB, 0xA5, 0xC7, 0x99, 0xCD, 0xA1, 0xC9,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA9, 0xC5, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xCD, 0x5D, 0xEA, 0x00, 0x00, 0x00, 0x00,
		0x6D, 0xE2, 0x7B, 0xDB, 0x00, 0x00, 0x00, 0x00,
		0x87, 0xD6, 0x8F, 0xD2, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xCE, 0x9F, 0xCA, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC7, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x55, 0x24, 0x5C, 0x24, 0x4D, 0x24, 0x55, 0x24,
		0x64, 0x24, 0x66, 0x24, 0x5D, 0x24, 0x66, 0x24,
		0x64, 0x24, 0x63, 0x24, 0x67, 0x24, 0x64, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8C, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x98, 0x24, 0xA5, 0x24, 0x9C, 0x24, 0xAD, 0x24,
		0xB7, 0x24, 0xC7, 0x29, 0xC3, 0x24, 0xC7, 0x37,
		0xC7, 0x3C, 0xC7, 0x45, 0xC7, 0x44, 0xC7, 0x4C,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x44, 0x24, 0x4C, 0x24, 0x38, 0x24, 0x41, 0x24,
		0x55, 0x24, 0x5F, 0x24, 0x4A, 0x24, 0x55, 0x24,
		0x67, 0x24, 0x64, 0x24, 0x60, 0x22, 0x66, 0x21,
		0x63, 0x24, 0x70, 0x24, 0x64, 0x22, 0x6D, 0x23,
		0x80, 0x24, 0x90, 0x24, 0x80, 0x25, 0x93, 0x26,
		0xA0, 0x24, 0xB7, 0x24, 0xA8, 0x27, 0xC4, 0x28,
		0xC7, 0x31, 0xC7, 0x42, 0xC4, 0x43, 0xC7, 0x4C,
		0xC7, 0x4C, 0xC7, 0x53, 0xC7, 0x54, 0xC7, 0x5A,
		0xC7, 0x58, 0x00, 0x00, 0xC7, 0x5D, 0x00, 0x00,
		0x37, 0x33, 0x37, 0x2A, 0x37, 0x42, 0x37, 0x3C,
		0x3C, 0x24, 0x48, 0x24, 0x37, 0x33, 0x37, 0x26,
		0x54, 0x22, 0x62, 0x1D, 0x42, 0x21, 0x53, 0x20,
		0x63, 0x1E, 0x68, 0x21, 0x6C, 0x35, 0x69, 0x36,
		0x80, 0x27, 0x96, 0x2A, 0x80, 0x3D, 0x96, 0x3F,
		0xB2, 0x2C, 0xBF, 0x44, 0xB0, 0x4A, 0xBC, 0x53,
		0xC4, 0x4E, 0xC7, 0x56, 0xC4, 0x5A, 0xC7, 0x5E,
		0xC7, 0x5C, 0xC7, 0x60, 0xC7, 0x62, 0xC7, 0x65,
		0xC7, 0x62, 0x00, 0x00, 0xC7, 0x67, 0x00, 0x00,
		0x37, 0x4F, 0x37, 0x4B, 0x37, 0x5B, 0x37, 0x5A,
		0x37, 0x46, 0x37, 0x3F, 0x37, 0x57, 0x37, 0x54,
		0x35, 0x31, 0x39, 0x1F, 0x36, 0x4E, 0x34, 0x44,
		0x66, 0x48, 0x73, 0x56, 0x54, 0x52, 0x6F, 0x62,
		0x80, 0x58, 0x96, 0x5A, 0x80, 0x70, 0x93, 0x70,
		0xA5, 0x64, 0xBC, 0x62, 0xA4, 0x71, 0xBA, 0x6D,
		0xC3, 0x64, 0xC7, 0x66, 0xC3, 0x6F, 0xC7, 0x71,
		0xC7, 0x69, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x7B, 0x37, 0x7D,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x80, 0x37, 0x85,
		0x39, 0x68, 0x3F, 0x6A, 0x3E, 0x8B, 0x4D, 0x90,
		0x58, 0x73, 0x6B, 0x7B, 0x5E, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x92, 0x80, 0x81, 0x8F, 0x92, 0x92,
		0xA4, 0x80, 0xBA, 0x80, 0xA4, 0x92, 0xB9, 0x93,
		0xC3, 0x80, 0xC7, 0x80, 0xC3, 0x90, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x8D, 0x37, 0x92, 0x37, 0x9A, 0x37, 0x9E,
		0x37, 0x97, 0x37, 0x9C, 0x37, 0xA3, 0x37, 0xAB,
		0x3F, 0xA0, 0x4D, 0xA1, 0x3F, 0xB0, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA3, 0x5D, 0xB6, 0x72, 0xBC,
		0x86, 0xA9, 0x93, 0xA4, 0x8B, 0xC8, 0x98, 0xBC,
		0xA4, 0xA4, 0xBA, 0xA6, 0xA6, 0xB8, 0xB9, 0xB9,
		0xC3, 0xA1, 0xC7, 0x9C, 0xC3, 0xB2, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA3, 0x37, 0xA8, 0x37, 0xAC, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x3E, 0xC1, 0x4A, 0xC6, 0x3E, 0xD2, 0x51, 0xD3,
		0x5F, 0xCB, 0x75, 0xD4, 0x66, 0xD5, 0x79, 0xD8,
		0x8F, 0xDF, 0x9D, 0xD2, 0x8F, 0xDB, 0x9B, 0xD7,
		0xAA, 0xCD, 0xB8, 0xCB, 0xA8, 0xD5, 0xB4, 0xD4,
		0xC3, 0xC3, 0xC7, 0xB9, 0xC3, 0xD3, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x40, 0xDA,
		0x45, 0xDA, 0x59, 0xDA, 0x51, 0xDA, 0x61, 0xDA,
		0x6C, 0xDA, 0x7D, 0xDA, 0x71, 0xDA, 0x7F, 0xDA,
		0x8E, 0xDA, 0x99, 0xDA, 0x8E, 0xDA, 0x98, 0xDA,
		0xA5, 0xDA, 0xB1, 0xDA, 0xA2, 0xDA, 0xAC, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB6, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x49, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x58, 0xDA, 0x67, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x74, 0xDA, 0x81, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xDA, 0x96, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x9F, 0xDA, 0xA8, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xDA, 0xBA, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x37,
		0x68, 0x33, 0x6A, 0x32, 0x63, 0x35, 0x6A, 0x32,
		0x69, 0x32, 0x68, 0x33, 0x6A, 0x32, 0x69, 0x32,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8C, 0x21, 0x80, 0x27, 0x8E, 0x20,
		0x9B, 0x1A, 0xA3, 0x29, 0x9F, 0x18, 0xA3, 0x38,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3C, 0x58, 0x3A, 0x4D, 0x3F, 0x52, 0x3D,
		0x5E, 0x37, 0x64, 0x34, 0x57, 0x3B, 0x5E, 0x37,
		0x6B, 0x31, 0x69, 0x32, 0x66, 0x34, 0x6A, 0x32,
		0x68, 0x33, 0x72, 0x2E, 0x69, 0x32, 0x6F, 0x2F,
		0x80, 0x27, 0x91, 0x1F, 0x80, 0x2C, 0x92, 0x29,
		0xA1, 0x22, 0xA3, 0x45, 0xA1, 0x37, 0xA3, 0x52,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x46, 0x43, 0x4A, 0x41, 0x3E, 0x47, 0x41, 0x45,
		0x50, 0x3E, 0x56, 0x3B, 0x46, 0x43, 0x4D, 0x41,
		0x5E, 0x37, 0x68, 0x33, 0x56, 0x3F, 0x61, 0x3F,
		0x6A, 0x36, 0x6E, 0x39, 0x6D, 0x3B, 0x6C, 0x42,
		0x80, 0x36, 0x94, 0x33, 0x80, 0x45, 0x91, 0x4D,
		0xA1, 0x49, 0xA3, 0x5E, 0xA0, 0x5B, 0xA3, 0x66,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x33, 0x4C, 0x36, 0x4B, 0x26, 0x52, 0x28, 0x51,
		0x3E, 0x4B, 0x46, 0x4C, 0x30, 0x53, 0x3A, 0x55,
		0x4D, 0x4B, 0x58, 0x4A, 0x45, 0x58, 0x5C, 0x63,
		0x68, 0x4E, 0x73, 0x57, 0x68, 0x67, 0x73, 0x69,
		0x80, 0x5C, 0x91, 0x63, 0x80, 0x6F, 0x91, 0x73,
		0xA0, 0x68, 0xA3, 0x6E, 0xA0, 0x72, 0xA3, 0x74,
		0xA3, 0x71, 0xA8, 0x71, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x5C, 0x13, 0x5C, 0x13, 0x78, 0x13, 0x7C,
		0x1B, 0x5E, 0x26, 0x62, 0x1D, 0x80, 0x2B, 0x86,
		0x32, 0x66, 0x4B, 0x6E, 0x3B, 0x8C, 0x4F, 0x8F,
		0x5B, 0x75, 0x72, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x91, 0x80, 0x82, 0x92, 0x91, 0x91,
		0xA0, 0x80, 0xA3, 0x80, 0xA0, 0x90, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x93, 0x13, 0x9B, 0x13, 0xA7, 0x14, 0xAC,
		0x1F, 0x9F, 0x2E, 0x9F, 0x24, 0xAC, 0x34, 0xAC,
		0x3D, 0xA0, 0x4E, 0xA0, 0x44, 0xAC, 0x53, 0xAC,
		0x5E, 0xA1, 0x71, 0xA2, 0x63, 0xAC, 0x76, 0xAC,
		0x86, 0xA6, 0x93, 0xA2, 0x87, 0xAC, 0x92, 0xAC,
		0xA0, 0xA0, 0xA3, 0x97, 0x9E, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x24, 0xAC, 0x30, 0xAC, 0x37, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x67, 0xAC,
		0x6C, 0xAC, 0x7A, 0xAC, 0x72, 0xAC, 0x7C, 0xAC,
		0x87, 0xAC, 0x8F, 0xAC, 0x87, 0xAC, 0x8D, 0xAC,
		0x98, 0xAC, 0xA1, 0xAC, 0x95, 0xAC, 0x9C, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x91, 0x50, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x71, 0x61,
		0x75, 0x5D, 0x78, 0x60, 0x78, 0x64, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x5D, 0x6A,
		0x67, 0x66, 0x6E, 0x68, 0x64, 0x6D, 0x6C, 0x70,
		0x76, 0x6B, 0x7A, 0x6E, 0x73, 0x73, 0x7B, 0x75,
		0x80, 0x70, 0x89, 0x70, 0x80, 0x78, 0x89, 0x79,
		0x92, 0x72, 0x99, 0x73, 0x90, 0x79, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x3F, 0x7B, 0x47, 0x7E,
		0x52, 0x70, 0x5A, 0x73, 0x4F, 0x80, 0x57, 0x83,
		0x61, 0x75, 0x69, 0x78, 0x5F, 0x85, 0x67, 0x87,
		0x70, 0x7A, 0x78, 0x7D, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x81, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8B, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x7A, 0x98,
		0x82, 0x8F, 0x88, 0x8F, 0x83, 0x97, 0x89, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x45, 0x3B, 0x4C, 0x3B, 0x44, 0x43, 0x4C, 0x43,
		0x54, 0x3C, 0x5C, 0x3D, 0x54, 0x44, 0x5C, 0x45,
		0x62, 0x3E, 0x68, 0x3F, 0x63, 0x46, 0x69, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA1, 0x40, 0xA9, 0x41, 0xA1, 0x48, 0xA9, 0x49,
		0xB2, 0x41, 0xBA, 0x42, 0xB2, 0x49, 0xB9, 0x49,
		0xC1, 0x42, 0x00, 0x00, 0xC1, 0x4A, 0x00, 0x00,
		0x43, 0x4B, 0x4B, 0x4B, 0x43, 0x53, 0x4B, 0x53,
		0x53, 0x4C, 0x5B, 0x4D, 0x53, 0x54, 0x5B, 0x55,
		0x63, 0x4D, 0x69, 0x4F, 0x63, 0x55, 0x6A, 0x56,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x99, 0x58,
		0xA1, 0x51, 0xA9, 0x51, 0xA1, 0x59, 0xA9, 0x59,
		0xB1, 0x51, 0xB9, 0x51, 0xB1, 0x59, 0xB9, 0x59,
		0xC0, 0x51, 0x00, 0x00, 0xC0, 0x59, 0x00, 0x00,
		0x42, 0x5B, 0x4A, 0x5B, 0x41, 0x63, 0x49, 0x63,
		0x52, 0x5C, 0x5A, 0x5D, 0x51, 0x64, 0x59, 0x65,
		0x62, 0x5D, 0x6A, 0x5E, 0x61, 0x65, 0x69, 0x66,
		0x71, 0x5F, 0x78, 0x60, 0x71, 0x66, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x99, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA1, 0x61, 0xA9, 0x61, 0xA0, 0x69, 0xA8, 0x68,
		0xB0, 0x61, 0xB8, 0x61, 0xB0, 0x68, 0xB8, 0x68,
		0xC0, 0x61, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x41, 0x6B, 0x49, 0x6B, 0x40, 0x73, 0x48, 0x73,
		0x51, 0x6C, 0x59, 0x6D, 0x50, 0x74, 0x58, 0x75,
		0x61, 0x6D, 0x69, 0x6E, 0x60, 0x75, 0x68, 0x76,
		0x6D, 0x6A, 0x75, 0x69, 0x6D, 0x75, 0x6E, 0x6B,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x6B, 0x94, 0x6C,
		0x98, 0x69, 0xA4, 0x69, 0xA7, 0x6D, 0xB8, 0x6D,
		0xAF, 0x69, 0xBA, 0x69, 0xC9, 0x6D, 0xDC, 0x6D,
		0xC5, 0x69, 0xCF, 0x69, 0xE8, 0x6E, 0xF5, 0x6F,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x7B, 0x48, 0x7C, 0x3F, 0x85, 0x47, 0x85,
		0x50, 0x7C, 0x58, 0x7D, 0x4F, 0x86, 0x57, 0x86,
		0x60, 0x7D, 0x68, 0x7E, 0x5F, 0x87, 0x67, 0x87,
		0x6D, 0x7E, 0x6E, 0x7E, 0x6D, 0x89, 0x6F, 0x90,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x93, 0x92, 0x92,
		0xA3, 0x80, 0xB7, 0x80, 0xA4, 0x92, 0xB2, 0x90,
		0xCA, 0x80, 0xD4, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x6F, 0xA2, 0x6D, 0x9B, 0x70, 0xB1,
		0x81, 0xAA, 0x91, 0xA1, 0x81, 0xAC, 0x8D, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA5, 0x74, 0xB2, 0x6D, 0xAF, 0x77, 0xB0,
		0x81, 0xAC, 0x8A, 0xA7, 0x81, 0xAC, 0x89, 0xA8,
		0x92, 0xA3, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6F, 0xB4, 0x79, 0xB0, 0x71, 0xB8, 0x79, 0xB8,
		0x81, 0xAF, 0x89, 0xAF, 0x81, 0xB7, 0x89, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x91, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x69, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x71, 0xBF, 0x79, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x82, 0xBF, 0x89, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x54, 0x30, 0x5B, 0x33, 0x51, 0x38, 0x59, 0x3A,
		0x63, 0x35, 0x67, 0x38, 0x60, 0x3B, 0x67, 0x3B,
		0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x8A, 0x3B,
		0x92, 0x3B, 0x9B, 0x3B, 0x94, 0x3B, 0xA1, 0x3B,
		0xA8, 0x3B, 0xB5, 0x3B, 0xB0, 0x3B, 0xBF, 0x3B,
		0xC4, 0x3B, 0xCE, 0x3B, 0xCC, 0x3B, 0xD8, 0x3B,
		0xD7, 0x3B, 0x00, 0x00, 0xD9, 0x42, 0x00, 0x00,
		0x4C, 0x3B, 0x52, 0x3B, 0x49, 0x43, 0x49, 0x3B,
		0x5A, 0x3B, 0x61, 0x3B, 0x51, 0x3B, 0x5A, 0x3B,
		0x68, 0x3B, 0x69, 0x3B, 0x63, 0x3B, 0x69, 0x3B,
		0x6A, 0x3B, 0x74, 0x3B, 0x6A, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8E, 0x3B,
		0x98, 0x3B, 0xA8, 0x3B, 0x9E, 0x3B, 0xB2, 0x3B,
		0xBA, 0x3B, 0xCA, 0x3B, 0xC8, 0x3B, 0xD8, 0x3B,
		0xD8, 0x3B, 0xD9, 0x44, 0xD9, 0x45, 0xD9, 0x4E,
		0xD9, 0x4B, 0x00, 0x00, 0xD9, 0x52, 0x00, 0x00,
		0x49, 0x4E, 0x49, 0x48, 0x49, 0x57, 0x49, 0x53,
		0x49, 0x40, 0x4F, 0x3B, 0x49, 0x4E, 0x49, 0x45,
		0x5A, 0x3B, 0x65, 0x3A, 0x4C, 0x3B, 0x58, 0x39,
		0x68, 0x3B, 0x6E, 0x3B, 0x67, 0x36, 0x69, 0x37,
		0x80, 0x3C, 0x91, 0x3C, 0x80, 0x3D, 0x96, 0x40,
		0xA7, 0x3D, 0xC2, 0x3D, 0xB4, 0x42, 0xCD, 0x43,
		0xD6, 0x3D, 0xD7, 0x49, 0xCE, 0x52, 0xD4, 0x56,
		0xD9, 0x50, 0xD9, 0x55, 0xD9, 0x59, 0xD9, 0x5D,
		0xD9, 0x59, 0x00, 0x00, 0xD9, 0x60, 0x00, 0x00,
		0x46, 0x5F, 0x49, 0x5E, 0x44, 0x67, 0x49, 0x68,
		0x49, 0x5A, 0x49, 0x55, 0x49, 0x66, 0x49, 0x64,
		0x49, 0x4E, 0x48, 0x3E, 0x49, 0x60, 0x48, 0x59,
		0x58, 0x39, 0x71, 0x53, 0x48, 0x4C, 0x6F, 0x62,
		0x80, 0x57, 0x96, 0x58, 0x80, 0x6A, 0x97, 0x6C,
		0xB1, 0x5A, 0xBC, 0x60, 0xA4, 0x70, 0xBB, 0x6D,
		0xCD, 0x5F, 0xD4, 0x61, 0xCC, 0x6C, 0xD4, 0x6F,
		0xD9, 0x63, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x42, 0x6F, 0x49, 0x71, 0x3F, 0x7E, 0x47, 0x80,
		0x49, 0x71, 0x49, 0x71, 0x49, 0x82, 0x49, 0x85,
		0x49, 0x71, 0x4B, 0x72, 0x49, 0x8A, 0x4F, 0x8F,
		0x51, 0x73, 0x6B, 0x7A, 0x5D, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8A, 0x8F, 0x8F,
		0xA3, 0x80, 0xBB, 0x80, 0xA3, 0x91, 0xBA, 0x93,
		0xCC, 0x80, 0xD4, 0x80, 0xCB, 0x92, 0xD4, 0x90,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x3F, 0x8C, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x49, 0x91, 0x49, 0x95, 0x49, 0x9A, 0x49, 0xA0,
		0x49, 0x9A, 0x4F, 0xA0, 0x49, 0xA8, 0x4E, 0xB1,
		0x5C, 0xA3, 0x6E, 0xA7, 0x5A, 0xBA, 0x72, 0xB7,
		0x85, 0xAD, 0x93, 0xA5, 0x88, 0xBD, 0x95, 0xB6,
		0xA3, 0xA3, 0xBA, 0xA7, 0xA4, 0xB6, 0xBA, 0xBA,
		0xCB, 0xA5, 0xD4, 0xA1, 0xCC, 0xB9, 0xD4, 0xB2,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x4E, 0xC2, 0x49, 0xC3, 0x4D, 0xD9,
		0x5A, 0xD3, 0x72, 0xDA, 0x60, 0xE2, 0x75, 0xEB,
		0x8D, 0xE9, 0x9D, 0xD8, 0x8F, 0xF7, 0x9E, 0xE9,
		0xAB, 0xD0, 0xB9, 0xCC, 0xAB, 0xE0, 0xB6, 0xD8,
		0xCB, 0xCB, 0xD4, 0xC3, 0xC5, 0xD6, 0xD4, 0xD4,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCC,
		0x49, 0xD3, 0x50, 0xEA, 0x49, 0xE6, 0x57, 0xF0,
		0x66, 0xEA, 0x79, 0xEF, 0x6A, 0xF1, 0x7C, 0xF1,
		0x8F, 0xF4, 0x9C, 0xEE, 0x8E, 0xF2, 0x9B, 0xF1,
		0xAA, 0xE9, 0xB6, 0xE4, 0xA8, 0xF1, 0xB4, 0xED,
		0xC1, 0xE2, 0xCF, 0xDF, 0xBE, 0xE8, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD9, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0xF1, 0x5E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x6E, 0xF1, 0x7E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xF1, 0x99, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xF1, 0xB0, 0xEF, 0x00, 0x00, 0x00, 0x00,
		0xB9, 0xEA, 0xC1, 0xE6, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0x13, 0x54, 0x0F, 0x46, 0x16, 0x4D, 0x13,
		0x5D, 0x0D, 0x60, 0x0D, 0x55, 0x0F, 0x60, 0x0D,
		0x5E, 0x0D, 0x5C, 0x0D, 0x60, 0x0D, 0x5E, 0x0D,
		0x63, 0x0D, 0x71, 0x0D, 0x5F, 0x0D, 0x6F, 0x0E,
		0x80, 0x0D, 0x8F, 0x0D, 0x80, 0x0E, 0x91, 0x0E,
		0x9E, 0x0D, 0xAE, 0x0D, 0xA2, 0x0E, 0xB5, 0x15,
		0xB5, 0x27, 0xB5, 0x3F, 0xB5, 0x37, 0xB5, 0x49,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x3D, 0x1A, 0x45, 0x17, 0x34, 0x1F, 0x3B, 0x1C,
		0x4D, 0x13, 0x57, 0x0E, 0x43, 0x18, 0x4C, 0x12,
		0x61, 0x0D, 0x5D, 0x0C, 0x58, 0x0C, 0x60, 0x0F,
		0x5B, 0x0C, 0x6D, 0x0E, 0x5D, 0x09, 0x69, 0x0D,
		0x80, 0x11, 0x93, 0x12, 0x80, 0x13, 0x96, 0x16,
		0xA6, 0x13, 0xB3, 0x2B, 0xAF, 0x18, 0xB3, 0x3E,
		0xB5, 0x44, 0xB5, 0x52, 0xB5, 0x50, 0xB5, 0x59,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x29, 0x24, 0x2F, 0x21, 0x25, 0x32, 0x25, 0x2B,
		0x37, 0x1D, 0x3F, 0x16, 0x29, 0x24, 0x30, 0x1E,
		0x4A, 0x0D, 0x5C, 0x0C, 0x38, 0x12, 0x55, 0x24,
		0x5E, 0x0D, 0x66, 0x19, 0x6C, 0x34, 0x6C, 0x42,
		0x80, 0x21, 0x98, 0x25, 0x80, 0x45, 0x94, 0x47,
		0xAC, 0x37, 0xB3, 0x50, 0xAB, 0x50, 0xB3, 0x5B,
		0xB5, 0x59, 0xB5, 0x60, 0xB5, 0x62, 0xB5, 0x67,
		0xB5, 0x65, 0xBA, 0x65, 0xB5, 0x6A, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x25, 0x43, 0x25, 0x3E, 0x25, 0x52, 0x25, 0x50,
		0x25, 0x38, 0x24, 0x2D, 0x25, 0x4D, 0x25, 0x49,
		0x23, 0x1E, 0x42, 0x2C, 0x23, 0x41, 0x34, 0x44,
		0x68, 0x4D, 0x71, 0x4F, 0x58, 0x55, 0x74, 0x6E,
		0x80, 0x55, 0x94, 0x5E, 0x80, 0x74, 0x93, 0x71,
		0xAA, 0x61, 0xB2, 0x67, 0xA9, 0x6F, 0xB2, 0x70,
		0xB5, 0x6A, 0xB5, 0x6C, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6F, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x62, 0x25, 0x62, 0x25, 0x79, 0x25, 0x7D,
		0x25, 0x62, 0x28, 0x63, 0x25, 0x80, 0x2C, 0x86,
		0x2C, 0x64, 0x3F, 0x6A, 0x3A, 0x8C, 0x4D, 0x90,
		0x5A, 0x73, 0x70, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x92, 0x80, 0x82, 0x96, 0x92, 0x92,
		0xA8, 0x80, 0xB2, 0x80, 0xA8, 0x94, 0xB2, 0x90,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x90, 0x25, 0x96, 0x25, 0xA0, 0x25, 0xA5,
		0x25, 0x9C, 0x2E, 0x9F, 0x25, 0xAC, 0x2E, 0xB0,
		0x3D, 0xA0, 0x4D, 0xA1, 0x3D, 0xB2, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA4, 0x5D, 0xB5, 0x72, 0xBB,
		0x87, 0xAB, 0x94, 0xA4, 0x8B, 0xC6, 0x98, 0xBB,
		0xA7, 0xA7, 0xB2, 0xA1, 0xA5, 0xB8, 0xB2, 0xB2,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAC, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x2F, 0xC0, 0x2E, 0xC3, 0x3C, 0xC3,
		0x3F, 0xC0, 0x4F, 0xC0, 0x49, 0xC3, 0x5A, 0xC3,
		0x63, 0xC1, 0x77, 0xC2, 0x6B, 0xC3, 0x7B, 0xC3,
		0x8B, 0xC3, 0x97, 0xC2, 0x8B, 0xC3, 0x95, 0xC3,
		0xA3, 0xC1, 0xB1, 0xC1, 0x9F, 0xC3, 0xAA, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x50, 0xC3,
		0x54, 0xC3, 0x62, 0xC3, 0x5C, 0xC3, 0x68, 0xC3,
		0x71, 0xC3, 0x7D, 0xC3, 0x74, 0xC3, 0x7F, 0xC3,
		0x8B, 0xC3, 0x93, 0xC3, 0x8B, 0xC3, 0x91, 0xC3,
		0x9C, 0xC3, 0xA5, 0xC3, 0x99, 0xC3, 0xA1, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA8, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x57, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x62, 0xC3, 0x6D, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC3, 0x81, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x8B, 0xC3, 0x91, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xC3, 0x9E, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC3, 0xAB, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x8A, 0x4F,
		0x91, 0x4F, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x6F, 0x5C,
		0x74, 0x5A, 0x76, 0x59, 0x75, 0x59, 0x74, 0x5A,
		0x80, 0x54, 0x8D, 0x4E, 0x80, 0x54, 0x90, 0x51,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x57, 0x67,
		0x63, 0x62, 0x68, 0x5F, 0x5A, 0x66, 0x60, 0x66,
		0x6F, 0x5C, 0x75, 0x5C, 0x68, 0x67, 0x75, 0x69,
		0x80, 0x5D, 0x90, 0x65, 0x80, 0x6E, 0x90, 0x74,
		0x92, 0x72, 0x99, 0x73, 0x91, 0x78, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x31, 0x7A, 0x33, 0x7D,
		0x4A, 0x6E, 0x4F, 0x6F, 0x37, 0x80, 0x2F, 0x86,
		0x56, 0x72, 0x60, 0x75, 0x25, 0x90, 0x4D, 0x90,
		0x6B, 0x79, 0x74, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x90, 0x80, 0x82, 0x91, 0x90, 0x90,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x05, 0x96, 0x28, 0x96, 0x3F, 0x97, 0x47, 0x97,
		0x3B, 0x96, 0x47, 0x96, 0x4F, 0x97, 0x57, 0x97,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x76, 0x96, 0x70, 0x98, 0x7A, 0x98,
		0x83, 0x96, 0x8C, 0x96, 0x83, 0x97, 0x89, 0x97,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_cem_table1 = {
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x5B, 0x52, 0x64, 0x52,
		0x70, 0x50, 0x78, 0x50, 0x6D, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x98, 0x50, 0x92, 0x52, 0x9B, 0x52,
		0xA0, 0x50, 0xA8, 0x50, 0xA4, 0x52, 0xAD, 0x52,
		0xB0, 0x50, 0xB8, 0x50, 0xB6, 0x52, 0xBF, 0x52,
		0xBF, 0x50, 0x00, 0x00, 0xC8, 0x52, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x5B, 0x5B, 0x5D, 0x52, 0x5B, 0x64, 0x5B, 0x5B,
		0x69, 0x52, 0x74, 0x52, 0x61, 0x52, 0x70, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x96, 0x52, 0xA2, 0x52, 0x9E, 0x52, 0xAD, 0x52,
		0xAD, 0x52, 0xB9, 0x52, 0xBC, 0x52, 0xCC, 0x52,
		0xC4, 0x52, 0xCF, 0x52, 0xDB, 0x52, 0xEA, 0x52,
		0xDA, 0x52, 0x00, 0x00, 0xEB, 0x57, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x5B, 0x6D, 0x5B, 0x67, 0x5B, 0x76, 0x5B, 0x73,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xDF, 0x60, 0xEB, 0x61, 0xDF, 0x70, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x5B, 0x80, 0x5B, 0x80, 0x5B, 0x89, 0x5B, 0x8C,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xDF, 0x80, 0xEB, 0x80, 0xDF, 0x8F, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x5B, 0x92, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xDF, 0x9F, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBC,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xD0, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9E, 0xCB, 0xA9, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB6, 0x5B, 0xC8, 0x5B, 0xBF, 0x5B, 0xD4,
		0x60, 0xDF, 0x70, 0xDF, 0x62, 0xE7, 0x72, 0xE0,
		0x80, 0xD9, 0x8D, 0xD2, 0x80, 0xD9, 0x8B, 0xD3,
		0x99, 0xCD, 0xA4, 0xC8, 0x96, 0xCE, 0x9F, 0xCA,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA8, 0xC6, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xC7, 0x5B, 0xE0, 0x00, 0x00, 0x00, 0x00,
		0x66, 0xE5, 0x74, 0xDF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xD9, 0x8A, 0xD4, 0x00, 0x00, 0x00, 0x00,
		0x94, 0xCF, 0x9C, 0xCB, 0x00, 0x00, 0x00, 0x00,
		0xA4, 0xC8, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x37, 0x37, 0x37, 0x2D, 0x37, 0x40, 0x37, 0x37,
		0x3B, 0x24, 0x46, 0x24, 0x37, 0x2B, 0x3E, 0x24,
		0x52, 0x24, 0x5D, 0x24, 0x4B, 0x24, 0x58, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8B, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x96, 0x24, 0xA2, 0x24, 0x9A, 0x24, 0xA7, 0x24,
		0xAD, 0x24, 0xB9, 0x24, 0xB4, 0x24, 0xC1, 0x24,
		0xC4, 0x24, 0xC7, 0x2E, 0xC7, 0x2C, 0xC7, 0x38,
		0xC7, 0x37, 0x00, 0x00, 0xC7, 0x40, 0x00, 0x00,
		0x37, 0x49, 0x37, 0x42, 0x37, 0x52, 0x37, 0x4C,
		0x37, 0x37, 0x37, 0x29, 0x37, 0x43, 0x37, 0x37,
		0x43, 0x24, 0x52, 0x24, 0x40, 0x30, 0x50, 0x30,
		0x61, 0x24, 0x70, 0x24, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x24, 0x8F, 0x24, 0x80, 0x30, 0x8F, 0x30,
		0x9E, 0x24, 0xAD, 0x24, 0x9F, 0x30, 0xAF, 0x30,
		0xBC, 0x24, 0xC7, 0x29, 0xBF, 0x30, 0xC7, 0x38,
		0xC7, 0x38, 0xC7, 0x42, 0xC7, 0x44, 0xC7, 0x4C,
		0xC7, 0x49, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x37, 0x5B, 0x37, 0x56, 0x37, 0x64, 0x37, 0x61,
		0x37, 0x4F, 0x37, 0x46, 0x37, 0x5B, 0x37, 0x54,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xC7, 0x46, 0xBF, 0x50, 0xC7, 0x54,
		0xC7, 0x50, 0xC7, 0x57, 0xC7, 0x5C, 0xC7, 0x61,
		0xC7, 0x5B, 0x00, 0x00, 0xC7, 0x64, 0x00, 0x00,
		0x37, 0x6D, 0x37, 0x6B, 0x37, 0x76, 0x37, 0x75,
		0x37, 0x67, 0x37, 0x63, 0x37, 0x73, 0x37, 0x71,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xC7, 0x63, 0xBF, 0x70, 0xC7, 0x71,
		0xC7, 0x68, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x89, 0x37, 0x8A,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x8C, 0x37, 0x8E,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xC7, 0x80, 0xBF, 0x8F, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x92, 0x37, 0x94, 0x37, 0x9B, 0x37, 0x9E,
		0x37, 0x98, 0x37, 0x9C, 0x37, 0xA4, 0x37, 0xAB,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xC7, 0x9C, 0xBF, 0xAF, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA4, 0x37, 0xA9, 0x37, 0xAD, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x40, 0xBF, 0x50, 0xBF, 0x40, 0xCF, 0x50, 0xCF,
		0x60, 0xBF, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xC7, 0xB9, 0xBF, 0xCF, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x3F, 0xDA,
		0x43, 0xDA, 0x52, 0xDA, 0x4C, 0xDA, 0x59, 0xDA,
		0x62, 0xDA, 0x71, 0xDA, 0x66, 0xDA, 0x73, 0xDA,
		0x80, 0xDA, 0x8F, 0xDA, 0x80, 0xDA, 0x8C, 0xDA,
		0x9E, 0xDA, 0xAD, 0xDA, 0x99, 0xDA, 0xA6, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB3, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x47, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x52, 0xDA, 0x5E, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x69, 0xDA, 0x74, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xDA, 0x8B, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x96, 0xDA, 0xA2, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xAD, 0xDA, 0xB9, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x3E, 0x47, 0x44, 0x44,
		0x4F, 0x3F, 0x55, 0x3C, 0x4A, 0x41, 0x51, 0x3E,
		0x5C, 0x38, 0x63, 0x35, 0x58, 0x3A, 0x60, 0x36,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8B, 0x22, 0x80, 0x27, 0x8D, 0x21,
		0x99, 0x1B, 0xA3, 0x21, 0x9D, 0x19, 0xA3, 0x2D,
		0xA3, 0x38, 0xA8, 0x40, 0xA3, 0x41, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x38, 0x4A, 0x3E, 0x47, 0x30, 0x4E, 0x36, 0x4B,
		0x44, 0x44, 0x4B, 0x41, 0x3C, 0x48, 0x44, 0x44,
		0x53, 0x3D, 0x5C, 0x38, 0x4D, 0x40, 0x56, 0x3B,
		0x66, 0x33, 0x72, 0x2E, 0x62, 0x36, 0x70, 0x30,
		0x80, 0x27, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xA3, 0x38, 0x9F, 0x30, 0xA3, 0x44,
		0xA3, 0x4A, 0xA8, 0x50, 0xA3, 0x53, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x26, 0x53, 0x2C, 0x50, 0x19, 0x59, 0x1F, 0x56,
		0x33, 0x4C, 0x3B, 0x48, 0x26, 0x53, 0x30, 0x50,
		0x44, 0x44, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xA3, 0x50, 0x9F, 0x50, 0xA3, 0x5C,
		0xA3, 0x5C, 0xA8, 0x60, 0xA3, 0x65, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x13, 0x64, 0x13, 0x61, 0x13, 0x72, 0x13, 0x70,
		0x20, 0x60, 0x30, 0x60, 0x20, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xA3, 0x68, 0x9F, 0x70, 0xA3, 0x74,
		0xA3, 0x6E, 0xA8, 0x70, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x80, 0x13, 0x80, 0x13, 0x8D, 0x13, 0x8F,
		0x20, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xA3, 0x80, 0x9F, 0x8F, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x9B, 0x13, 0x9E, 0x13, 0xA8, 0x17, 0xAC,
		0x20, 0x9F, 0x30, 0x9F, 0x26, 0xAC, 0x35, 0xAC,
		0x40, 0x9F, 0x50, 0x9F, 0x44, 0xAC, 0x53, 0xAC,
		0x60, 0x9F, 0x70, 0x9F, 0x62, 0xAC, 0x71, 0xAC,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8E, 0xAC,
		0x9F, 0x9F, 0xA3, 0x97, 0x9D, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x26, 0xAC, 0x31, 0xAC, 0x38, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x65, 0xAC,
		0x69, 0xAC, 0x74, 0xAC, 0x6E, 0xAC, 0x77, 0xAC,
		0x80, 0xAC, 0x8B, 0xAC, 0x80, 0xAC, 0x88, 0xAC,
		0x96, 0xAC, 0xA1, 0xAC, 0x91, 0xAC, 0x9A, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x6D, 0x6D, 0x74, 0x69, 0x6D, 0x76, 0x70, 0x70,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x70, 0x8F, 0x70,
		0x96, 0x69, 0xA2, 0x69, 0x9F, 0x70, 0xAF, 0x70,
		0xAD, 0x69, 0xB9, 0x69, 0xBF, 0x70, 0xCF, 0x70,
		0xC4, 0x69, 0xCF, 0x69, 0xDF, 0x70, 0xEF, 0x70,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x6D, 0x80, 0x70, 0x80, 0x6D, 0x89, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x70, 0xA0, 0x6D, 0x9B, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8C, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA4, 0x73, 0xB2, 0x6D, 0xAD, 0x76, 0xB1,
		0x80, 0xAC, 0x89, 0xA7, 0x80, 0xAC, 0x88, 0xA8,
		0x92, 0xA4, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6E, 0xB5, 0x77, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x4C, 0x3B, 0x55, 0x3B, 0x49, 0x40, 0x4F, 0x3B,
		0x5D, 0x3B, 0x66, 0x3B, 0x58, 0x3B, 0x62, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x89, 0x3B,
		0x91, 0x3B, 0x99, 0x3B, 0x93, 0x3B, 0x9D, 0x3B,
		0xA2, 0x3B, 0xAA, 0x3B, 0xA7, 0x3B, 0xB0, 0x3B,
		0xB3, 0x3B, 0xBB, 0x3B, 0xBA, 0x3B, 0xC4, 0x3B,
		0xC3, 0x3B, 0x00, 0x00, 0xCD, 0x3B, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x49, 0x49, 0x49, 0x3E, 0x49, 0x52, 0x49, 0x49,
		0x52, 0x3B, 0x5D, 0x3B, 0x49, 0x3C, 0x56, 0x3B,
		0x69, 0x3B, 0x74, 0x3B, 0x64, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8D, 0x3B,
		0x96, 0x3B, 0xA2, 0x3B, 0x9B, 0x3B, 0xA9, 0x3B,
		0xAD, 0x3B, 0xB9, 0x3B, 0xB6, 0x3B, 0xC4, 0x3B,
		0xC4, 0x3B, 0xCF, 0x3B, 0xD2, 0x3B, 0xD9, 0x3F,
		0xD9, 0x3C, 0x00, 0x00, 0xD9, 0x47, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x49, 0x5B, 0x49, 0x54, 0x49, 0x64, 0x49, 0x5F,
		0x49, 0x49, 0x50, 0x40, 0x49, 0x57, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xCF, 0x40, 0xBF, 0x50, 0xCF, 0x50,
		0xD9, 0x44, 0xD9, 0x4C, 0xD9, 0x53, 0xD9, 0x59,
		0xD9, 0x52, 0x00, 0x00, 0xD9, 0x5E, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x49, 0x6D, 0x49, 0x6A, 0x49, 0x76, 0x49, 0x75,
		0x49, 0x64, 0x50, 0x60, 0x49, 0x72, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xD9, 0x62, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x49, 0x80, 0x49, 0x80, 0x49, 0x89, 0x49, 0x8A,
		0x49, 0x80, 0x50, 0x80, 0x49, 0x8D, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x49, 0x92, 0x49, 0x95, 0x49, 0x9B, 0x49, 0xA0,
		0x49, 0x9B, 0x50, 0x9F, 0x49, 0xA8, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x50, 0xBF, 0x49, 0xC3, 0x50, 0xD0,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xCF, 0xBF, 0xBF, 0xCF, 0xCF, 0xCF,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCB,
		0x49, 0xD1, 0x50, 0xE0, 0x49, 0xDF, 0x50, 0xEF,
		0x60, 0xDF, 0x70, 0xDF, 0x60, 0xEF, 0x70, 0xEF,
		0x80, 0xDF, 0x8F, 0xDF, 0x80, 0xEF, 0x8F, 0xEF,
		0x9F, 0xDF, 0xAF, 0xDF, 0x9F, 0xEF, 0xAF, 0xEF,
		0xBF, 0xDF, 0xCF, 0xDF, 0xBC, 0xE9, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD6, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xEB, 0x55, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xF1, 0x71, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xF1, 0x8E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x9C, 0xF1, 0xAA, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xB6, 0xEC, 0xC0, 0xE7, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x26, 0x26, 0x2E, 0x22, 0x25, 0x30, 0x26, 0x26,
		0x36, 0x1E, 0x40, 0x19, 0x2F, 0x21, 0x39, 0x1D,
		0x4A, 0x14, 0x55, 0x0F, 0x44, 0x17, 0x50, 0x11,
		0x63, 0x0D, 0x71, 0x0D, 0x60, 0x10, 0x70, 0x10,
		0x80, 0x0D, 0x8E, 0x0D, 0x80, 0x10, 0x8F, 0x10,
		0x9C, 0x0D, 0xAA, 0x0D, 0x9F, 0x10, 0xAF, 0x10,
		0xB5, 0x14, 0xB5, 0x2A, 0xB5, 0x22, 0xB5, 0x34,
		0xB5, 0x38, 0xB8, 0x40, 0xB5, 0x41, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x25, 0x3C, 0x25, 0x32, 0x25, 0x47, 0x25, 0x3F,
		0x26, 0x26, 0x30, 0x21, 0x25, 0x34, 0x30, 0x30,
		0x40, 0x20, 0x50, 0x20, 0x40, 0x30, 0x50, 0x30,
		0x60, 0x20, 0x70, 0x20, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x20, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xAF, 0x20, 0x9F, 0x30, 0xAF, 0x30,
		0xB5, 0x2F, 0xB5, 0x3F, 0xB5, 0x3C, 0xB5, 0x4A,
		0xB5, 0x4A, 0xB8, 0x50, 0xB5, 0x53, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x25, 0x52, 0x25, 0x4C, 0x25, 0x5E, 0x25, 0x59,
		0x25, 0x43, 0x30, 0x40, 0x25, 0x52, 0x30, 0x50,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xB5, 0x4A, 0xB5, 0x55, 0xB5, 0x57, 0xB5, 0x5F,
		0xB5, 0x5C, 0xB8, 0x60, 0xB5, 0x65, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x25, 0x69, 0x25, 0x66, 0x25, 0x74, 0x25, 0x73,
		0x25, 0x61, 0x30, 0x60, 0x25, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xB5, 0x65, 0xB5, 0x6A, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6E, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x25, 0x8B, 0x25, 0x8C,
		0x25, 0x80, 0x30, 0x80, 0x25, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x96, 0x25, 0x99, 0x25, 0xA1, 0x25, 0xA6,
		0x25, 0x9E, 0x30, 0x9F, 0x25, 0xAD, 0x30, 0xAF,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAD, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x30, 0xBF, 0x2E, 0xC3, 0x3C, 0xC3,
		0x40, 0xBF, 0x50, 0xBF, 0x49, 0xC3, 0x57, 0xC3,
		0x60, 0xBF, 0x70, 0xBF, 0x65, 0xC3, 0x72, 0xC3,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xC3, 0x8D, 0xC3,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9B, 0xC3, 0xA8, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x4F, 0xC3,
		0x52, 0xC3, 0x5E, 0xC3, 0x59, 0xC3, 0x63, 0xC3,
		0x69, 0xC3, 0x74, 0xC3, 0x6C, 0xC3, 0x76, 0xC3,
		0x80, 0xC3, 0x8B, 0xC3, 0x80, 0xC3, 0x89, 0xC3,
		0x96, 0xC3, 0xA1, 0xC3, 0x93, 0xC3, 0x9D, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA6, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x55, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x5D, 0xC3, 0x66, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x6F, 0xC3, 0x77, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xC3, 0x88, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xC3, 0x99, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xC3, 0xAA, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x89, 0x4F,
		0x91, 0x4C, 0x98, 0x50, 0x91, 0x54, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x57, 0x67,
		0x60, 0x60, 0x68, 0x60, 0x5C, 0x65, 0x62, 0x62,
		0x6E, 0x5D, 0x76, 0x59, 0x6A, 0x5F, 0x73, 0x5A,
		0x80, 0x54, 0x8C, 0x4E, 0x80, 0x54, 0x8F, 0x50,
		0x91, 0x5D, 0x98, 0x60, 0x91, 0x65, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x47, 0x6F, 0x38, 0x77, 0x3A, 0x76,
		0x4A, 0x6E, 0x4E, 0x6C, 0x3D, 0x74, 0x40, 0x73,
		0x53, 0x69, 0x5A, 0x66, 0x44, 0x71, 0x50, 0x70,
		0x62, 0x62, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x91, 0x6E, 0x98, 0x70, 0x91, 0x77, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x06, 0x8F, 0x10, 0x8F,
		0x25, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x27, 0x96, 0x32, 0x96, 0x40, 0x98, 0x48, 0x98,
		0x3D, 0x96, 0x48, 0x96, 0x50, 0x98, 0x58, 0x98,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x74, 0x96, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x96, 0x8B, 0x96, 0x80, 0x98, 0x88, 0x98,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_pltm_table = {
		/* pltm - H */
		0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xFE, 0xFE, 0xFE,
		0xFD, 0xFD, 0xFD, 0xFC, 0xFC, 0xFB, 0xFB, 0xFA,
		0xF9, 0xF9, 0xF8, 0xF7, 0xF6, 0xF5, 0xF5, 0xF4,
		0xF3, 0xF2, 0xF1, 0xF0, 0xEE, 0xED, 0xEC, 0xEB,
		0xEA, 0xE8, 0xE7, 0xE6, 0xE4, 0xE3, 0xE1, 0xE0,
		0xDF, 0xDD, 0xDB, 0xDA, 0xD8, 0xD7, 0xD5, 0xD3,
		0xD2, 0xD0, 0xCE, 0xCC, 0xCB, 0xC9, 0xC7, 0xC5,
		0xC3, 0xC2, 0xC0, 0xBE, 0xBC, 0xBA, 0xB8, 0xB6,
		0xB4, 0xB2, 0xB0, 0xAE, 0xAC, 0xAA, 0xA8, 0xA6,
		0xA4, 0xA2, 0xA0, 0x9E, 0x9C, 0x9A, 0x98, 0x96,
		0x94, 0x92, 0x90, 0x8E, 0x8C, 0x8A, 0x88, 0x86,
		0x84, 0x82, 0x80, 0x7E, 0x7C, 0x7A, 0x79, 0x77,
		0x75, 0x73, 0x71, 0x6F, 0x6D, 0x6B, 0x69, 0x68,
		0x66, 0x64, 0x62, 0x61, 0x5F, 0x5D, 0x5B, 0x5A,
		0x58, 0x56, 0x55, 0x53, 0x51, 0x50, 0x4E, 0x4D,
		0x4B, 0x4A, 0x48, 0x47, 0x45, 0x44, 0x42, 0x41,
		0x3F, 0x3E, 0x3D, 0x3B, 0x3A, 0x39, 0x37, 0x36,
		0x35, 0x34, 0x33, 0x31, 0x30, 0x2F, 0x2E, 0x2D,
		0x2C, 0x2B, 0x2A, 0x29, 0x28, 0x27, 0x26, 0x25,
		0x24, 0x23, 0x22, 0x21, 0x20, 0x1F, 0x1E, 0x1E,
		0x1D, 0x1C, 0x1B, 0x1B, 0x1A, 0x19, 0x18, 0x18,
		0x17, 0x16, 0x16, 0x15, 0x14, 0x14, 0x13, 0x13,
		0x12, 0x12, 0x11, 0x11, 0x10, 0x10, 0x0F, 0x0F,
		0x0E, 0x0E, 0x0D, 0x0D, 0x0C, 0x0C, 0x0C, 0x0B,
		0x0B, 0x0A, 0x0A, 0x0A, 0x09, 0x09, 0x09, 0x08,
		0x08, 0x08, 0x08, 0x07, 0x07, 0x07, 0x07, 0x06,
		0x06, 0x06, 0x06, 0x05, 0x05, 0x05, 0x05, 0x05,
		0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03,
		0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x02, 0x02,
		0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
		0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
		0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
		/* pltm - V */
		0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xFE, 0xFE, 0xFE,
		0xFD, 0xFD, 0xFD, 0xFC, 0xFC, 0xFB, 0xFB, 0xFA,
		0xF9, 0xF9, 0xF8, 0xF7, 0xF6, 0xF5, 0xF5, 0xF4,
		0xF3, 0xF2, 0xF1, 0xF0, 0xEE, 0xED, 0xEC, 0xEB,
		0xEA, 0xE8, 0xE7, 0xE6, 0xE4, 0xE3, 0xE1, 0xE0,
		0xDF, 0xDD, 0xDB, 0xDA, 0xD8, 0xD7, 0xD5, 0xD3,
		0xD2, 0xD0, 0xCE, 0xCC, 0xCB, 0xC9, 0xC7, 0xC5,
		0xC3, 0xC2, 0xC0, 0xBE, 0xBC, 0xBA, 0xB8, 0xB6,
		0xB4, 0xB2, 0xB0, 0xAE, 0xAC, 0xAA, 0xA8, 0xA6,
		0xA4, 0xA2, 0xA0, 0x9E, 0x9C, 0x9A, 0x98, 0x96,
		0x94, 0x92, 0x90, 0x8E, 0x8C, 0x8A, 0x88, 0x86,
		0x84, 0x82, 0x80, 0x7E, 0x7C, 0x7A, 0x79, 0x77,
		0x75, 0x73, 0x71, 0x6F, 0x6D, 0x6B, 0x69, 0x68,
		0x66, 0x64, 0x62, 0x61, 0x5F, 0x5D, 0x5B, 0x5A,
		0x58, 0x56, 0x55, 0x53, 0x51, 0x50, 0x4E, 0x4D,
		0x4B, 0x4A, 0x48, 0x47, 0x45, 0x44, 0x42, 0x41,
		0x3F, 0x3E, 0x3D, 0x3B, 0x3A, 0x39, 0x37, 0x36,
		0x35, 0x34, 0x33, 0x31, 0x30, 0x2F, 0x2E, 0x2D,
		0x2C, 0x2B, 0x2A, 0x29, 0x28, 0x27, 0x26, 0x25,
		0x24, 0x23, 0x22, 0x21, 0x20, 0x1F, 0x1E, 0x1E,
		0x1D, 0x1C, 0x1B, 0x1B, 0x1A, 0x19, 0x18, 0x18,
		0x17, 0x16, 0x16, 0x15, 0x14, 0x14, 0x13, 0x13,
		0x12, 0x12, 0x11, 0x11, 0x10, 0x10, 0x0F, 0x0F,
		0x0E, 0x0E, 0x0D, 0x0D, 0x0C, 0x0C, 0x0C, 0x0B,
		0x0B, 0x0A, 0x0A, 0x0A, 0x09, 0x09, 0x09, 0x08,
		0x08, 0x08, 0x08, 0x07, 0x07, 0x07, 0x07, 0x06,
		0x06, 0x06, 0x06, 0x05, 0x05, 0x05, 0x05, 0x05,
		0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03,
		0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x02, 0x02,
		0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
		0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
		0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
		/* pltm - P */
		0x00, 0x00, 0x09, 0x00, 0x1B, 0x00, 0x35, 0x00,
		0x54, 0x00, 0x78, 0x00, 0xA1, 0x00, 0xCE, 0x00,
		0xFF, 0x00, 0x35, 0x01, 0x6D, 0x01, 0xAA, 0x01,
		0xE9, 0x01, 0x2C, 0x02, 0x72, 0x02, 0xBB, 0x02,
		0x08, 0x03, 0x57, 0x03, 0xA8, 0x03, 0xFD, 0x03,
		0x55, 0x04, 0xAF, 0x04, 0x0B, 0x05, 0x6A, 0x05,
		0xCC, 0x05, 0x30, 0x06, 0x97, 0x06, 0x00, 0x07,
		0x6B, 0x07, 0xD9, 0x07, 0x49, 0x08, 0xBB, 0x08,
		0x30, 0x09, 0xA7, 0x09, 0x20, 0x0A, 0x9B, 0x0A,
		0x18, 0x0B, 0x97, 0x0B, 0x19, 0x0C, 0x9C, 0x0C,
		0x21, 0x0D, 0xA9, 0x0D, 0x32, 0x0E, 0xBE, 0x0E,
		0x4B, 0x0F, 0xDB, 0x0F, 0x6C, 0x10, 0xFF, 0x10,
		0x94, 0x11, 0x2B, 0x12, 0xC4, 0x12, 0x5F, 0x13,
		0xFB, 0x13, 0x99, 0x14, 0x39, 0x15, 0xDB, 0x15,
		0x7F, 0x16, 0x25, 0x17, 0xCC, 0x17, 0x75, 0x18,
		0x1F, 0x19, 0xCC, 0x19, 0x7A, 0x1A, 0x29, 0x1B,
		0xDB, 0x1B, 0x8E, 0x1C, 0x43, 0x1D, 0xF9, 0x1D,
		0xB1, 0x1E, 0x6B, 0x1F, 0x26, 0x20, 0xE3, 0x20,
		0xA2, 0x21, 0x62, 0x22, 0x24, 0x23, 0xE7, 0x23,
		0xAC, 0x24, 0x72, 0x25, 0x3A, 0x26, 0x04, 0x27,
		0xCF, 0x27, 0x9B, 0x28, 0x6A, 0x29, 0x39, 0x2A,
		0x0A, 0x2B, 0xDD, 0x2B, 0xB1, 0x2C, 0x87, 0x2D,
		0x5E, 0x2E, 0x36, 0x2F, 0x10, 0x30, 0xEC, 0x30,
		0xC9, 0x31, 0xA7, 0x32, 0x87, 0x33, 0x68, 0x34,
		0x4B, 0x35, 0x2F, 0x36, 0x15, 0x37, 0xFB, 0x37,
		0xE4, 0x38, 0xCE, 0x39, 0xB9, 0x3A, 0xA5, 0x3B,
		0x93, 0x3C, 0x82, 0x3D, 0x73, 0x3E, 0x65, 0x3F,
		0x58, 0x40, 0x4D, 0x41, 0x43, 0x42, 0x3B, 0x43,
		0x33, 0x44, 0x2D, 0x45, 0x29, 0x46, 0x26, 0x47,
		0x24, 0x48, 0x23, 0x49, 0x24, 0x4A, 0x26, 0x4B,
		0x29, 0x4C, 0x2E, 0x4D, 0x34, 0x4E, 0x3B, 0x4F,
		0x43, 0x50, 0x4D, 0x51, 0x58, 0x52, 0x64, 0x53,
		0x72, 0x54, 0x81, 0x55, 0x91, 0x56, 0xA2, 0x57,
		0xB5, 0x58, 0xC9, 0x59, 0xDE, 0x5A, 0xF4, 0x5B,
		0x0C, 0x5D, 0x25, 0x5E, 0x3F, 0x5F, 0x5A, 0x60,
		0x77, 0x61, 0x95, 0x62, 0xB4, 0x63, 0xD4, 0x64,
		0xF5, 0x65, 0x18, 0x67, 0x3C, 0x68, 0x61, 0x69,
		0x87, 0x6A, 0xAE, 0x6B, 0xD7, 0x6C, 0x01, 0x6E,
		0x2C, 0x6F, 0x58, 0x70, 0x85, 0x71, 0xB4, 0x72,
		0xE4, 0x73, 0x14, 0x75, 0x46, 0x76, 0x7A, 0x77,
		0xAE, 0x78, 0xE4, 0x79, 0x1A, 0x7B, 0x52, 0x7C,
		0x8B, 0x7D, 0xC5, 0x7E, 0x00, 0x80, 0x3D, 0x81,
		0x7A, 0x82, 0xB9, 0x83, 0xF9, 0x84, 0x3A, 0x86,
		0x7C, 0x87, 0xBF, 0x88, 0x03, 0x8A, 0x49, 0x8B,
		0x90, 0x8C, 0xD7, 0x8D, 0x20, 0x8F, 0x6A, 0x90,
		0xB5, 0x91, 0x01, 0x93, 0x4E, 0x94, 0x9D, 0x95,
		0xEC, 0x96, 0x3D, 0x98, 0x8E, 0x99, 0xE1, 0x9A,
		0x35, 0x9C, 0x8A, 0x9D, 0xDF, 0x9E, 0x37, 0xA0,
		0x8F, 0xA1, 0xE8, 0xA2, 0x42, 0xA4, 0x9D, 0xA5,
		0xFA, 0xA6, 0x57, 0xA8, 0xB6, 0xA9, 0x16, 0xAB,
		0x76, 0xAC, 0xD8, 0xAD, 0x3B, 0xAF, 0x9F, 0xB0,
		0x03, 0xB2, 0x69, 0xB3, 0xD0, 0xB4, 0x38, 0xB6,
		0xA2, 0xB7, 0x0C, 0xB9, 0x77, 0xBA, 0xE3, 0xBB,
		0x50, 0xBD, 0xBF, 0xBE, 0x2E, 0xC0, 0x9E, 0xC1,
		0x10, 0xC3, 0x82, 0xC4, 0xF6, 0xC5, 0x6A, 0xC7,
		0xDF, 0xC8, 0x56, 0xCA, 0xCD, 0xCB, 0x46, 0xCD,
		0xC0, 0xCE, 0x3A, 0xD0, 0xB6, 0xD1, 0x32, 0xD3,
		0xB0, 0xD4, 0x2E, 0xD6, 0xAE, 0xD7, 0x2F, 0xD9,
		0xB0, 0xDA, 0x33, 0xDC, 0xB6, 0xDD, 0x3B, 0xDF,
		0xC1, 0xE0, 0x47, 0xE2, 0xCF, 0xE3, 0x57, 0xE5,
		0xE1, 0xE6, 0x6B, 0xE8, 0xF7, 0xE9, 0x83, 0xEB,
		0x11, 0xED, 0x9F, 0xEE, 0x2F, 0xF0, 0xBF, 0xF1,
		0x51, 0xF3, 0xE3, 0xF4, 0x76, 0xF6, 0x0B, 0xF8,
		0xA0, 0xF9, 0x36, 0xFB, 0xCD, 0xFC, 0x65, 0xFE,
		/* pltm - F */
		0xFF, 0xFF, 0x41, 0x07, 0x3A, 0x07, 0x33, 0x07,
		0x2C, 0x07, 0x25, 0x07, 0x1E, 0x07, 0x17, 0x07,
		0x10, 0x07, 0x0A, 0x07, 0x03, 0x07, 0xFC, 0x06,
		0xF6, 0x06, 0xEF, 0x06, 0xE9, 0x06, 0xE3, 0x06,
		0xDD, 0x06, 0xD6, 0x06, 0xD0, 0x06, 0xCA, 0x06,
		0xC4, 0x06, 0xBE, 0x06, 0xB8, 0x06, 0xB2, 0x06,
		0xAD, 0x06, 0xA7, 0x06, 0xA1, 0x06, 0x9B, 0x06,
		0x96, 0x06, 0x90, 0x06, 0x8B, 0x06, 0x85, 0x06,
		0x80, 0x06, 0x7B, 0x06, 0x75, 0x06, 0x70, 0x06,
		0x6B, 0x06, 0x66, 0x06, 0x60, 0x06, 0x5B, 0x06,
		0x56, 0x06, 0x51, 0x06, 0x4C, 0x06, 0x47, 0x06,
		0x42, 0x06, 0x3D, 0x06, 0x39, 0x06, 0x34, 0x06,
		0x2F, 0x06, 0x2A, 0x06, 0x26, 0x06, 0x21, 0x06,
		0x1C, 0x06, 0x18, 0x06, 0x13, 0x06, 0x0F, 0x06,
		0x0A, 0x06, 0x06, 0x06, 0x01, 0x06, 0xFD, 0x05,
		0xF9, 0x05, 0xF4, 0x05, 0xF0, 0x05, 0xEC, 0x05,
		0xE8, 0x05, 0xE4, 0x05, 0xDF, 0x05, 0xDB, 0x05,
		0xD7, 0x05, 0xD3, 0x05, 0xCF, 0x05, 0xCB, 0x05,
		0xC7, 0x05, 0xC3, 0x05, 0xBF, 0x05, 0xBB, 0x05,
		0xB7, 0x05, 0xB4, 0x05, 0xB0, 0x05, 0xAC, 0x05,
		0xA8, 0x05, 0xA4, 0x05, 0xA1, 0x05, 0x9D, 0x05,
		0x99, 0x05, 0x96, 0x05, 0x92, 0x05, 0x8E, 0x05,
		0x8B, 0x05, 0x87, 0x05, 0x84, 0x05, 0x80, 0x05,
		0x7D, 0x05, 0x79, 0x05, 0x76, 0x05, 0x72, 0x05,
		0x6F, 0x05, 0x6C, 0x05, 0x68, 0x05, 0x65, 0x05,
		0x62, 0x05, 0x5E, 0x05, 0x5B, 0x05, 0x58, 0x05,
		0x54, 0x05, 0x51, 0x05, 0x4E, 0x05, 0x4B, 0x05,
		0x48, 0x05, 0x45, 0x05, 0x41, 0x05, 0x3E, 0x05,
		0x3B, 0x05, 0x38, 0x05, 0x35, 0x05, 0x32, 0x05,
		0x2F, 0x05, 0x2C, 0x05, 0x29, 0x05, 0x26, 0x05,
		0x23, 0x05, 0x20, 0x05, 0x1D, 0x05, 0x1A, 0x05,
		0x18, 0x05, 0x15, 0x05, 0x12, 0x05, 0x0F, 0x05,
		0x0C, 0x05, 0x09, 0x05, 0x07, 0x05, 0x04, 0x05,
		0x01, 0x05, 0xFE, 0x04, 0xFC, 0x04, 0xF9, 0x04,
		0xF6, 0x04, 0xF3, 0x04, 0xF1, 0x04, 0xEE, 0x04,
		0xEB, 0x04, 0xE9, 0x04, 0xE6, 0x04, 0xE4, 0x04,
		0xE1, 0x04, 0xDE, 0x04, 0xDC, 0x04, 0xD9, 0x04,
		0xD7, 0x04, 0xD4, 0x04, 0xD2, 0x04, 0xCF, 0x04,
		0xCD, 0x04, 0xCA, 0x04, 0xC8, 0x04, 0xC5, 0x04,
		0xC3, 0x04, 0xC1, 0x04, 0xBE, 0x04, 0xBC, 0x04,
		0xB9, 0x04, 0xB7, 0x04, 0xB5, 0x04, 0xB2, 0x04,
		0xB0, 0x04, 0xAE, 0x04, 0xAB, 0x04, 0xA9, 0x04,
		0xA7, 0x04, 0xA4, 0x04, 0xA2, 0x04, 0xA0, 0x04,
		0x9E, 0x04, 0x9B, 0x04, 0x99, 0x04, 0x97, 0x04,
		0x95, 0x04, 0x93, 0x04, 0x90, 0x04, 0x8E, 0x04,
		0x8C, 0x04, 0x8A, 0x04, 0x88, 0x04, 0x86, 0x04,
		0x83, 0x04, 0x81, 0x04, 0x7F, 0x04, 0x7D, 0x04,
		0x7B, 0x04, 0x79, 0x04, 0x77, 0x04, 0x75, 0x04,
		0x73, 0x04, 0x71, 0x04, 0x6F, 0x04, 0x6D, 0x04,
		0x6B, 0x04, 0x69, 0x04, 0x67, 0x04, 0x65, 0x04,
		0x63, 0x04, 0x61, 0x04, 0x5F, 0x04, 0x5D, 0x04,
		0x5B, 0x04, 0x59, 0x04, 0x57, 0x04, 0x55, 0x04,
		0x53, 0x04, 0x51, 0x04, 0x4F, 0x04, 0x4D, 0x04,
		0x4C, 0x04, 0x4A, 0x04, 0x48, 0x04, 0x46, 0x04,
		0x44, 0x04, 0x42, 0x04, 0x40, 0x04, 0x3F, 0x04,
		0x3D, 0x04, 0x3B, 0x04, 0x39, 0x04, 0x37, 0x04,
		0x36, 0x04, 0x34, 0x04, 0x32, 0x04, 0x30, 0x04,
		0x2F, 0x04, 0x2D, 0x04, 0x2B, 0x04, 0x29, 0x04,
		0x28, 0x04, 0x26, 0x04, 0x24, 0x04, 0x22, 0x04,
		0x21, 0x04, 0x1F, 0x04, 0x1D, 0x04, 0x1C, 0x04,
		0x1A, 0x04, 0x18, 0x04, 0x17, 0x04, 0x15, 0x04,
		0x13, 0x04, 0x12, 0x04, 0x10, 0x04, 0x0E, 0x04,
		0x0D, 0x04, 0x0B, 0x04, 0x0A, 0x04, 0x08, 0x04,
		0x06, 0x04, 0x05, 0x04, 0x03, 0x04, 0x02, 0x04
	},
	.isp_wdr_table = { 0 }
};
struct isp_cfg_pt imx258_isp_cfg = {
	.isp_test_settings = &imx258_isp_test_settings,
	.isp_3a_settings = &imx258_isp_3a_settings,
	.isp_tunning_settings = &imx258_isp_tuning_settings,
	.isp_iso_settings = &imx258_isp_iso_settings
};


#endif /* end of _IMX258_H_V100_ */