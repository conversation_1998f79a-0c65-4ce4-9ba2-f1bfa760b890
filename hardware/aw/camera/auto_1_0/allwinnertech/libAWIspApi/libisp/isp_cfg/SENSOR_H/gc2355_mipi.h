/*
 *****************************************************************************
 * gc2355_mipi
 * 1600x1200@30fps, wdr: 0
 * Hawkview ISP - gc2355_mipi config module
 * Copyright (c) 2018 by Allwinnertech Co., Ltd. http://www.allwinnertech.com
 *  Version  |     Author      |     Date     |      Description
 *    2.0    |  Hawkview Tool  |  2018/05/21  |  Automatic generation.
 *
 *****************************************************************************
 */

#ifndef _GC2355_MIPI_H_V100_
#define _GC2355_MIPI_H_V100_

#include "../../include/isp_ini_parse.h"

struct isp_test_param gc2355_mipi_isp_test_settings = {
	.isp_test_mode = 0,
	.isp_test_exptime = 0,
	.exp_line_start = 1000,
	.exp_line_step = 1000,
	.exp_line_end = 32000,
	.exp_change_interval = 5,
	.isp_test_gain = 0,
	.gain_start = 256,
	.gain_step = 64,
	.gain_end = 4096,
	.gain_change_interval = 5,
	.isp_test_focus = 0,
	.focus_start = 10,
	.focus_step = 10,
	.focus_end = 800,
	.focus_change_interval = 5,
	.isp_log_param = 0,
	.isp_gain = 1024,
	.isp_exp_line = 10000,
	.isp_color_temp = 2700,
	.ae_forced = 0,
	.lum_forced = 80,
	.manual_en = 1,
	.afs_en = 1,
	.sharp_en = 1,
	.contrast_en = 1,
	.denoise_en = 1,
	.drc_en = 0,
	.cem_en = 0,
	.lsc_en = 1,
	.gamma_en = 1,
	.cm_en = 1,
	.ae_en = 1,
	.af_en = 0,
	.awb_en = 1,
	.hist_en = 1,
	.blc_en = 0,
	.so_en = 0,
	.wb_en = 1,
	.otf_dpc_en = 1,
	.cfa_en = 1,
	.tdf_en = 0,
	.cnr_en = 1,
	.satur_en = 1,
	.defog_en = 0,
	.linear_en = 0,
	.gtm_en = 0,
	.dig_gain_en = 1,
	.pltm_en = 0,
	.wdr_en = 0,
	.ctc_en = 0
};
struct isp_3a_param gc2355_mipi_isp_3a_settings = {
	.define_ae_table = 1,
	.ae_max_lv = 1650,
	.ae_table_preview_length = 2,
	.ae_table_preview = {
		 8000,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_capture_length = 2,
	.ae_table_capture = {
		    0,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_table_video_length = 2,
	.ae_table_video = {
		    0,    30,   256,   256,   266,   266,
		   30,    30,   256,  2048,   266,   266
	},
	.ae_win_weight = {
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     4,     4,     4,     4,     4,
		    4,     4,     4,     8,     8,     4,     4,     4,
		    4,     4,     6,     8,     8,     6,     4,     4,
		    4,     6,     8,     8,     8,     8,     6,     4,
		    4,     8,     8,     8,     8,     8,     8,     4,
		    4,     4,     4,     4,     4,     4,     4,     4
	},
	.ae_hist_mod_en = 1,
	.ae_hist_sel = 0,
	.ae_stat_sel = 2,
	.ae_ki = 50,
	.ae_ConvDataIndex = 3,
	.ae_blowout_pre_en = 0,
	.ae_blowout_attr = 30,
	.ae_delay_frame = 0,
	.exp_delay_frame = 2,
	.gain_delay_frame = 2,
	.exp_comp_step = 4,
	.ae_touch_dist_ind = 0,
	.ae_iso2gain_ratio = 16,
	.ae_fno_step = {
		  141,   145,   152,   163,   175,   190,   209,   233,
		  266,   311,   379,   487,   657,   971,  1825,  3794
	},
	.wdr_cfg = {
		   16,   128,  1280
	},
	.awb_interval = 2,
	.awb_speed = 16,
	.awb_stat_sel = 0,
	.awb_color_temper_low = 1800,
	.awb_color_temper_high = 8000,
	.awb_base_temper = 6500,
	.awb_green_zone_dist = 63,
	.awb_blue_sky_dist = 63,
	.awb_light_num = 8,
	.awb_light_info = {
		  326,   256,    93,   256,   256,   256,    64,  1900,    32,    90,
		  301,   256,   102,   256,   256,   256,    64,  2500,    32,    90,
		  265,   256,   112,   256,   256,   256,    64,  2800,    32,    90,
		  222,   256,   142,   256,   256,   256,    64,  4000,    96,   100,
		  195,   256,   136,   256,   256,   256,    64,  4100,    64,   100,
		  205,   256,   180,   256,   256,   256,    64,  5000,   100,   100,
		  193,   256,   233,   256,   256,   256,    64,  6500,    64,   100,
		  188,   256,   265,   256,   256,   256,    64,  7500,    64,   100
	},
	.awb_ext_light_num = 0,
	.awb_ext_light_info = {
		0
	},
	.awb_skin_color_num = 0,
	.awb_skin_color_info = {
		0
	},
	.awb_special_color_num = 0,
	.awb_special_color_info = {
		0
	},
	.awb_preset_gain = {
		  256,   256,   256,   256,   151,   405,   210,   340,   210,   340,
		  145,   480,   265,   256,   256,   256,   285,   245,   280,   235,
		  140,   480
	},
	.awb_rgain_favor = 256,
	.awb_bgain_favor = 256,
	.af_use_otp = 0,
	.vcm_min_code = 380,
	.vcm_max_code = 800,
	.af_interval_time = 136,
	.af_speed_ind = 20,
	.af_auto_fine_en = 0,
	.af_single_fine_en = 0,
	.af_fine_step = 10,
	.af_move_cnt = 4,
	.af_still_cnt = 2,
	.af_move_monitor_cnt = 6,
	.af_still_monitor_cnt = 3,
	.af_stable_min = 245,
	.af_stable_max = 265,
	.af_low_light_lv = 10,
	.af_near_tolerance = 15,
	.af_far_tolerance = 25,
	.af_tolerance_off = 0,
	.af_peak_th = 100,
	.af_dir_th = 10,
	.af_change_ratio = 30,
	.af_move_minus = 2,
	.af_still_minus = 1,
	.af_scene_motion_th = 0,
	.af_tolerance_tbl_len = 0,
	.af_std_code_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	},
	.af_tolerance_value_tbl = {
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
		    0,     0,     0,     0,     0,     0,     0,     0,     0,     0
	}
};
struct isp_dynamic_param gc2355_mipi_isp_iso_settings = {
	.triger = {
		.sharp_triger = 1,
		.contrast_triger = 1,
		.denoise_triger = 1,
		.sensor_offset_triger = 0,
		.black_level_triger = 0,
		.dpc_triger = 1,
		.defog_value_triger = 0,
		.pltm_dynamic_triger = 0,
		.brightness_triger = 0,
		.gcontrast_triger = 0,
		.saturation_triger = 1,
		.cem_ratio_triger = 0,
		.tdf_triger = 0,
		.color_denoise_triger = 0,
		.ae_cfg_triger = 0,
		.gtm_cfg_triger = 0
	},
	.isp_lum_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_gain_mapping_point = {
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0
	},
	.isp_dynamic_cfg[0] = {
		.sharp_cfg = {
			8, 20, 188, 188, 188, 188, 256, 0, 256, 0
		},
		.contrast_cfg = {
			1, 16, 36, 12, 105, 512, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			111, 0, 444, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			16, 16, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 256, 0, 256, 0, 256, 0, 256, 0
		},
		.color_denoise = 4,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[1] = {
		.sharp_cfg = {
			8, 24, 177, 177, 177, 177, 256, 0, 256, 0
		},
		.contrast_cfg = {
			2, 16, 36, 12, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			222, 0, 666, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			12, 12, 2047, 0
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 576, 0, 576, 0, 384, 0, 384, 0
		},
		.color_denoise = 8,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[2] = {
		.sharp_cfg = {
			8, 28, 166, 166, 166, 166, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			333, 0, 888, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			8, 8, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 768, 0, 768, 0, 512, 0, 512, 0
		},
		.color_denoise = 12,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[3] = {
		.sharp_cfg = {
			8, 32, 155, 155, 155, 155, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 32, 32, 105, 256, 0, 256, 0, 256, 0
		},
		.denoise_cfg = {
			444, 0, 1111, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			4, 4, 2047, 1
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 960, 0, 960, 0, 768, 0, 768, 0
		},
		.color_denoise = 16,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[4] = {
		.sharp_cfg = {
			8, 36, 144, 144, 144, 144, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 36, 12, 105, 256, 64, 256, 0, 256, 0
		},
		.denoise_cfg = {
			555, 0, 1333, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			2, 3, 2047, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1152, 0, 1152, 0, 1152, 0, 1152, 0
		},
		.color_denoise = 20,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[5] = {
		.sharp_cfg = {
			10, 40, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 34, 12, 105, 256, 80, 256, 0, 256, 0
		},
		.denoise_cfg = {
			666, 0, 1555, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1600, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1344, 0, 1344, 0, 1536, 0, 1536, 0
		},
		.color_denoise = 66,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[6] = {
		.sharp_cfg = {
			12, 50, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 30, 12, 105, 256, 96, 256, 0, 256, 0
		},
		.denoise_cfg = {
			512, 0, 512, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1536, 0, 1536, 0, 1920, 0, 1920, 0
		},
		.color_denoise = 88,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[7] = {
		.sharp_cfg = {
			14, 60, 133, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 26, 12, 105, 256, 104, 256, 0, 256, 0
		},
		.denoise_cfg = {
			768, 0, 768, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1728, 0, 1728, 0, 2340, 0, 2340, 0
		},
		.color_denoise = 100,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[8] = {
		.sharp_cfg = {
			16, 70, 33, 133, 133, 133, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 22, 12, 105, 256, 112, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1000, 0, 1280, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 1920, 0, 1920, 0, 2560, 0, 2560, 0
		},
		.color_denoise = 122,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[9] = {
		.sharp_cfg = {
			18, 80, 33, 133, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 18, 10, 105, 200, 128, 256, 0, 256, 0
		},
		.denoise_cfg = {
			1200, 0, 1200, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2112, 0, 2112, 0, 2816, 0, 2816, 0
		},
		.color_denoise = 144,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[10] = {
		.sharp_cfg = {
			20, 90, 122, 122, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 14, 8, 105, 199, 160, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2200, 0, 2000, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2304, 0, 2304, 0, 3072, 0, 3072, 0
		},
		.color_denoise = 166,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[11] = {
		.sharp_cfg = {
			22, 100, 88, 88, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 10, 7, 88, 188, 256, 256, 0, 256, 0
		},
		.denoise_cfg = {
			2800, 0, 2240, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2496, 0, 2496, 0, 3238, 0, 3238, 0
		},
		.color_denoise = 188,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[12] = {
		.sharp_cfg = {
			24, 110, 66, 66, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 8, 6, 77, 177, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			3600, 0, 2360, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 2688, 0, 2688, 0, 3584, 0, 3584, 0
		},
		.color_denoise = 200,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	},
	.isp_dynamic_cfg[13] = {
		.sharp_cfg = {
			26, 120, 44, 44, 133, 88, 256, 0, 256, 0
		},
		.contrast_cfg = {
			4, 16, 4, 3, 66, 166, 320, 256, 0, 256, 0
		},
		.denoise_cfg = {
			4400, 0, 2560, 0
		},
		.sensor_offset = {
			-237, -242, -242, -246
		},
		.black_level = {
			-237, -242, -242, -246
		},
		.dpc_cfg = {
			1, 3, 1024, 2
		},
		.pltm_dynamic_cfg = {
			0, 0, 0
		},
		.defog_value = 30,
		.brightness = 0,
		.contrast = 0,
		.saturation_cb = 0,
		.saturation_cr = 0,
		.saturation_cfg = {
			4, 8, 4, 0, 0, 0, 64
		},
		.cem_ratio = 0,
		.tdf_cfg = {
			16, 16, 2, 0, 3072, 0, 3072, 0, 4096, 0, 4096, 0
		},
		.color_denoise = 222,
		.ae_cfg = {
			512, 192, 256, 256, 12, 12, 12, 12, 3, 60, 4, 30, 2, 25
		},
		.gtm_cfg = {
			1024, 50, 10, 13, 215, 1, 7, 1, 0
		},
	}
};
struct isp_tunning_param gc2355_mipi_isp_tuning_settings = {
	.flash_gain = 80,
	.flash_delay_frame = 16,
	.flicker_type = 0,
	.flicker_ratio = 15,
	.hor_visual_angle = 60,
	.ver_visual_angle = 40,
	.focus_length = 300,
	.gamma_num = 5,
	.rolloff_ratio = 8,
	.gtm_type = 1,
	.gamma_type = 1,
	.auto_alpha_en = 1,
	.cfa_dir_th = 2047,
	.ctc_th_max = 316,
	.ctc_th_min = 60,
	.ctc_th_slope = 262,
	.ctc_dir_wt = 64,
	.ctc_dir_th = 80,
	.bayer_gain = {
		 1024,  1024,  1024,  1024
	},
	.ff_mod = 2,
	.lsc_center_x = 2048,
	.lsc_center_y = 2048,
	.lsc_trig_cfg = {
		 2200,  2800,  4000,  5000,  5500,  6500
	},
	.gamma_trig_cfg = {
		 1300,  1100,   900,   600,   300
	},
	.color_matrix_ini[0] = {
		.matrix = { { 328, 0, -72 }, { -153, 508, -99 },
				{ 0, -173, 429 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[1] = {
		.matrix = { { 353, 0, -97 }, { -128, 475, -91 },
				{ -11, -86, 353 } },
		.offset = { 0, 0, 0 }
	},
	.color_matrix_ini[2] = {
		.matrix = { { 411, -17, -138 }, { -88, 485, -141 },
				{ -20, -24, 300 } },
		.offset = { 0, 0, 0 }
	},
	.cm_trig_cfg = {
		 2700,  4000,  6500
	},
	.pltm_cfg = {
		    1,     0,    10,     7,  2048,  2048,     0,    15,
		   15,   210,    32,   255,    23,    31,    34
	},
	.isp_bdnf_th = {
		    7,    18,    25,    28,    29,    32,    34,    36,
		   38,    41,    42,    45,    46,    49,    50,    53,
		   55,    57,    59,    62,    63,    66,    67,    70,
		   71,    74,    76,    78,    80,    83,    84,    87,
		   88
	},
	.isp_tdnf_th = {
		    4,     4,     5,     6,     7,     8,     9,    10,
		   11,    12,    13,    14,    15,    16,    17,    18,
		   19,    20,    21,    22,    23,    24,    25,    26,
		   27,    28,    29,    30,    31,    32,    32,    32,
		   32
	},
	.isp_tdnf_ref_noise = {
		    6,     6,     7,     8,     8,     9,    10,    11,
		   12,    13,    14,    15,    16,    17,    18,    19,
		   20,    21,    22,    23,    24,    25,    26,    27,
		   28,    29,    30,    31,    32,    32,    32,    32,
		   32
	},
	.isp_tdnf_k = {
		    4,     4,     6,     8,    12,    16,    24,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31,
		   31,    31,    31,    31,    31,    31,    31,    31
	},
	.isp_contrast_val = {
		  103,   160,   160,   160,   160,   160,   176,   192,
		  208,   208,   208,   208,   208,   208,   208,   208,
		  208,   208,   180,   160,   144,   128,   112,    96,
		   80,    72,    64,    56,    48,    32,    32,    32,
		   32
	},
	.isp_contrast_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_sharp_val = {
		  144,   132,   128,   128,   128,   128,   124,   117,
		   96,    96,    80,    80,    64,    64,    48,    48,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32,    32,    32,    32,    32,    32,    32,    32,
		   32
	},
	.isp_sharp_lum = {
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128
	},
	.isp_tdnf_diff = {
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   255,   255,   255,   255,   255,   255,   255,
		  255,   254,   254,   254,   254,   254,   254,   253,
		  253,   253,   253,   253,   252,   252,   252,   252,
		  252,   251,   251,   251,   250,   250,   250,   250,
		  249,   249,   249,   248,   248,   248,   247,   247,
		  247,   246,   246,   245,   245,   245,   244,   244,
		  243,   243,   242,   242,   241,   241,   240,   240,
		  240,   239,   238,   238,   237,   237,   236,   236,
		  235,   235,   234,   234,   233,   232,   232,   231,
		  231,   230,   229,   229,   228,   227,   227,   226,
		  225,   225,   224,   223,   222,   222,   221,   220,
		  220,   219,   218,   217,   216,   216,   215,   214,
		  213,   212,   212,   211,   210,   209,   208,   207,
		  207,   206,   205,   204,   203,   202,   201,   200,
		  199,   198,   197,   196,   195,   194,   193,   192,
		  192,   190,   189,   188,   187,   186,   185,   184,
		  183,   182,   181,   180,   179,   178,   177,   176,
		  175,   173,   172,   171,   170,   169,   168,   166,
		  165,   164,   163,   162,   160,   159,   158,   157,
		  156,   154,   153,   152,   150,   149,   148,   147,
		  145,   144,   143,   141,   140,   139,   137,   136,
		  135,   133,   132,   130,   129,   128,   126,   125,
		  123,   122,   120,   119,   117,   116,   114,   113,
		  112,   110,   108,   107,   105,   104,   102,   101,
		   99,    98,    96,    95,    93,    91,    90,    88,
		   87,    85,    83,    82,    80,    78,    77,    75,
		   73,    72,    70,    68,    66,    65,    63,    61,
		   60,    58,    56,    54,    52,    51,    49,    47,
		   45,    43,    42,    40,    38,    36,    34,    32,
		   31,    29,    27,    25,    23,    21,    19,    17,
		   15,    13,    11,     9,     7,     5,     3,     1
	},
	.isp_contrat_pe = {
		    0,     2,     4,     6,     8,    10,    12,    14,
		   16,    26,    36,    46,    56,    66,    76,    86,
		   96,   100,   104,   108,   112,   116,   120,   124,
		  128,   128,   128,   128,   128,   128,   128,   128,
		  128,   130,   132,   134,   136,   138,   140,   142,
		  144,   146,   148,   150,   152,   154,   156,   158,
		  160,   164,   168,   172,   176,   180,   184,   188,
		  192,   195,   197,   200,   202,   205,   207,   209,
		  212,   209,   207,   205,   202,   200,   197,   195,
		  192,   188,   184,   180,   176,   172,   168,   164,
		  160,   158,   156,   154,   152,   150,   148,   146,
		  144,   142,   140,   138,   136,   134,   132,   130,
		  128,   126,   124,   122,   120,   118,   116,   114,
		  112,   110,   108,   106,   104,   102,   100,    98,
		   96,    96,    96,    96,    96,    96,    96,    96,
		   96,    96,    96,    96,    96,    96,    96,    96
	},
	.gamma_tbl_ini = {
	{
		/* gamma - 0 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 1 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 2 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 3 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	},
	{
		/* gamma - 4 */
		/* R */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* G */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088,
		/* B */
		    0,   59,  118,  176,  235,  293,  350,  407,
		  462,  517,  571,  624,  676,  726,  775,  822,
		  868,  912,  954,  995, 1034, 1072, 1108, 1143,
		 1177, 1210, 1242, 1274, 1304, 1334, 1364, 1392,
		 1421, 1449, 1477, 1505, 1532, 1560, 1587, 1613,
		 1639, 1665, 1691, 1717, 1742, 1767, 1791, 1815,
		 1839, 1863, 1886, 1909, 1931, 1953, 1975, 1997,
		 2019, 2040, 2061, 2081, 2102, 2122, 2142, 2162,
		 2181, 2200, 2219, 2238, 2257, 2275, 2294, 2312,
		 2330, 2347, 2365, 2382, 2400, 2417, 2434, 2450,
		 2467, 2483, 2500, 2516, 2532, 2548, 2563, 2579,
		 2594, 2610, 2625, 2640, 2655, 2670, 2684, 2699,
		 2713, 2727, 2741, 2755, 2769, 2783, 2796, 2810,
		 2823, 2836, 2850, 2863, 2875, 2888, 2901, 2914,
		 2926, 2938, 2951, 2963, 2975, 2987, 2999, 3011,
		 3022, 3034, 3045, 3057, 3068, 3080, 3091, 3102,
		 3113, 3124, 3135, 3146, 3156, 3167, 3178, 3188,
		 3199, 3209, 3219, 3230, 3240, 3250, 3260, 3270,
		 3280, 3290, 3300, 3309, 3319, 3329, 3338, 3348,
		 3357, 3366, 3376, 3385, 3394, 3403, 3412, 3421,
		 3430, 3439, 3448, 3456, 3465, 3474, 3482, 3491,
		 3500, 3508, 3516, 3525, 3533, 3541, 3550, 3558,
		 3566, 3574, 3582, 3590, 3598, 3606, 3614, 3622,
		 3630, 3638, 3645, 3653, 3661, 3668, 3676, 3684,
		 3691, 3698, 3706, 3713, 3721, 3728, 3735, 3742,
		 3749, 3757, 3764, 3771, 3778, 3785, 3791, 3798,
		 3805, 3812, 3818, 3825, 3832, 3838, 3845, 3851,
		 3858, 3864, 3871, 3877, 3884, 3890, 3896, 3903,
		 3909, 3915, 3922, 3928, 3934, 3941, 3947, 3953,
		 3959, 3965, 3971, 3978, 3984, 3989, 3995, 4001,
		 4007, 4013, 4018, 4024, 4029, 4035, 4040, 4046,
		 4051, 4056, 4062, 4067, 4072, 4077, 4083, 4088
	}
	},
	.lsc_tbl = {
	{
		/* lsc - 0 */
		/* R */
		 1024, 1029, 1039, 1048, 1059, 1071, 1081, 1091,
		 1099, 1111, 1120, 1132, 1142, 1153, 1162, 1172,
		 1180, 1190, 1200, 1210, 1218, 1224, 1232, 1242,
		 1253, 1262, 1272, 1281, 1288, 1297, 1306, 1315,
		 1325, 1334, 1343, 1352, 1360, 1368, 1376, 1385,
		 1394, 1402, 1412, 1420, 1430, 1441, 1449, 1458,
		 1468, 1477, 1486, 1493, 1502, 1511, 1520, 1530,
		 1540, 1549, 1554, 1563, 1572, 1584, 1591, 1601,
		 1611, 1622, 1628, 1636, 1645, 1656, 1664, 1672,
		 1680, 1689, 1698, 1707, 1715, 1724, 1732, 1743,
		 1751, 1758, 1765, 1773, 1784, 1795, 1805, 1814,
		 1821, 1825, 1831, 1841, 1849, 1853, 1859, 1866,
		 1875, 1881, 1888, 1893, 1904, 1913, 1922, 1928,
		 1937, 1942, 1950, 1956, 1966, 1974, 1984, 1988,
		 1997, 2004, 2011, 2015, 2027, 2035, 2042, 2046,
		 2053, 2058, 2067, 2075, 2082, 2089, 2101, 2108,
		 2112, 2116, 2123, 2130, 2139, 2147, 2155, 2164,
		 2168, 2171, 2177, 2188, 2195, 2201, 2210, 2217,
		 2224, 2231, 2241, 2250, 2257, 2264, 2273, 2282,
		 2289, 2299, 2305, 2312, 2317, 2327, 2335, 2345,
		 2352, 2365, 2371, 2376, 2382, 2387, 2396, 2401,
		 2411, 2417, 2427, 2432, 2442, 2447, 2453, 2461,
		 2465, 2466, 2474, 2481, 2489, 2497, 2504, 2513,
		 2520, 2528, 2530, 2534, 2537, 2549, 2556, 2557,
		 2558, 2570, 2577, 2582, 2585, 2590, 2596, 2600,
		 2602, 2608, 2616, 2621, 2621, 2626, 2633, 2640,
		 2642, 2650, 2659, 2665, 2667, 2673, 2674, 2676,
		 2678, 2688, 2694, 2703, 2698, 2703, 2701, 2714,
		 2724, 2743, 2743, 2755, 2764, 2761, 2758, 2766,
		 2781, 2793, 2798, 2800, 2814, 2822, 2829, 2825,
		 2846, 2888, 2899, 2909, 2920, 2931, 2942, 2953,
		 2964, 2975, 2985, 2996, 3007, 3018, 3028, 3039,
		/* G */
		 1024, 1024, 1033, 1043, 1052, 1061, 1070, 1078,
		 1086, 1095, 1102, 1109, 1117, 1126, 1133, 1140,
		 1146, 1153, 1161, 1169, 1175, 1179, 1183, 1190,
		 1199, 1206, 1214, 1220, 1226, 1231, 1238, 1243,
		 1250, 1258, 1264, 1269, 1275, 1281, 1287, 1294,
		 1302, 1306, 1313, 1319, 1325, 1332, 1339, 1344,
		 1350, 1356, 1365, 1369, 1375, 1378, 1385, 1393,
		 1400, 1407, 1411, 1420, 1426, 1434, 1437, 1445,
		 1452, 1459, 1462, 1468, 1474, 1480, 1484, 1491,
		 1496, 1502, 1508, 1516, 1520, 1526, 1531, 1538,
		 1543, 1547, 1550, 1557, 1567, 1574, 1579, 1585,
		 1590, 1593, 1595, 1600, 1606, 1612, 1614, 1618,
		 1622, 1627, 1631, 1635, 1643, 1648, 1652, 1654,
		 1661, 1665, 1669, 1670, 1676, 1684, 1692, 1694,
		 1701, 1706, 1710, 1710, 1719, 1726, 1732, 1734,
		 1739, 1742, 1748, 1753, 1759, 1763, 1771, 1776,
		 1780, 1783, 1786, 1791, 1798, 1805, 1810, 1816,
		 1817, 1819, 1824, 1832, 1838, 1845, 1851, 1853,
		 1857, 1863, 1871, 1875, 1879, 1884, 1893, 1902,
		 1907, 1913, 1918, 1925, 1928, 1934, 1940, 1946,
		 1951, 1956, 1961, 1966, 1973, 1978, 1983, 1985,
		 1992, 1994, 1998, 2002, 2014, 2022, 2024, 2027,
		 2030, 2034, 2043, 2049, 2050, 2056, 2063, 2068,
		 2069, 2077, 2081, 2083, 2081, 2088, 2095, 2102,
		 2106, 2113, 2113, 2117, 2121, 2127, 2132, 2140,
		 2143, 2147, 2151, 2152, 2147, 2152, 2161, 2173,
		 2178, 2184, 2184, 2186, 2188, 2195, 2199, 2207,
		 2210, 2213, 2212, 2217, 2219, 2236, 2239, 2247,
		 2243, 2251, 2255, 2274, 2288, 2297, 2302, 2296,
		 2294, 2311, 2326, 2331, 2328, 2335, 2362, 2369,
		 2370, 2387, 2398, 2409, 2420, 2431, 2442, 2453,
		 2464, 2476, 2487, 2498, 2509, 2520, 2531, 2542,
		/* B */
		 1024, 1024, 1037, 1046, 1056, 1067, 1077, 1083,
		 1090, 1105, 1115, 1123, 1127, 1138, 1145, 1152,
		 1156, 1166, 1177, 1183, 1186, 1189, 1194, 1204,
		 1214, 1220, 1227, 1235, 1239, 1244, 1252, 1258,
		 1264, 1271, 1277, 1281, 1287, 1294, 1301, 1311,
		 1319, 1323, 1329, 1333, 1337, 1344, 1354, 1356,
		 1360, 1366, 1375, 1378, 1384, 1387, 1394, 1400,
		 1406, 1413, 1419, 1430, 1439, 1444, 1444, 1451,
		 1461, 1466, 1468, 1473, 1478, 1484, 1489, 1494,
		 1501, 1508, 1516, 1523, 1523, 1526, 1531, 1539,
		 1546, 1551, 1552, 1559, 1576, 1585, 1589, 1592,
		 1596, 1600, 1603, 1610, 1615, 1620, 1625, 1629,
		 1631, 1635, 1641, 1645, 1652, 1657, 1663, 1667,
		 1676, 1674, 1678, 1685, 1695, 1701, 1705, 1703,
		 1709, 1715, 1724, 1723, 1731, 1737, 1745, 1747,
		 1754, 1758, 1765, 1767, 1773, 1780, 1793, 1798,
		 1800, 1799, 1799, 1799, 1806, 1817, 1824, 1829,
		 1828, 1833, 1837, 1846, 1850, 1858, 1863, 1861,
		 1867, 1872, 1879, 1880, 1883, 1887, 1900, 1912,
		 1915, 1919, 1926, 1929, 1926, 1928, 1934, 1943,
		 1954, 1965, 1972, 1981, 1990, 1987, 1988, 1992,
		 2002, 2000, 2000, 2007, 2022, 2034, 2036, 2039,
		 2040, 2046, 2059, 2061, 2060, 2072, 2087, 2092,
		 2091, 2105, 2112, 2104, 2092, 2097, 2105, 2121,
		 2135, 2148, 2138, 2138, 2136, 2144, 2155, 2176,
		 2185, 2191, 2188, 2183, 2169, 2177, 2196, 2221,
		 2220, 2227, 2230, 2222, 2213, 2216, 2224, 2229,
		 2228, 2238, 2249, 2277, 2274, 2275, 2258, 2282,
		 2279, 2291, 2282, 2308, 2322, 2320, 2301, 2291,
		 2322, 2378, 2401, 2377, 2357, 2365, 2447, 2439,
		 2398, 2416, 2427, 2438, 2449, 2460, 2471, 2482,
		 2493, 2504, 2515, 2526, 2537, 2548, 2559, 2570
	},
	{
		/* lsc - 1 */
		/* R */
		 1024, 1027, 1034, 1042, 1052, 1062, 1070, 1078,
		 1087, 1098, 1106, 1116, 1125, 1134, 1143, 1151,
		 1159, 1168, 1178, 1186, 1194, 1202, 1211, 1220,
		 1228, 1237, 1245, 1252, 1261, 1270, 1278, 1287,
		 1296, 1304, 1313, 1322, 1331, 1340, 1349, 1356,
		 1363, 1373, 1382, 1391, 1398, 1405, 1414, 1422,
		 1431, 1439, 1447, 1453, 1461, 1471, 1480, 1487,
		 1495, 1504, 1511, 1518, 1527, 1536, 1545, 1553,
		 1561, 1570, 1578, 1585, 1592, 1600, 1608, 1615,
		 1623, 1632, 1639, 1648, 1656, 1663, 1670, 1679,
		 1687, 1694, 1701, 1707, 1716, 1725, 1733, 1741,
		 1748, 1754, 1760, 1766, 1772, 1779, 1786, 1794,
		 1801, 1808, 1812, 1819, 1828, 1836, 1842, 1847,
		 1856, 1864, 1874, 1879, 1885, 1892, 1899, 1904,
		 1912, 1921, 1928, 1933, 1940, 1947, 1954, 1959,
		 1965, 1974, 1982, 1989, 1995, 2003, 2009, 2014,
		 2020, 2028, 2037, 2044, 2051, 2057, 2063, 2070,
		 2078, 2082, 2089, 2097, 2105, 2109, 2116, 2123,
		 2132, 2136, 2142, 2147, 2155, 2163, 2174, 2181,
		 2188, 2193, 2199, 2206, 2214, 2222, 2227, 2234,
		 2241, 2250, 2256, 2262, 2270, 2279, 2288, 2292,
		 2299, 2307, 2312, 2315, 2322, 2331, 2337, 2345,
		 2352, 2356, 2361, 2364, 2370, 2377, 2387, 2391,
		 2392, 2395, 2401, 2407, 2413, 2421, 2423, 2423,
		 2430, 2440, 2447, 2449, 2455, 2457, 2459, 2461,
		 2465, 2472, 2475, 2475, 2476, 2486, 2489, 2493,
		 2497, 2503, 2509, 2518, 2525, 2522, 2523, 2535,
		 2545, 2551, 2546, 2546, 2548, 2551, 2562, 2567,
		 2571, 2572, 2587, 2592, 2592, 2596, 2607, 2615,
		 2623, 2634, 2639, 2647, 2654, 2663, 2666, 2673,
		 2682, 2703, 2711, 2719, 2727, 2735, 2743, 2751,
		 2759, 2766, 2774, 2782, 2790, 2798, 2806, 2813,
		/* G */
		 1024, 1024, 1029, 1036, 1043, 1051, 1057, 1063,
		 1070, 1078, 1085, 1092, 1099, 1105, 1111, 1117,
		 1123, 1130, 1137, 1142, 1147, 1153, 1159, 1166,
		 1172, 1179, 1185, 1192, 1197, 1204, 1209, 1215,
		 1223, 1229, 1234, 1239, 1245, 1253, 1259, 1265,
		 1269, 1276, 1283, 1289, 1293, 1297, 1304, 1310,
		 1317, 1322, 1327, 1331, 1337, 1344, 1350, 1355,
		 1361, 1366, 1371, 1375, 1381, 1387, 1393, 1398,
		 1403, 1409, 1415, 1420, 1425, 1429, 1433, 1438,
		 1444, 1450, 1454, 1459, 1464, 1468, 1472, 1479,
		 1485, 1490, 1495, 1500, 1506, 1511, 1518, 1525,
		 1529, 1531, 1535, 1540, 1544, 1547, 1552, 1559,
		 1563, 1567, 1569, 1574, 1579, 1585, 1588, 1592,
		 1597, 1603, 1608, 1612, 1616, 1620, 1625, 1629,
		 1634, 1638, 1643, 1647, 1652, 1657, 1662, 1665,
		 1668, 1674, 1680, 1685, 1687, 1693, 1697, 1702,
		 1706, 1710, 1714, 1720, 1726, 1731, 1735, 1740,
		 1745, 1748, 1752, 1756, 1761, 1765, 1772, 1776,
		 1782, 1784, 1789, 1794, 1802, 1807, 1815, 1821,
		 1825, 1830, 1837, 1844, 1849, 1853, 1856, 1861,
		 1867, 1872, 1876, 1881, 1887, 1894, 1899, 1903,
		 1908, 1914, 1917, 1919, 1926, 1933, 1938, 1942,
		 1946, 1951, 1955, 1958, 1963, 1968, 1971, 1974,
		 1978, 1983, 1986, 1989, 1990, 1995, 2000, 2004,
		 2011, 2019, 2023, 2025, 2029, 2028, 2028, 2031,
		 2035, 2040, 2043, 2047, 2050, 2058, 2060, 2063,
		 2067, 2072, 2078, 2082, 2084, 2085, 2086, 2091,
		 2096, 2104, 2106, 2109, 2112, 2116, 2126, 2129,
		 2131, 2129, 2135, 2139, 2147, 2158, 2170, 2174,
		 2179, 2191, 2195, 2205, 2214, 2224, 2223, 2231,
		 2243, 2256, 2264, 2272, 2280, 2289, 2297, 2305,
		 2313, 2321, 2329, 2337, 2345, 2353, 2361, 2369,
		/* B */
		 1024, 1023, 1028, 1036, 1042, 1050, 1055, 1061,
		 1070, 1080, 1085, 1092, 1098, 1106, 1112, 1119,
		 1123, 1130, 1136, 1142, 1143, 1147, 1154, 1163,
		 1169, 1177, 1183, 1188, 1194, 1201, 1206, 1213,
		 1220, 1224, 1229, 1235, 1240, 1245, 1249, 1253,
		 1257, 1263, 1269, 1274, 1278, 1280, 1285, 1292,
		 1300, 1305, 1311, 1314, 1322, 1328, 1333, 1334,
		 1339, 1344, 1349, 1355, 1361, 1364, 1370, 1376,
		 1380, 1383, 1391, 1397, 1401, 1404, 1408, 1411,
		 1417, 1424, 1430, 1434, 1439, 1443, 1446, 1453,
		 1460, 1466, 1470, 1473, 1477, 1482, 1490, 1497,
		 1500, 1498, 1503, 1510, 1515, 1515, 1519, 1525,
		 1531, 1533, 1535, 1541, 1547, 1549, 1549, 1550,
		 1558, 1563, 1569, 1572, 1578, 1579, 1583, 1585,
		 1590, 1594, 1599, 1604, 1609, 1614, 1620, 1623,
		 1623, 1633, 1642, 1645, 1644, 1651, 1656, 1664,
		 1668, 1674, 1675, 1681, 1686, 1689, 1692, 1700,
		 1705, 1705, 1708, 1713, 1720, 1724, 1732, 1734,
		 1738, 1738, 1741, 1747, 1756, 1762, 1769, 1774,
		 1778, 1782, 1792, 1796, 1796, 1796, 1803, 1811,
		 1816, 1819, 1825, 1832, 1837, 1841, 1848, 1853,
		 1858, 1863, 1866, 1872, 1878, 1885, 1884, 1889,
		 1895, 1902, 1908, 1911, 1915, 1920, 1928, 1931,
		 1936, 1938, 1941, 1944, 1950, 1956, 1959, 1962,
		 1975, 1988, 1990, 1985, 1985, 1987, 1992, 1996,
		 1999, 2005, 2011, 2016, 2016, 2029, 2027, 2028,
		 2029, 2044, 2047, 2053, 2057, 2061, 2062, 2069,
		 2071, 2074, 2067, 2065, 2073, 2084, 2093, 2089,
		 2093, 2098, 2103, 2099, 2105, 2121, 2134, 2129,
		 2127, 2146, 2158, 2174, 2193, 2215, 2207, 2215,
		 2228, 2208, 2216, 2224, 2232, 2240, 2248, 2256,
		 2265, 2273, 2281, 2289, 2297, 2305, 2313, 2321
	},
	{
		/* lsc - 2 */
		/* R */
		 1024, 1027, 1032, 1038, 1042, 1048, 1053, 1059,
		 1066, 1071, 1075, 1080, 1087, 1093, 1097, 1102,
		 1108, 1114, 1119, 1125, 1130, 1135, 1141, 1145,
		 1150, 1156, 1163, 1167, 1171, 1176, 1181, 1187,
		 1192, 1197, 1203, 1209, 1214, 1219, 1224, 1229,
		 1235, 1241, 1246, 1250, 1255, 1261, 1268, 1273,
		 1277, 1281, 1286, 1291, 1297, 1303, 1308, 1311,
		 1315, 1321, 1327, 1332, 1336, 1342, 1348, 1354,
		 1358, 1364, 1370, 1375, 1380, 1384, 1389, 1394,
		 1399, 1403, 1408, 1413, 1420, 1425, 1429, 1434,
		 1439, 1443, 1448, 1453, 1459, 1463, 1469, 1475,
		 1478, 1480, 1483, 1488, 1491, 1496, 1500, 1503,
		 1505, 1511, 1516, 1518, 1520, 1527, 1536, 1542,
		 1545, 1544, 1547, 1552, 1560, 1564, 1567, 1571,
		 1577, 1581, 1585, 1590, 1597, 1599, 1604, 1606,
		 1609, 1613, 1620, 1625, 1631, 1636, 1641, 1643,
		 1646, 1648, 1655, 1664, 1669, 1672, 1675, 1680,
		 1681, 1686, 1692, 1699, 1703, 1706, 1709, 1711,
		 1719, 1727, 1733, 1736, 1738, 1742, 1751, 1759,
		 1764, 1766, 1772, 1778, 1785, 1790, 1793, 1800,
		 1807, 1813, 1817, 1820, 1828, 1836, 1840, 1840,
		 1847, 1855, 1863, 1869, 1876, 1878, 1880, 1884,
		 1891, 1893, 1901, 1907, 1912, 1914, 1922, 1928,
		 1931, 1933, 1937, 1945, 1954, 1963, 1964, 1962,
		 1967, 1974, 1981, 1983, 1986, 1987, 1994, 2003,
		 2008, 2011, 2014, 2017, 2016, 2023, 2030, 2039,
		 2040, 2039, 2046, 2055, 2063, 2068, 2073, 2068,
		 2067, 2077, 2087, 2086, 2091, 2098, 2111, 2114,
		 2119, 2119, 2124, 2124, 2133, 2145, 2156, 2149,
		 2165, 2175, 2195, 2197, 2207, 2200, 2222, 2229,
		 2230, 2240, 2248, 2257, 2265, 2273, 2281, 2289,
		 2297, 2305, 2313, 2321, 2329, 2337, 2346, 2354,
		/* G */
		 1024, 1027, 1034, 1040, 1045, 1050, 1056, 1063,
		 1070, 1075, 1079, 1085, 1091, 1098, 1101, 1106,
		 1112, 1118, 1122, 1128, 1134, 1139, 1144, 1147,
		 1153, 1158, 1163, 1168, 1173, 1176, 1181, 1186,
		 1192, 1196, 1201, 1207, 1212, 1215, 1220, 1225,
		 1231, 1236, 1240, 1244, 1248, 1253, 1258, 1262,
		 1267, 1272, 1276, 1280, 1285, 1291, 1295, 1298,
		 1302, 1308, 1314, 1317, 1321, 1326, 1331, 1336,
		 1340, 1344, 1350, 1356, 1359, 1364, 1369, 1373,
		 1376, 1380, 1385, 1390, 1395, 1399, 1403, 1407,
		 1412, 1416, 1421, 1425, 1431, 1436, 1442, 1447,
		 1450, 1451, 1454, 1459, 1463, 1467, 1470, 1473,
		 1475, 1480, 1485, 1489, 1493, 1499, 1504, 1508,
		 1511, 1513, 1515, 1519, 1526, 1530, 1533, 1537,
		 1541, 1544, 1548, 1553, 1559, 1562, 1566, 1568,
		 1571, 1576, 1583, 1586, 1590, 1594, 1599, 1602,
		 1605, 1608, 1613, 1620, 1624, 1627, 1629, 1635,
		 1638, 1641, 1645, 1651, 1655, 1658, 1662, 1665,
		 1672, 1678, 1682, 1684, 1688, 1694, 1702, 1707,
		 1711, 1715, 1721, 1726, 1730, 1735, 1740, 1745,
		 1748, 1752, 1757, 1759, 1765, 1771, 1777, 1778,
		 1783, 1786, 1793, 1798, 1806, 1808, 1811, 1813,
		 1819, 1820, 1827, 1832, 1838, 1841, 1845, 1849,
		 1854, 1857, 1857, 1860, 1865, 1871, 1877, 1881,
		 1885, 1886, 1888, 1891, 1897, 1901, 1905, 1911,
		 1915, 1918, 1921, 1926, 1929, 1936, 1939, 1943,
		 1944, 1944, 1950, 1952, 1954, 1958, 1966, 1967,
		 1966, 1971, 1978, 1984, 1989, 1995, 1999, 2001,
		 2007, 2012, 2013, 2014, 2024, 2037, 2048, 2045,
		 2054, 2055, 2066, 2069, 2081, 2076, 2095, 2109,
		 2124, 2128, 2136, 2145, 2153, 2161, 2169, 2177,
		 2185, 2194, 2202, 2210, 2218, 2226, 2234, 2242,
		/* B */
		 1024, 1026, 1032, 1036, 1038, 1041, 1048, 1054,
		 1059, 1063, 1068, 1074, 1079, 1083, 1084, 1090,
		 1096, 1102, 1106, 1111, 1115, 1119, 1123, 1129,
		 1134, 1136, 1140, 1143, 1149, 1151, 1154, 1159,
		 1165, 1169, 1172, 1177, 1182, 1184, 1188, 1195,
		 1201, 1204, 1208, 1212, 1216, 1221, 1226, 1228,
		 1233, 1236, 1241, 1243, 1248, 1253, 1256, 1258,
		 1262, 1269, 1272, 1275, 1277, 1282, 1285, 1291,
		 1294, 1297, 1300, 1306, 1309, 1314, 1317, 1321,
		 1322, 1326, 1331, 1337, 1341, 1344, 1347, 1350,
		 1352, 1356, 1362, 1368, 1373, 1375, 1382, 1385,
		 1388, 1388, 1391, 1396, 1400, 1402, 1404, 1409,
		 1412, 1414, 1419, 1423, 1425, 1431, 1438, 1440,
		 1441, 1442, 1445, 1449, 1455, 1458, 1460, 1466,
		 1470, 1471, 1473, 1479, 1485, 1489, 1493, 1493,
		 1495, 1499, 1507, 1511, 1514, 1516, 1520, 1522,
		 1525, 1528, 1533, 1538, 1540, 1543, 1547, 1555,
		 1560, 1561, 1563, 1566, 1571, 1573, 1580, 1582,
		 1588, 1595, 1600, 1602, 1604, 1608, 1614, 1619,
		 1622, 1626, 1633, 1637, 1640, 1644, 1651, 1657,
		 1661, 1666, 1672, 1673, 1679, 1688, 1694, 1690,
		 1693, 1698, 1703, 1706, 1718, 1726, 1729, 1725,
		 1731, 1731, 1739, 1746, 1756, 1757, 1760, 1766,
		 1770, 1768, 1765, 1767, 1773, 1781, 1788, 1790,
		 1791, 1794, 1799, 1800, 1800, 1800, 1807, 1818,
		 1825, 1829, 1831, 1837, 1836, 1842, 1849, 1856,
		 1855, 1851, 1857, 1863, 1869, 1874, 1878, 1873,
		 1873, 1879, 1881, 1885, 1896, 1902, 1906, 1908,
		 1913, 1917, 1923, 1927, 1938, 1949, 1958, 1960,
		 1981, 1982, 1987, 1978, 1996, 1999, 2022, 2012,
		 2013, 2048, 2057, 2065, 2073, 2081, 2089, 2098,
		 2106, 2114, 2122, 2130, 2138, 2147, 2155, 2163
	},
	{
		/* lsc - 3 */
		/* R */
		 1024, 1027, 1032, 1038, 1042, 1048, 1053, 1059,
		 1066, 1071, 1075, 1080, 1087, 1093, 1097, 1102,
		 1108, 1114, 1119, 1125, 1130, 1135, 1141, 1145,
		 1150, 1156, 1163, 1167, 1171, 1176, 1181, 1187,
		 1192, 1197, 1203, 1209, 1214, 1219, 1224, 1229,
		 1235, 1241, 1246, 1250, 1255, 1261, 1268, 1273,
		 1277, 1281, 1286, 1291, 1297, 1303, 1308, 1311,
		 1315, 1321, 1327, 1332, 1336, 1342, 1348, 1354,
		 1358, 1364, 1370, 1375, 1380, 1384, 1389, 1394,
		 1399, 1403, 1408, 1413, 1420, 1425, 1429, 1434,
		 1439, 1443, 1448, 1453, 1459, 1463, 1469, 1475,
		 1478, 1480, 1483, 1488, 1491, 1496, 1500, 1503,
		 1505, 1511, 1516, 1518, 1520, 1527, 1536, 1542,
		 1545, 1544, 1547, 1552, 1560, 1564, 1567, 1571,
		 1577, 1581, 1585, 1590, 1597, 1599, 1604, 1606,
		 1609, 1613, 1620, 1625, 1631, 1636, 1641, 1643,
		 1646, 1648, 1655, 1664, 1669, 1672, 1675, 1680,
		 1681, 1686, 1692, 1699, 1703, 1706, 1709, 1711,
		 1719, 1727, 1733, 1736, 1738, 1742, 1751, 1759,
		 1764, 1766, 1772, 1778, 1785, 1790, 1793, 1800,
		 1807, 1813, 1817, 1820, 1828, 1836, 1840, 1840,
		 1847, 1855, 1863, 1869, 1876, 1878, 1880, 1884,
		 1891, 1893, 1901, 1907, 1912, 1914, 1922, 1928,
		 1931, 1933, 1937, 1945, 1954, 1963, 1964, 1962,
		 1967, 1974, 1981, 1983, 1986, 1987, 1994, 2003,
		 2008, 2011, 2014, 2017, 2016, 2023, 2030, 2039,
		 2040, 2039, 2046, 2055, 2063, 2068, 2073, 2068,
		 2067, 2077, 2087, 2086, 2091, 2098, 2111, 2114,
		 2119, 2119, 2124, 2124, 2133, 2145, 2156, 2149,
		 2165, 2175, 2195, 2197, 2207, 2200, 2222, 2229,
		 2230, 2240, 2248, 2257, 2265, 2273, 2281, 2289,
		 2297, 2305, 2313, 2321, 2329, 2337, 2346, 2354,
		/* G */
		 1024, 1027, 1034, 1040, 1045, 1050, 1056, 1063,
		 1070, 1075, 1079, 1085, 1091, 1098, 1101, 1106,
		 1112, 1118, 1122, 1128, 1134, 1139, 1144, 1147,
		 1153, 1158, 1163, 1168, 1173, 1176, 1181, 1186,
		 1192, 1196, 1201, 1207, 1212, 1215, 1220, 1225,
		 1231, 1236, 1240, 1244, 1248, 1253, 1258, 1262,
		 1267, 1272, 1276, 1280, 1285, 1291, 1295, 1298,
		 1302, 1308, 1314, 1317, 1321, 1326, 1331, 1336,
		 1340, 1344, 1350, 1356, 1359, 1364, 1369, 1373,
		 1376, 1380, 1385, 1390, 1395, 1399, 1403, 1407,
		 1412, 1416, 1421, 1425, 1431, 1436, 1442, 1447,
		 1450, 1451, 1454, 1459, 1463, 1467, 1470, 1473,
		 1475, 1480, 1485, 1489, 1493, 1499, 1504, 1508,
		 1511, 1513, 1515, 1519, 1526, 1530, 1533, 1537,
		 1541, 1544, 1548, 1553, 1559, 1562, 1566, 1568,
		 1571, 1576, 1583, 1586, 1590, 1594, 1599, 1602,
		 1605, 1608, 1613, 1620, 1624, 1627, 1629, 1635,
		 1638, 1641, 1645, 1651, 1655, 1658, 1662, 1665,
		 1672, 1678, 1682, 1684, 1688, 1694, 1702, 1707,
		 1711, 1715, 1721, 1726, 1730, 1735, 1740, 1745,
		 1748, 1752, 1757, 1759, 1765, 1771, 1777, 1778,
		 1783, 1786, 1793, 1798, 1806, 1808, 1811, 1813,
		 1819, 1820, 1827, 1832, 1838, 1841, 1845, 1849,
		 1854, 1857, 1857, 1860, 1865, 1871, 1877, 1881,
		 1885, 1886, 1888, 1891, 1897, 1901, 1905, 1911,
		 1915, 1918, 1921, 1926, 1929, 1936, 1939, 1943,
		 1944, 1944, 1950, 1952, 1954, 1958, 1966, 1967,
		 1966, 1971, 1978, 1984, 1989, 1995, 1999, 2001,
		 2007, 2012, 2013, 2014, 2024, 2037, 2048, 2045,
		 2054, 2055, 2066, 2069, 2081, 2076, 2095, 2109,
		 2124, 2128, 2136, 2145, 2153, 2161, 2169, 2177,
		 2185, 2194, 2202, 2210, 2218, 2226, 2234, 2242,
		/* B */
		 1024, 1026, 1032, 1036, 1038, 1041, 1048, 1054,
		 1059, 1063, 1068, 1074, 1079, 1083, 1084, 1090,
		 1096, 1102, 1106, 1111, 1115, 1119, 1123, 1129,
		 1134, 1136, 1140, 1143, 1149, 1151, 1154, 1159,
		 1165, 1169, 1172, 1177, 1182, 1184, 1188, 1195,
		 1201, 1204, 1208, 1212, 1216, 1221, 1226, 1228,
		 1233, 1236, 1241, 1243, 1248, 1253, 1256, 1258,
		 1262, 1269, 1272, 1275, 1277, 1282, 1285, 1291,
		 1294, 1297, 1300, 1306, 1309, 1314, 1317, 1321,
		 1322, 1326, 1331, 1337, 1341, 1344, 1347, 1350,
		 1352, 1356, 1362, 1368, 1373, 1375, 1382, 1385,
		 1388, 1388, 1391, 1396, 1400, 1402, 1404, 1409,
		 1412, 1414, 1419, 1423, 1425, 1431, 1438, 1440,
		 1441, 1442, 1445, 1449, 1455, 1458, 1460, 1466,
		 1470, 1471, 1473, 1479, 1485, 1489, 1493, 1493,
		 1495, 1499, 1507, 1511, 1514, 1516, 1520, 1522,
		 1525, 1528, 1533, 1538, 1540, 1543, 1547, 1555,
		 1560, 1561, 1563, 1566, 1571, 1573, 1580, 1582,
		 1588, 1595, 1600, 1602, 1604, 1608, 1614, 1619,
		 1622, 1626, 1633, 1637, 1640, 1644, 1651, 1657,
		 1661, 1666, 1672, 1673, 1679, 1688, 1694, 1690,
		 1693, 1698, 1703, 1706, 1718, 1726, 1729, 1725,
		 1731, 1731, 1739, 1746, 1756, 1757, 1760, 1766,
		 1770, 1768, 1765, 1767, 1773, 1781, 1788, 1790,
		 1791, 1794, 1799, 1800, 1800, 1800, 1807, 1818,
		 1825, 1829, 1831, 1837, 1836, 1842, 1849, 1856,
		 1855, 1851, 1857, 1863, 1869, 1874, 1878, 1873,
		 1873, 1879, 1881, 1885, 1896, 1902, 1906, 1908,
		 1913, 1917, 1923, 1927, 1938, 1949, 1958, 1960,
		 1981, 1982, 1987, 1978, 1996, 1999, 2022, 2012,
		 2013, 2048, 2057, 2065, 2073, 2081, 2089, 2098,
		 2106, 2114, 2122, 2130, 2138, 2147, 2155, 2163
	},
	{
		/* lsc - 4 */
		/* R */
		 1024, 1026, 1035, 1045, 1053, 1061, 1070, 1080,
		 1089, 1097, 1103, 1111, 1120, 1130, 1138, 1146,
		 1153, 1160, 1167, 1173, 1181, 1189, 1195, 1200,
		 1208, 1218, 1225, 1230, 1237, 1246, 1253, 1258,
		 1264, 1272, 1280, 1287, 1294, 1303, 1311, 1316,
		 1322, 1329, 1337, 1343, 1350, 1357, 1364, 1372,
		 1378, 1384, 1390, 1399, 1407, 1414, 1420, 1427,
		 1434, 1440, 1446, 1453, 1462, 1470, 1476, 1484,
		 1490, 1500, 1505, 1509, 1515, 1524, 1531, 1535,
		 1540, 1549, 1558, 1563, 1568, 1579, 1584, 1590,
		 1596, 1604, 1609, 1616, 1624, 1632, 1639, 1643,
		 1646, 1651, 1659, 1663, 1668, 1674, 1682, 1686,
		 1692, 1699, 1704, 1707, 1712, 1719, 1725, 1729,
		 1738, 1743, 1749, 1754, 1761, 1766, 1769, 1776,
		 1786, 1792, 1796, 1802, 1810, 1816, 1819, 1823,
		 1829, 1837, 1841, 1844, 1852, 1861, 1868, 1870,
		 1875, 1883, 1889, 1893, 1899, 1901, 1905, 1912,
		 1921, 1926, 1929, 1937, 1943, 1950, 1959, 1963,
		 1971, 1974, 1981, 1982, 1988, 1997, 2007, 2016,
		 2019, 2026, 2033, 2041, 2047, 2051, 2056, 2064,
		 2073, 2081, 2082, 2086, 2095, 2108, 2115, 2117,
		 2114, 2121, 2136, 2147, 2149, 2149, 2158, 2168,
		 2178, 2178, 2179, 2181, 2191, 2197, 2204, 2214,
		 2221, 2223, 2224, 2238, 2239, 2244, 2243, 2249,
		 2251, 2262, 2274, 2281, 2277, 2276, 2281, 2289,
		 2291, 2295, 2305, 2317, 2330, 2331, 2334, 2342,
		 2347, 2351, 2364, 2370, 2367, 2370, 2379, 2384,
		 2392, 2399, 2400, 2405, 2414, 2418, 2426, 2435,
		 2438, 2438, 2456, 2463, 2458, 2467, 2472, 2479,
		 2489, 2516, 2524, 2528, 2516, 2534, 2554, 2566,
		 2556, 2600, 2611, 2622, 2632, 2643, 2654, 2665,
		 2675, 2686, 2697, 2708, 2718, 2729, 2740, 2750,
		/* G */
		 1024, 1026, 1034, 1041, 1047, 1053, 1060, 1069,
		 1076, 1081, 1086, 1093, 1098, 1105, 1111, 1117,
		 1122, 1126, 1131, 1136, 1142, 1147, 1152, 1156,
		 1162, 1168, 1173, 1178, 1183, 1189, 1194, 1198,
		 1204, 1209, 1215, 1218, 1223, 1229, 1236, 1241,
		 1246, 1250, 1255, 1259, 1265, 1270, 1276, 1281,
		 1286, 1290, 1294, 1301, 1306, 1311, 1316, 1320,
		 1325, 1330, 1335, 1340, 1346, 1351, 1357, 1362,
		 1367, 1373, 1377, 1382, 1387, 1392, 1396, 1400,
		 1404, 1411, 1415, 1420, 1424, 1431, 1434, 1438,
		 1443, 1448, 1452, 1458, 1464, 1469, 1476, 1481,
		 1483, 1484, 1488, 1492, 1495, 1498, 1504, 1508,
		 1513, 1517, 1521, 1522, 1526, 1531, 1536, 1538,
		 1544, 1548, 1552, 1556, 1562, 1566, 1569, 1575,
		 1581, 1582, 1585, 1591, 1597, 1602, 1606, 1609,
		 1613, 1617, 1620, 1624, 1630, 1636, 1641, 1645,
		 1648, 1652, 1657, 1661, 1666, 1670, 1673, 1676,
		 1681, 1685, 1690, 1695, 1700, 1702, 1708, 1712,
		 1718, 1721, 1728, 1731, 1737, 1742, 1749, 1754,
		 1758, 1764, 1770, 1777, 1781, 1785, 1789, 1794,
		 1800, 1807, 1810, 1813, 1816, 1823, 1828, 1832,
		 1834, 1838, 1845, 1850, 1854, 1858, 1865, 1872,
		 1875, 1874, 1878, 1882, 1889, 1894, 1898, 1903,
		 1905, 1909, 1915, 1926, 1926, 1927, 1927, 1933,
		 1936, 1943, 1948, 1954, 1954, 1958, 1963, 1969,
		 1971, 1977, 1982, 1989, 1997, 1999, 2003, 2006,
		 2007, 2009, 2018, 2023, 2022, 2025, 2030, 2032,
		 2035, 2041, 2049, 2055, 2063, 2065, 2070, 2072,
		 2077, 2080, 2090, 2094, 2096, 2108, 2115, 2120,
		 2123, 2136, 2138, 2141, 2143, 2164, 2186, 2198,
		 2198, 2220, 2231, 2242, 2253, 2264, 2275, 2286,
		 2297, 2308, 2319, 2330, 2340, 2351, 2362, 2373,
		/* B */
		 1024, 1022, 1030, 1038, 1043, 1048, 1055, 1063,
		 1071, 1076, 1079, 1085, 1091, 1098, 1103, 1109,
		 1114, 1118, 1122, 1126, 1130, 1136, 1142, 1145,
		 1150, 1155, 1160, 1163, 1168, 1174, 1178, 1181,
		 1186, 1191, 1195, 1200, 1206, 1212, 1218, 1223,
		 1228, 1230, 1233, 1237, 1244, 1250, 1255, 1260,
		 1262, 1266, 1270, 1277, 1282, 1286, 1290, 1295,
		 1300, 1303, 1307, 1312, 1319, 1324, 1329, 1333,
		 1337, 1344, 1348, 1352, 1356, 1362, 1365, 1368,
		 1372, 1378, 1382, 1385, 1390, 1397, 1400, 1404,
		 1409, 1412, 1415, 1422, 1426, 1431, 1438, 1444,
		 1445, 1445, 1447, 1450, 1453, 1458, 1465, 1469,
		 1470, 1472, 1476, 1479, 1484, 1487, 1492, 1494,
		 1500, 1503, 1506, 1510, 1515, 1519, 1524, 1529,
		 1533, 1533, 1535, 1539, 1545, 1551, 1554, 1557,
		 1559, 1566, 1570, 1571, 1574, 1580, 1586, 1590,
		 1591, 1595, 1597, 1602, 1609, 1614, 1616, 1620,
		 1622, 1622, 1626, 1633, 1638, 1640, 1647, 1650,
		 1657, 1660, 1665, 1665, 1669, 1674, 1680, 1685,
		 1689, 1695, 1705, 1711, 1713, 1716, 1722, 1729,
		 1734, 1739, 1742, 1746, 1749, 1755, 1760, 1765,
		 1766, 1769, 1775, 1781, 1783, 1783, 1791, 1800,
		 1806, 1806, 1811, 1814, 1818, 1821, 1827, 1834,
		 1836, 1837, 1841, 1855, 1857, 1858, 1853, 1857,
		 1860, 1871, 1879, 1884, 1882, 1883, 1889, 1898,
		 1901, 1907, 1911, 1917, 1922, 1924, 1926, 1929,
		 1925, 1925, 1940, 1951, 1947, 1945, 1950, 1952,
		 1957, 1964, 1966, 1969, 1978, 1983, 1984, 1986,
		 1994, 1998, 2002, 2000, 2003, 2015, 2021, 2031,
		 2040, 2050, 2047, 2045, 2053, 2071, 2096, 2118,
		 2131, 2119, 2130, 2141, 2152, 2163, 2174, 2185,
		 2196, 2207, 2217, 2228, 2239, 2250, 2261, 2272
	},
	{
		/* lsc - 5 */
		/* R */
		 1024, 1026, 1035, 1045, 1053, 1061, 1070, 1080,
		 1089, 1097, 1103, 1111, 1120, 1130, 1138, 1146,
		 1153, 1160, 1167, 1173, 1181, 1189, 1195, 1200,
		 1208, 1218, 1225, 1230, 1237, 1246, 1253, 1258,
		 1264, 1272, 1280, 1287, 1294, 1303, 1311, 1316,
		 1322, 1329, 1337, 1343, 1350, 1357, 1364, 1372,
		 1378, 1384, 1390, 1399, 1407, 1414, 1420, 1427,
		 1434, 1440, 1446, 1453, 1462, 1470, 1476, 1484,
		 1490, 1500, 1505, 1509, 1515, 1524, 1531, 1535,
		 1540, 1549, 1558, 1563, 1568, 1579, 1584, 1590,
		 1596, 1604, 1609, 1616, 1624, 1632, 1639, 1643,
		 1646, 1651, 1659, 1663, 1668, 1674, 1682, 1686,
		 1692, 1699, 1704, 1707, 1712, 1719, 1725, 1729,
		 1738, 1743, 1749, 1754, 1761, 1766, 1769, 1776,
		 1786, 1792, 1796, 1802, 1810, 1816, 1819, 1823,
		 1829, 1837, 1841, 1844, 1852, 1861, 1868, 1870,
		 1875, 1883, 1889, 1893, 1899, 1901, 1905, 1912,
		 1921, 1926, 1929, 1937, 1943, 1950, 1959, 1963,
		 1971, 1974, 1981, 1982, 1988, 1997, 2007, 2016,
		 2019, 2026, 2033, 2041, 2047, 2051, 2056, 2064,
		 2073, 2081, 2082, 2086, 2095, 2108, 2115, 2117,
		 2114, 2121, 2136, 2147, 2149, 2149, 2158, 2168,
		 2178, 2178, 2179, 2181, 2191, 2197, 2204, 2214,
		 2221, 2223, 2224, 2238, 2239, 2244, 2243, 2249,
		 2251, 2262, 2274, 2281, 2277, 2276, 2281, 2289,
		 2291, 2295, 2305, 2317, 2330, 2331, 2334, 2342,
		 2347, 2351, 2364, 2370, 2367, 2370, 2379, 2384,
		 2392, 2399, 2400, 2405, 2414, 2418, 2426, 2435,
		 2438, 2438, 2456, 2463, 2458, 2467, 2472, 2479,
		 2489, 2516, 2524, 2528, 2516, 2534, 2554, 2566,
		 2556, 2600, 2611, 2622, 2632, 2643, 2654, 2665,
		 2675, 2686, 2697, 2708, 2718, 2729, 2740, 2750,
		/* G */
		 1024, 1026, 1034, 1041, 1047, 1053, 1060, 1069,
		 1076, 1081, 1086, 1093, 1098, 1105, 1111, 1117,
		 1122, 1126, 1131, 1136, 1142, 1147, 1152, 1156,
		 1162, 1168, 1173, 1178, 1183, 1189, 1194, 1198,
		 1204, 1209, 1215, 1218, 1223, 1229, 1236, 1241,
		 1246, 1250, 1255, 1259, 1265, 1270, 1276, 1281,
		 1286, 1290, 1294, 1301, 1306, 1311, 1316, 1320,
		 1325, 1330, 1335, 1340, 1346, 1351, 1357, 1362,
		 1367, 1373, 1377, 1382, 1387, 1392, 1396, 1400,
		 1404, 1411, 1415, 1420, 1424, 1431, 1434, 1438,
		 1443, 1448, 1452, 1458, 1464, 1469, 1476, 1481,
		 1483, 1484, 1488, 1492, 1495, 1498, 1504, 1508,
		 1513, 1517, 1521, 1522, 1526, 1531, 1536, 1538,
		 1544, 1548, 1552, 1556, 1562, 1566, 1569, 1575,
		 1581, 1582, 1585, 1591, 1597, 1602, 1606, 1609,
		 1613, 1617, 1620, 1624, 1630, 1636, 1641, 1645,
		 1648, 1652, 1657, 1661, 1666, 1670, 1673, 1676,
		 1681, 1685, 1690, 1695, 1700, 1702, 1708, 1712,
		 1718, 1721, 1728, 1731, 1737, 1742, 1749, 1754,
		 1758, 1764, 1770, 1777, 1781, 1785, 1789, 1794,
		 1800, 1807, 1810, 1813, 1816, 1823, 1828, 1832,
		 1834, 1838, 1845, 1850, 1854, 1858, 1865, 1872,
		 1875, 1874, 1878, 1882, 1889, 1894, 1898, 1903,
		 1905, 1909, 1915, 1926, 1926, 1927, 1927, 1933,
		 1936, 1943, 1948, 1954, 1954, 1958, 1963, 1969,
		 1971, 1977, 1982, 1989, 1997, 1999, 2003, 2006,
		 2007, 2009, 2018, 2023, 2022, 2025, 2030, 2032,
		 2035, 2041, 2049, 2055, 2063, 2065, 2070, 2072,
		 2077, 2080, 2090, 2094, 2096, 2108, 2115, 2120,
		 2123, 2136, 2138, 2141, 2143, 2164, 2186, 2198,
		 2198, 2220, 2231, 2242, 2253, 2264, 2275, 2286,
		 2297, 2308, 2319, 2330, 2340, 2351, 2362, 2373,
		/* B */
		 1024, 1022, 1030, 1038, 1043, 1048, 1055, 1063,
		 1071, 1076, 1079, 1085, 1091, 1098, 1103, 1109,
		 1114, 1118, 1122, 1126, 1130, 1136, 1142, 1145,
		 1150, 1155, 1160, 1163, 1168, 1174, 1178, 1181,
		 1186, 1191, 1195, 1200, 1206, 1212, 1218, 1223,
		 1228, 1230, 1233, 1237, 1244, 1250, 1255, 1260,
		 1262, 1266, 1270, 1277, 1282, 1286, 1290, 1295,
		 1300, 1303, 1307, 1312, 1319, 1324, 1329, 1333,
		 1337, 1344, 1348, 1352, 1356, 1362, 1365, 1368,
		 1372, 1378, 1382, 1385, 1390, 1397, 1400, 1404,
		 1409, 1412, 1415, 1422, 1426, 1431, 1438, 1444,
		 1445, 1445, 1447, 1450, 1453, 1458, 1465, 1469,
		 1470, 1472, 1476, 1479, 1484, 1487, 1492, 1494,
		 1500, 1503, 1506, 1510, 1515, 1519, 1524, 1529,
		 1533, 1533, 1535, 1539, 1545, 1551, 1554, 1557,
		 1559, 1566, 1570, 1571, 1574, 1580, 1586, 1590,
		 1591, 1595, 1597, 1602, 1609, 1614, 1616, 1620,
		 1622, 1622, 1626, 1633, 1638, 1640, 1647, 1650,
		 1657, 1660, 1665, 1665, 1669, 1674, 1680, 1685,
		 1689, 1695, 1705, 1711, 1713, 1716, 1722, 1729,
		 1734, 1739, 1742, 1746, 1749, 1755, 1760, 1765,
		 1766, 1769, 1775, 1781, 1783, 1783, 1791, 1800,
		 1806, 1806, 1811, 1814, 1818, 1821, 1827, 1834,
		 1836, 1837, 1841, 1855, 1857, 1858, 1853, 1857,
		 1860, 1871, 1879, 1884, 1882, 1883, 1889, 1898,
		 1901, 1907, 1911, 1917, 1922, 1924, 1926, 1929,
		 1925, 1925, 1940, 1951, 1947, 1945, 1950, 1952,
		 1957, 1964, 1966, 1969, 1978, 1983, 1984, 1986,
		 1994, 1998, 2002, 2000, 2003, 2015, 2021, 2031,
		 2040, 2050, 2047, 2045, 2053, 2071, 2096, 2118,
		 2131, 2119, 2130, 2141, 2152, 2163, 2174, 2185,
		 2196, 2207, 2217, 2228, 2239, 2250, 2261, 2272
	},
	{
		/* lsc - 6 */
		/* R */
		 1024, 1029, 1039, 1048, 1059, 1071, 1081, 1091,
		 1099, 1111, 1120, 1132, 1142, 1153, 1162, 1172,
		 1180, 1190, 1200, 1210, 1218, 1224, 1232, 1242,
		 1253, 1262, 1272, 1281, 1288, 1297, 1306, 1315,
		 1325, 1334, 1343, 1352, 1360, 1368, 1376, 1385,
		 1394, 1402, 1412, 1420, 1430, 1441, 1449, 1458,
		 1468, 1477, 1486, 1493, 1502, 1511, 1520, 1530,
		 1540, 1549, 1554, 1563, 1572, 1584, 1591, 1601,
		 1611, 1622, 1628, 1636, 1645, 1656, 1664, 1672,
		 1680, 1689, 1698, 1707, 1715, 1724, 1732, 1743,
		 1751, 1758, 1765, 1773, 1784, 1795, 1805, 1814,
		 1821, 1825, 1831, 1841, 1849, 1853, 1859, 1866,
		 1875, 1881, 1888, 1893, 1904, 1913, 1922, 1928,
		 1937, 1942, 1950, 1956, 1966, 1974, 1984, 1988,
		 1997, 2004, 2011, 2015, 2027, 2035, 2042, 2046,
		 2053, 2058, 2067, 2075, 2082, 2089, 2101, 2108,
		 2112, 2116, 2123, 2130, 2139, 2147, 2155, 2164,
		 2168, 2171, 2177, 2188, 2195, 2201, 2210, 2217,
		 2224, 2231, 2241, 2250, 2257, 2264, 2273, 2282,
		 2289, 2299, 2305, 2312, 2317, 2327, 2335, 2345,
		 2352, 2365, 2371, 2376, 2382, 2387, 2396, 2401,
		 2411, 2417, 2427, 2432, 2442, 2447, 2453, 2461,
		 2465, 2466, 2474, 2481, 2489, 2497, 2504, 2513,
		 2520, 2528, 2530, 2534, 2537, 2549, 2556, 2557,
		 2558, 2570, 2577, 2582, 2585, 2590, 2596, 2600,
		 2602, 2608, 2616, 2621, 2621, 2626, 2633, 2640,
		 2642, 2650, 2659, 2665, 2667, 2673, 2674, 2676,
		 2678, 2688, 2694, 2703, 2698, 2703, 2701, 2714,
		 2724, 2743, 2743, 2755, 2764, 2761, 2758, 2766,
		 2781, 2793, 2798, 2800, 2814, 2822, 2829, 2825,
		 2846, 2888, 2899, 2909, 2920, 2931, 2942, 2953,
		 2964, 2975, 2985, 2996, 3007, 3018, 3028, 3039,
		/* G */
		 1024, 1024, 1033, 1043, 1052, 1061, 1070, 1078,
		 1086, 1095, 1102, 1109, 1117, 1126, 1133, 1140,
		 1146, 1153, 1161, 1169, 1175, 1179, 1183, 1190,
		 1199, 1206, 1214, 1220, 1226, 1231, 1238, 1243,
		 1250, 1258, 1264, 1269, 1275, 1281, 1287, 1294,
		 1302, 1306, 1313, 1319, 1325, 1332, 1339, 1344,
		 1350, 1356, 1365, 1369, 1375, 1378, 1385, 1393,
		 1400, 1407, 1411, 1420, 1426, 1434, 1437, 1445,
		 1452, 1459, 1462, 1468, 1474, 1480, 1484, 1491,
		 1496, 1502, 1508, 1516, 1520, 1526, 1531, 1538,
		 1543, 1547, 1550, 1557, 1567, 1574, 1579, 1585,
		 1590, 1593, 1595, 1600, 1606, 1612, 1614, 1618,
		 1622, 1627, 1631, 1635, 1643, 1648, 1652, 1654,
		 1661, 1665, 1669, 1670, 1676, 1684, 1692, 1694,
		 1701, 1706, 1710, 1710, 1719, 1726, 1732, 1734,
		 1739, 1742, 1748, 1753, 1759, 1763, 1771, 1776,
		 1780, 1783, 1786, 1791, 1798, 1805, 1810, 1816,
		 1817, 1819, 1824, 1832, 1838, 1845, 1851, 1853,
		 1857, 1863, 1871, 1875, 1879, 1884, 1893, 1902,
		 1907, 1913, 1918, 1925, 1928, 1934, 1940, 1946,
		 1951, 1956, 1961, 1966, 1973, 1978, 1983, 1985,
		 1992, 1994, 1998, 2002, 2014, 2022, 2024, 2027,
		 2030, 2034, 2043, 2049, 2050, 2056, 2063, 2068,
		 2069, 2077, 2081, 2083, 2081, 2088, 2095, 2102,
		 2106, 2113, 2113, 2117, 2121, 2127, 2132, 2140,
		 2143, 2147, 2151, 2152, 2147, 2152, 2161, 2173,
		 2178, 2184, 2184, 2186, 2188, 2195, 2199, 2207,
		 2210, 2213, 2212, 2217, 2219, 2236, 2239, 2247,
		 2243, 2251, 2255, 2274, 2288, 2297, 2302, 2296,
		 2294, 2311, 2326, 2331, 2328, 2335, 2362, 2369,
		 2370, 2387, 2398, 2409, 2420, 2431, 2442, 2453,
		 2464, 2476, 2487, 2498, 2509, 2520, 2531, 2542,
		/* B */
		 1024, 1024, 1037, 1046, 1056, 1067, 1077, 1083,
		 1090, 1105, 1115, 1123, 1127, 1138, 1145, 1152,
		 1156, 1166, 1177, 1183, 1186, 1189, 1194, 1204,
		 1214, 1220, 1227, 1235, 1239, 1244, 1252, 1258,
		 1264, 1271, 1277, 1281, 1287, 1294, 1301, 1311,
		 1319, 1323, 1329, 1333, 1337, 1344, 1354, 1356,
		 1360, 1366, 1375, 1378, 1384, 1387, 1394, 1400,
		 1406, 1413, 1419, 1430, 1439, 1444, 1444, 1451,
		 1461, 1466, 1468, 1473, 1478, 1484, 1489, 1494,
		 1501, 1508, 1516, 1523, 1523, 1526, 1531, 1539,
		 1546, 1551, 1552, 1559, 1576, 1585, 1589, 1592,
		 1596, 1600, 1603, 1610, 1615, 1620, 1625, 1629,
		 1631, 1635, 1641, 1645, 1652, 1657, 1663, 1667,
		 1676, 1674, 1678, 1685, 1695, 1701, 1705, 1703,
		 1709, 1715, 1724, 1723, 1731, 1737, 1745, 1747,
		 1754, 1758, 1765, 1767, 1773, 1780, 1793, 1798,
		 1800, 1799, 1799, 1799, 1806, 1817, 1824, 1829,
		 1828, 1833, 1837, 1846, 1850, 1858, 1863, 1861,
		 1867, 1872, 1879, 1880, 1883, 1887, 1900, 1912,
		 1915, 1919, 1926, 1929, 1926, 1928, 1934, 1943,
		 1954, 1965, 1972, 1981, 1990, 1987, 1988, 1992,
		 2002, 2000, 2000, 2007, 2022, 2034, 2036, 2039,
		 2040, 2046, 2059, 2061, 2060, 2072, 2087, 2092,
		 2091, 2105, 2112, 2104, 2092, 2097, 2105, 2121,
		 2135, 2148, 2138, 2138, 2136, 2144, 2155, 2176,
		 2185, 2191, 2188, 2183, 2169, 2177, 2196, 2221,
		 2220, 2227, 2230, 2222, 2213, 2216, 2224, 2229,
		 2228, 2238, 2249, 2277, 2274, 2275, 2258, 2282,
		 2279, 2291, 2282, 2308, 2322, 2320, 2301, 2291,
		 2322, 2378, 2401, 2377, 2357, 2365, 2447, 2439,
		 2398, 2416, 2427, 2438, 2449, 2460, 2471, 2482,
		 2493, 2504, 2515, 2526, 2537, 2548, 2559, 2570
	},
	{
		/* lsc - 7 */
		/* R */
		 1024, 1027, 1034, 1042, 1052, 1062, 1070, 1078,
		 1087, 1098, 1106, 1116, 1125, 1134, 1143, 1151,
		 1159, 1168, 1178, 1186, 1194, 1202, 1211, 1220,
		 1228, 1237, 1245, 1252, 1261, 1270, 1278, 1287,
		 1296, 1304, 1313, 1322, 1331, 1340, 1349, 1356,
		 1363, 1373, 1382, 1391, 1398, 1405, 1414, 1422,
		 1431, 1439, 1447, 1453, 1461, 1471, 1480, 1487,
		 1495, 1504, 1511, 1518, 1527, 1536, 1545, 1553,
		 1561, 1570, 1578, 1585, 1592, 1600, 1608, 1615,
		 1623, 1632, 1639, 1648, 1656, 1663, 1670, 1679,
		 1687, 1694, 1701, 1707, 1716, 1725, 1733, 1741,
		 1748, 1754, 1760, 1766, 1772, 1779, 1786, 1794,
		 1801, 1808, 1812, 1819, 1828, 1836, 1842, 1847,
		 1856, 1864, 1874, 1879, 1885, 1892, 1899, 1904,
		 1912, 1921, 1928, 1933, 1940, 1947, 1954, 1959,
		 1965, 1974, 1982, 1989, 1995, 2003, 2009, 2014,
		 2020, 2028, 2037, 2044, 2051, 2057, 2063, 2070,
		 2078, 2082, 2089, 2097, 2105, 2109, 2116, 2123,
		 2132, 2136, 2142, 2147, 2155, 2163, 2174, 2181,
		 2188, 2193, 2199, 2206, 2214, 2222, 2227, 2234,
		 2241, 2250, 2256, 2262, 2270, 2279, 2288, 2292,
		 2299, 2307, 2312, 2315, 2322, 2331, 2337, 2345,
		 2352, 2356, 2361, 2364, 2370, 2377, 2387, 2391,
		 2392, 2395, 2401, 2407, 2413, 2421, 2423, 2423,
		 2430, 2440, 2447, 2449, 2455, 2457, 2459, 2461,
		 2465, 2472, 2475, 2475, 2476, 2486, 2489, 2493,
		 2497, 2503, 2509, 2518, 2525, 2522, 2523, 2535,
		 2545, 2551, 2546, 2546, 2548, 2551, 2562, 2567,
		 2571, 2572, 2587, 2592, 2592, 2596, 2607, 2615,
		 2623, 2634, 2639, 2647, 2654, 2663, 2666, 2673,
		 2682, 2703, 2711, 2719, 2727, 2735, 2743, 2751,
		 2759, 2766, 2774, 2782, 2790, 2798, 2806, 2813,
		/* G */
		 1024, 1024, 1029, 1036, 1043, 1051, 1057, 1063,
		 1070, 1078, 1085, 1092, 1099, 1105, 1111, 1117,
		 1123, 1130, 1137, 1142, 1147, 1153, 1159, 1166,
		 1172, 1179, 1185, 1192, 1197, 1204, 1209, 1215,
		 1223, 1229, 1234, 1239, 1245, 1253, 1259, 1265,
		 1269, 1276, 1283, 1289, 1293, 1297, 1304, 1310,
		 1317, 1322, 1327, 1331, 1337, 1344, 1350, 1355,
		 1361, 1366, 1371, 1375, 1381, 1387, 1393, 1398,
		 1403, 1409, 1415, 1420, 1425, 1429, 1433, 1438,
		 1444, 1450, 1454, 1459, 1464, 1468, 1472, 1479,
		 1485, 1490, 1495, 1500, 1506, 1511, 1518, 1525,
		 1529, 1531, 1535, 1540, 1544, 1547, 1552, 1559,
		 1563, 1567, 1569, 1574, 1579, 1585, 1588, 1592,
		 1597, 1603, 1608, 1612, 1616, 1620, 1625, 1629,
		 1634, 1638, 1643, 1647, 1652, 1657, 1662, 1665,
		 1668, 1674, 1680, 1685, 1687, 1693, 1697, 1702,
		 1706, 1710, 1714, 1720, 1726, 1731, 1735, 1740,
		 1745, 1748, 1752, 1756, 1761, 1765, 1772, 1776,
		 1782, 1784, 1789, 1794, 1802, 1807, 1815, 1821,
		 1825, 1830, 1837, 1844, 1849, 1853, 1856, 1861,
		 1867, 1872, 1876, 1881, 1887, 1894, 1899, 1903,
		 1908, 1914, 1917, 1919, 1926, 1933, 1938, 1942,
		 1946, 1951, 1955, 1958, 1963, 1968, 1971, 1974,
		 1978, 1983, 1986, 1989, 1990, 1995, 2000, 2004,
		 2011, 2019, 2023, 2025, 2029, 2028, 2028, 2031,
		 2035, 2040, 2043, 2047, 2050, 2058, 2060, 2063,
		 2067, 2072, 2078, 2082, 2084, 2085, 2086, 2091,
		 2096, 2104, 2106, 2109, 2112, 2116, 2126, 2129,
		 2131, 2129, 2135, 2139, 2147, 2158, 2170, 2174,
		 2179, 2191, 2195, 2205, 2214, 2224, 2223, 2231,
		 2243, 2256, 2264, 2272, 2280, 2289, 2297, 2305,
		 2313, 2321, 2329, 2337, 2345, 2353, 2361, 2369,
		/* B */
		 1024, 1023, 1028, 1036, 1042, 1050, 1055, 1061,
		 1070, 1080, 1085, 1092, 1098, 1106, 1112, 1119,
		 1123, 1130, 1136, 1142, 1143, 1147, 1154, 1163,
		 1169, 1177, 1183, 1188, 1194, 1201, 1206, 1213,
		 1220, 1224, 1229, 1235, 1240, 1245, 1249, 1253,
		 1257, 1263, 1269, 1274, 1278, 1280, 1285, 1292,
		 1300, 1305, 1311, 1314, 1322, 1328, 1333, 1334,
		 1339, 1344, 1349, 1355, 1361, 1364, 1370, 1376,
		 1380, 1383, 1391, 1397, 1401, 1404, 1408, 1411,
		 1417, 1424, 1430, 1434, 1439, 1443, 1446, 1453,
		 1460, 1466, 1470, 1473, 1477, 1482, 1490, 1497,
		 1500, 1498, 1503, 1510, 1515, 1515, 1519, 1525,
		 1531, 1533, 1535, 1541, 1547, 1549, 1549, 1550,
		 1558, 1563, 1569, 1572, 1578, 1579, 1583, 1585,
		 1590, 1594, 1599, 1604, 1609, 1614, 1620, 1623,
		 1623, 1633, 1642, 1645, 1644, 1651, 1656, 1664,
		 1668, 1674, 1675, 1681, 1686, 1689, 1692, 1700,
		 1705, 1705, 1708, 1713, 1720, 1724, 1732, 1734,
		 1738, 1738, 1741, 1747, 1756, 1762, 1769, 1774,
		 1778, 1782, 1792, 1796, 1796, 1796, 1803, 1811,
		 1816, 1819, 1825, 1832, 1837, 1841, 1848, 1853,
		 1858, 1863, 1866, 1872, 1878, 1885, 1884, 1889,
		 1895, 1902, 1908, 1911, 1915, 1920, 1928, 1931,
		 1936, 1938, 1941, 1944, 1950, 1956, 1959, 1962,
		 1975, 1988, 1990, 1985, 1985, 1987, 1992, 1996,
		 1999, 2005, 2011, 2016, 2016, 2029, 2027, 2028,
		 2029, 2044, 2047, 2053, 2057, 2061, 2062, 2069,
		 2071, 2074, 2067, 2065, 2073, 2084, 2093, 2089,
		 2093, 2098, 2103, 2099, 2105, 2121, 2134, 2129,
		 2127, 2146, 2158, 2174, 2193, 2215, 2207, 2215,
		 2228, 2208, 2216, 2224, 2232, 2240, 2248, 2256,
		 2265, 2273, 2281, 2289, 2297, 2305, 2313, 2321
	},
	{
		/* lsc - 8 */
		/* R */
		 1024, 1027, 1032, 1038, 1042, 1048, 1053, 1059,
		 1066, 1071, 1075, 1080, 1087, 1093, 1097, 1102,
		 1108, 1114, 1119, 1125, 1130, 1135, 1141, 1145,
		 1150, 1156, 1163, 1167, 1171, 1176, 1181, 1187,
		 1192, 1197, 1203, 1209, 1214, 1219, 1224, 1229,
		 1235, 1241, 1246, 1250, 1255, 1261, 1268, 1273,
		 1277, 1281, 1286, 1291, 1297, 1303, 1308, 1311,
		 1315, 1321, 1327, 1332, 1336, 1342, 1348, 1354,
		 1358, 1364, 1370, 1375, 1380, 1384, 1389, 1394,
		 1399, 1403, 1408, 1413, 1420, 1425, 1429, 1434,
		 1439, 1443, 1448, 1453, 1459, 1463, 1469, 1475,
		 1478, 1480, 1483, 1488, 1491, 1496, 1500, 1503,
		 1505, 1511, 1516, 1518, 1520, 1527, 1536, 1542,
		 1545, 1544, 1547, 1552, 1560, 1564, 1567, 1571,
		 1577, 1581, 1585, 1590, 1597, 1599, 1604, 1606,
		 1609, 1613, 1620, 1625, 1631, 1636, 1641, 1643,
		 1646, 1648, 1655, 1664, 1669, 1672, 1675, 1680,
		 1681, 1686, 1692, 1699, 1703, 1706, 1709, 1711,
		 1719, 1727, 1733, 1736, 1738, 1742, 1751, 1759,
		 1764, 1766, 1772, 1778, 1785, 1790, 1793, 1800,
		 1807, 1813, 1817, 1820, 1828, 1836, 1840, 1840,
		 1847, 1855, 1863, 1869, 1876, 1878, 1880, 1884,
		 1891, 1893, 1901, 1907, 1912, 1914, 1922, 1928,
		 1931, 1933, 1937, 1945, 1954, 1963, 1964, 1962,
		 1967, 1974, 1981, 1983, 1986, 1987, 1994, 2003,
		 2008, 2011, 2014, 2017, 2016, 2023, 2030, 2039,
		 2040, 2039, 2046, 2055, 2063, 2068, 2073, 2068,
		 2067, 2077, 2087, 2086, 2091, 2098, 2111, 2114,
		 2119, 2119, 2124, 2124, 2133, 2145, 2156, 2149,
		 2165, 2175, 2195, 2197, 2207, 2200, 2222, 2229,
		 2230, 2240, 2248, 2257, 2265, 2273, 2281, 2289,
		 2297, 2305, 2313, 2321, 2329, 2337, 2346, 2354,
		/* G */
		 1024, 1027, 1034, 1040, 1045, 1050, 1056, 1063,
		 1070, 1075, 1079, 1085, 1091, 1098, 1101, 1106,
		 1112, 1118, 1122, 1128, 1134, 1139, 1144, 1147,
		 1153, 1158, 1163, 1168, 1173, 1176, 1181, 1186,
		 1192, 1196, 1201, 1207, 1212, 1215, 1220, 1225,
		 1231, 1236, 1240, 1244, 1248, 1253, 1258, 1262,
		 1267, 1272, 1276, 1280, 1285, 1291, 1295, 1298,
		 1302, 1308, 1314, 1317, 1321, 1326, 1331, 1336,
		 1340, 1344, 1350, 1356, 1359, 1364, 1369, 1373,
		 1376, 1380, 1385, 1390, 1395, 1399, 1403, 1407,
		 1412, 1416, 1421, 1425, 1431, 1436, 1442, 1447,
		 1450, 1451, 1454, 1459, 1463, 1467, 1470, 1473,
		 1475, 1480, 1485, 1489, 1493, 1499, 1504, 1508,
		 1511, 1513, 1515, 1519, 1526, 1530, 1533, 1537,
		 1541, 1544, 1548, 1553, 1559, 1562, 1566, 1568,
		 1571, 1576, 1583, 1586, 1590, 1594, 1599, 1602,
		 1605, 1608, 1613, 1620, 1624, 1627, 1629, 1635,
		 1638, 1641, 1645, 1651, 1655, 1658, 1662, 1665,
		 1672, 1678, 1682, 1684, 1688, 1694, 1702, 1707,
		 1711, 1715, 1721, 1726, 1730, 1735, 1740, 1745,
		 1748, 1752, 1757, 1759, 1765, 1771, 1777, 1778,
		 1783, 1786, 1793, 1798, 1806, 1808, 1811, 1813,
		 1819, 1820, 1827, 1832, 1838, 1841, 1845, 1849,
		 1854, 1857, 1857, 1860, 1865, 1871, 1877, 1881,
		 1885, 1886, 1888, 1891, 1897, 1901, 1905, 1911,
		 1915, 1918, 1921, 1926, 1929, 1936, 1939, 1943,
		 1944, 1944, 1950, 1952, 1954, 1958, 1966, 1967,
		 1966, 1971, 1978, 1984, 1989, 1995, 1999, 2001,
		 2007, 2012, 2013, 2014, 2024, 2037, 2048, 2045,
		 2054, 2055, 2066, 2069, 2081, 2076, 2095, 2109,
		 2124, 2128, 2136, 2145, 2153, 2161, 2169, 2177,
		 2185, 2194, 2202, 2210, 2218, 2226, 2234, 2242,
		/* B */
		 1024, 1026, 1032, 1036, 1038, 1041, 1048, 1054,
		 1059, 1063, 1068, 1074, 1079, 1083, 1084, 1090,
		 1096, 1102, 1106, 1111, 1115, 1119, 1123, 1129,
		 1134, 1136, 1140, 1143, 1149, 1151, 1154, 1159,
		 1165, 1169, 1172, 1177, 1182, 1184, 1188, 1195,
		 1201, 1204, 1208, 1212, 1216, 1221, 1226, 1228,
		 1233, 1236, 1241, 1243, 1248, 1253, 1256, 1258,
		 1262, 1269, 1272, 1275, 1277, 1282, 1285, 1291,
		 1294, 1297, 1300, 1306, 1309, 1314, 1317, 1321,
		 1322, 1326, 1331, 1337, 1341, 1344, 1347, 1350,
		 1352, 1356, 1362, 1368, 1373, 1375, 1382, 1385,
		 1388, 1388, 1391, 1396, 1400, 1402, 1404, 1409,
		 1412, 1414, 1419, 1423, 1425, 1431, 1438, 1440,
		 1441, 1442, 1445, 1449, 1455, 1458, 1460, 1466,
		 1470, 1471, 1473, 1479, 1485, 1489, 1493, 1493,
		 1495, 1499, 1507, 1511, 1514, 1516, 1520, 1522,
		 1525, 1528, 1533, 1538, 1540, 1543, 1547, 1555,
		 1560, 1561, 1563, 1566, 1571, 1573, 1580, 1582,
		 1588, 1595, 1600, 1602, 1604, 1608, 1614, 1619,
		 1622, 1626, 1633, 1637, 1640, 1644, 1651, 1657,
		 1661, 1666, 1672, 1673, 1679, 1688, 1694, 1690,
		 1693, 1698, 1703, 1706, 1718, 1726, 1729, 1725,
		 1731, 1731, 1739, 1746, 1756, 1757, 1760, 1766,
		 1770, 1768, 1765, 1767, 1773, 1781, 1788, 1790,
		 1791, 1794, 1799, 1800, 1800, 1800, 1807, 1818,
		 1825, 1829, 1831, 1837, 1836, 1842, 1849, 1856,
		 1855, 1851, 1857, 1863, 1869, 1874, 1878, 1873,
		 1873, 1879, 1881, 1885, 1896, 1902, 1906, 1908,
		 1913, 1917, 1923, 1927, 1938, 1949, 1958, 1960,
		 1981, 1982, 1987, 1978, 1996, 1999, 2022, 2012,
		 2013, 2048, 2057, 2065, 2073, 2081, 2089, 2098,
		 2106, 2114, 2122, 2130, 2138, 2147, 2155, 2163
	},
	{
		/* lsc - 9 */
		/* R */
		 1024, 1027, 1032, 1038, 1042, 1048, 1053, 1059,
		 1066, 1071, 1075, 1080, 1087, 1093, 1097, 1102,
		 1108, 1114, 1119, 1125, 1130, 1135, 1141, 1145,
		 1150, 1156, 1163, 1167, 1171, 1176, 1181, 1187,
		 1192, 1197, 1203, 1209, 1214, 1219, 1224, 1229,
		 1235, 1241, 1246, 1250, 1255, 1261, 1268, 1273,
		 1277, 1281, 1286, 1291, 1297, 1303, 1308, 1311,
		 1315, 1321, 1327, 1332, 1336, 1342, 1348, 1354,
		 1358, 1364, 1370, 1375, 1380, 1384, 1389, 1394,
		 1399, 1403, 1408, 1413, 1420, 1425, 1429, 1434,
		 1439, 1443, 1448, 1453, 1459, 1463, 1469, 1475,
		 1478, 1480, 1483, 1488, 1491, 1496, 1500, 1503,
		 1505, 1511, 1516, 1518, 1520, 1527, 1536, 1542,
		 1545, 1544, 1547, 1552, 1560, 1564, 1567, 1571,
		 1577, 1581, 1585, 1590, 1597, 1599, 1604, 1606,
		 1609, 1613, 1620, 1625, 1631, 1636, 1641, 1643,
		 1646, 1648, 1655, 1664, 1669, 1672, 1675, 1680,
		 1681, 1686, 1692, 1699, 1703, 1706, 1709, 1711,
		 1719, 1727, 1733, 1736, 1738, 1742, 1751, 1759,
		 1764, 1766, 1772, 1778, 1785, 1790, 1793, 1800,
		 1807, 1813, 1817, 1820, 1828, 1836, 1840, 1840,
		 1847, 1855, 1863, 1869, 1876, 1878, 1880, 1884,
		 1891, 1893, 1901, 1907, 1912, 1914, 1922, 1928,
		 1931, 1933, 1937, 1945, 1954, 1963, 1964, 1962,
		 1967, 1974, 1981, 1983, 1986, 1987, 1994, 2003,
		 2008, 2011, 2014, 2017, 2016, 2023, 2030, 2039,
		 2040, 2039, 2046, 2055, 2063, 2068, 2073, 2068,
		 2067, 2077, 2087, 2086, 2091, 2098, 2111, 2114,
		 2119, 2119, 2124, 2124, 2133, 2145, 2156, 2149,
		 2165, 2175, 2195, 2197, 2207, 2200, 2222, 2229,
		 2230, 2240, 2248, 2257, 2265, 2273, 2281, 2289,
		 2297, 2305, 2313, 2321, 2329, 2337, 2346, 2354,
		/* G */
		 1024, 1027, 1034, 1040, 1045, 1050, 1056, 1063,
		 1070, 1075, 1079, 1085, 1091, 1098, 1101, 1106,
		 1112, 1118, 1122, 1128, 1134, 1139, 1144, 1147,
		 1153, 1158, 1163, 1168, 1173, 1176, 1181, 1186,
		 1192, 1196, 1201, 1207, 1212, 1215, 1220, 1225,
		 1231, 1236, 1240, 1244, 1248, 1253, 1258, 1262,
		 1267, 1272, 1276, 1280, 1285, 1291, 1295, 1298,
		 1302, 1308, 1314, 1317, 1321, 1326, 1331, 1336,
		 1340, 1344, 1350, 1356, 1359, 1364, 1369, 1373,
		 1376, 1380, 1385, 1390, 1395, 1399, 1403, 1407,
		 1412, 1416, 1421, 1425, 1431, 1436, 1442, 1447,
		 1450, 1451, 1454, 1459, 1463, 1467, 1470, 1473,
		 1475, 1480, 1485, 1489, 1493, 1499, 1504, 1508,
		 1511, 1513, 1515, 1519, 1526, 1530, 1533, 1537,
		 1541, 1544, 1548, 1553, 1559, 1562, 1566, 1568,
		 1571, 1576, 1583, 1586, 1590, 1594, 1599, 1602,
		 1605, 1608, 1613, 1620, 1624, 1627, 1629, 1635,
		 1638, 1641, 1645, 1651, 1655, 1658, 1662, 1665,
		 1672, 1678, 1682, 1684, 1688, 1694, 1702, 1707,
		 1711, 1715, 1721, 1726, 1730, 1735, 1740, 1745,
		 1748, 1752, 1757, 1759, 1765, 1771, 1777, 1778,
		 1783, 1786, 1793, 1798, 1806, 1808, 1811, 1813,
		 1819, 1820, 1827, 1832, 1838, 1841, 1845, 1849,
		 1854, 1857, 1857, 1860, 1865, 1871, 1877, 1881,
		 1885, 1886, 1888, 1891, 1897, 1901, 1905, 1911,
		 1915, 1918, 1921, 1926, 1929, 1936, 1939, 1943,
		 1944, 1944, 1950, 1952, 1954, 1958, 1966, 1967,
		 1966, 1971, 1978, 1984, 1989, 1995, 1999, 2001,
		 2007, 2012, 2013, 2014, 2024, 2037, 2048, 2045,
		 2054, 2055, 2066, 2069, 2081, 2076, 2095, 2109,
		 2124, 2128, 2136, 2145, 2153, 2161, 2169, 2177,
		 2185, 2194, 2202, 2210, 2218, 2226, 2234, 2242,
		/* B */
		 1024, 1026, 1032, 1036, 1038, 1041, 1048, 1054,
		 1059, 1063, 1068, 1074, 1079, 1083, 1084, 1090,
		 1096, 1102, 1106, 1111, 1115, 1119, 1123, 1129,
		 1134, 1136, 1140, 1143, 1149, 1151, 1154, 1159,
		 1165, 1169, 1172, 1177, 1182, 1184, 1188, 1195,
		 1201, 1204, 1208, 1212, 1216, 1221, 1226, 1228,
		 1233, 1236, 1241, 1243, 1248, 1253, 1256, 1258,
		 1262, 1269, 1272, 1275, 1277, 1282, 1285, 1291,
		 1294, 1297, 1300, 1306, 1309, 1314, 1317, 1321,
		 1322, 1326, 1331, 1337, 1341, 1344, 1347, 1350,
		 1352, 1356, 1362, 1368, 1373, 1375, 1382, 1385,
		 1388, 1388, 1391, 1396, 1400, 1402, 1404, 1409,
		 1412, 1414, 1419, 1423, 1425, 1431, 1438, 1440,
		 1441, 1442, 1445, 1449, 1455, 1458, 1460, 1466,
		 1470, 1471, 1473, 1479, 1485, 1489, 1493, 1493,
		 1495, 1499, 1507, 1511, 1514, 1516, 1520, 1522,
		 1525, 1528, 1533, 1538, 1540, 1543, 1547, 1555,
		 1560, 1561, 1563, 1566, 1571, 1573, 1580, 1582,
		 1588, 1595, 1600, 1602, 1604, 1608, 1614, 1619,
		 1622, 1626, 1633, 1637, 1640, 1644, 1651, 1657,
		 1661, 1666, 1672, 1673, 1679, 1688, 1694, 1690,
		 1693, 1698, 1703, 1706, 1718, 1726, 1729, 1725,
		 1731, 1731, 1739, 1746, 1756, 1757, 1760, 1766,
		 1770, 1768, 1765, 1767, 1773, 1781, 1788, 1790,
		 1791, 1794, 1799, 1800, 1800, 1800, 1807, 1818,
		 1825, 1829, 1831, 1837, 1836, 1842, 1849, 1856,
		 1855, 1851, 1857, 1863, 1869, 1874, 1878, 1873,
		 1873, 1879, 1881, 1885, 1896, 1902, 1906, 1908,
		 1913, 1917, 1923, 1927, 1938, 1949, 1958, 1960,
		 1981, 1982, 1987, 1978, 1996, 1999, 2022, 2012,
		 2013, 2048, 2057, 2065, 2073, 2081, 2089, 2098,
		 2106, 2114, 2122, 2130, 2138, 2147, 2155, 2163
	},
	{
		/* lsc - 10 */
		/* R */
		 1024, 1026, 1035, 1045, 1053, 1061, 1070, 1080,
		 1089, 1097, 1103, 1111, 1120, 1130, 1138, 1146,
		 1153, 1160, 1167, 1173, 1181, 1189, 1195, 1200,
		 1208, 1218, 1225, 1230, 1237, 1246, 1253, 1258,
		 1264, 1272, 1280, 1287, 1294, 1303, 1311, 1316,
		 1322, 1329, 1337, 1343, 1350, 1357, 1364, 1372,
		 1378, 1384, 1390, 1399, 1407, 1414, 1420, 1427,
		 1434, 1440, 1446, 1453, 1462, 1470, 1476, 1484,
		 1490, 1500, 1505, 1509, 1515, 1524, 1531, 1535,
		 1540, 1549, 1558, 1563, 1568, 1579, 1584, 1590,
		 1596, 1604, 1609, 1616, 1624, 1632, 1639, 1643,
		 1646, 1651, 1659, 1663, 1668, 1674, 1682, 1686,
		 1692, 1699, 1704, 1707, 1712, 1719, 1725, 1729,
		 1738, 1743, 1749, 1754, 1761, 1766, 1769, 1776,
		 1786, 1792, 1796, 1802, 1810, 1816, 1819, 1823,
		 1829, 1837, 1841, 1844, 1852, 1861, 1868, 1870,
		 1875, 1883, 1889, 1893, 1899, 1901, 1905, 1912,
		 1921, 1926, 1929, 1937, 1943, 1950, 1959, 1963,
		 1971, 1974, 1981, 1982, 1988, 1997, 2007, 2016,
		 2019, 2026, 2033, 2041, 2047, 2051, 2056, 2064,
		 2073, 2081, 2082, 2086, 2095, 2108, 2115, 2117,
		 2114, 2121, 2136, 2147, 2149, 2149, 2158, 2168,
		 2178, 2178, 2179, 2181, 2191, 2197, 2204, 2214,
		 2221, 2223, 2224, 2238, 2239, 2244, 2243, 2249,
		 2251, 2262, 2274, 2281, 2277, 2276, 2281, 2289,
		 2291, 2295, 2305, 2317, 2330, 2331, 2334, 2342,
		 2347, 2351, 2364, 2370, 2367, 2370, 2379, 2384,
		 2392, 2399, 2400, 2405, 2414, 2418, 2426, 2435,
		 2438, 2438, 2456, 2463, 2458, 2467, 2472, 2479,
		 2489, 2516, 2524, 2528, 2516, 2534, 2554, 2566,
		 2556, 2600, 2611, 2622, 2632, 2643, 2654, 2665,
		 2675, 2686, 2697, 2708, 2718, 2729, 2740, 2750,
		/* G */
		 1024, 1026, 1034, 1041, 1047, 1053, 1060, 1069,
		 1076, 1081, 1086, 1093, 1098, 1105, 1111, 1117,
		 1122, 1126, 1131, 1136, 1142, 1147, 1152, 1156,
		 1162, 1168, 1173, 1178, 1183, 1189, 1194, 1198,
		 1204, 1209, 1215, 1218, 1223, 1229, 1236, 1241,
		 1246, 1250, 1255, 1259, 1265, 1270, 1276, 1281,
		 1286, 1290, 1294, 1301, 1306, 1311, 1316, 1320,
		 1325, 1330, 1335, 1340, 1346, 1351, 1357, 1362,
		 1367, 1373, 1377, 1382, 1387, 1392, 1396, 1400,
		 1404, 1411, 1415, 1420, 1424, 1431, 1434, 1438,
		 1443, 1448, 1452, 1458, 1464, 1469, 1476, 1481,
		 1483, 1484, 1488, 1492, 1495, 1498, 1504, 1508,
		 1513, 1517, 1521, 1522, 1526, 1531, 1536, 1538,
		 1544, 1548, 1552, 1556, 1562, 1566, 1569, 1575,
		 1581, 1582, 1585, 1591, 1597, 1602, 1606, 1609,
		 1613, 1617, 1620, 1624, 1630, 1636, 1641, 1645,
		 1648, 1652, 1657, 1661, 1666, 1670, 1673, 1676,
		 1681, 1685, 1690, 1695, 1700, 1702, 1708, 1712,
		 1718, 1721, 1728, 1731, 1737, 1742, 1749, 1754,
		 1758, 1764, 1770, 1777, 1781, 1785, 1789, 1794,
		 1800, 1807, 1810, 1813, 1816, 1823, 1828, 1832,
		 1834, 1838, 1845, 1850, 1854, 1858, 1865, 1872,
		 1875, 1874, 1878, 1882, 1889, 1894, 1898, 1903,
		 1905, 1909, 1915, 1926, 1926, 1927, 1927, 1933,
		 1936, 1943, 1948, 1954, 1954, 1958, 1963, 1969,
		 1971, 1977, 1982, 1989, 1997, 1999, 2003, 2006,
		 2007, 2009, 2018, 2023, 2022, 2025, 2030, 2032,
		 2035, 2041, 2049, 2055, 2063, 2065, 2070, 2072,
		 2077, 2080, 2090, 2094, 2096, 2108, 2115, 2120,
		 2123, 2136, 2138, 2141, 2143, 2164, 2186, 2198,
		 2198, 2220, 2231, 2242, 2253, 2264, 2275, 2286,
		 2297, 2308, 2319, 2330, 2340, 2351, 2362, 2373,
		/* B */
		 1024, 1022, 1030, 1038, 1043, 1048, 1055, 1063,
		 1071, 1076, 1079, 1085, 1091, 1098, 1103, 1109,
		 1114, 1118, 1122, 1126, 1130, 1136, 1142, 1145,
		 1150, 1155, 1160, 1163, 1168, 1174, 1178, 1181,
		 1186, 1191, 1195, 1200, 1206, 1212, 1218, 1223,
		 1228, 1230, 1233, 1237, 1244, 1250, 1255, 1260,
		 1262, 1266, 1270, 1277, 1282, 1286, 1290, 1295,
		 1300, 1303, 1307, 1312, 1319, 1324, 1329, 1333,
		 1337, 1344, 1348, 1352, 1356, 1362, 1365, 1368,
		 1372, 1378, 1382, 1385, 1390, 1397, 1400, 1404,
		 1409, 1412, 1415, 1422, 1426, 1431, 1438, 1444,
		 1445, 1445, 1447, 1450, 1453, 1458, 1465, 1469,
		 1470, 1472, 1476, 1479, 1484, 1487, 1492, 1494,
		 1500, 1503, 1506, 1510, 1515, 1519, 1524, 1529,
		 1533, 1533, 1535, 1539, 1545, 1551, 1554, 1557,
		 1559, 1566, 1570, 1571, 1574, 1580, 1586, 1590,
		 1591, 1595, 1597, 1602, 1609, 1614, 1616, 1620,
		 1622, 1622, 1626, 1633, 1638, 1640, 1647, 1650,
		 1657, 1660, 1665, 1665, 1669, 1674, 1680, 1685,
		 1689, 1695, 1705, 1711, 1713, 1716, 1722, 1729,
		 1734, 1739, 1742, 1746, 1749, 1755, 1760, 1765,
		 1766, 1769, 1775, 1781, 1783, 1783, 1791, 1800,
		 1806, 1806, 1811, 1814, 1818, 1821, 1827, 1834,
		 1836, 1837, 1841, 1855, 1857, 1858, 1853, 1857,
		 1860, 1871, 1879, 1884, 1882, 1883, 1889, 1898,
		 1901, 1907, 1911, 1917, 1922, 1924, 1926, 1929,
		 1925, 1925, 1940, 1951, 1947, 1945, 1950, 1952,
		 1957, 1964, 1966, 1969, 1978, 1983, 1984, 1986,
		 1994, 1998, 2002, 2000, 2003, 2015, 2021, 2031,
		 2040, 2050, 2047, 2045, 2053, 2071, 2096, 2118,
		 2131, 2119, 2130, 2141, 2152, 2163, 2174, 2185,
		 2196, 2207, 2217, 2228, 2239, 2250, 2261, 2272
	},
	{
		/* lsc - 11 */
		/* R */
		 1024, 1026, 1035, 1045, 1053, 1061, 1070, 1080,
		 1089, 1097, 1103, 1111, 1120, 1130, 1138, 1146,
		 1153, 1160, 1167, 1173, 1181, 1189, 1195, 1200,
		 1208, 1218, 1225, 1230, 1237, 1246, 1253, 1258,
		 1264, 1272, 1280, 1287, 1294, 1303, 1311, 1316,
		 1322, 1329, 1337, 1343, 1350, 1357, 1364, 1372,
		 1378, 1384, 1390, 1399, 1407, 1414, 1420, 1427,
		 1434, 1440, 1446, 1453, 1462, 1470, 1476, 1484,
		 1490, 1500, 1505, 1509, 1515, 1524, 1531, 1535,
		 1540, 1549, 1558, 1563, 1568, 1579, 1584, 1590,
		 1596, 1604, 1609, 1616, 1624, 1632, 1639, 1643,
		 1646, 1651, 1659, 1663, 1668, 1674, 1682, 1686,
		 1692, 1699, 1704, 1707, 1712, 1719, 1725, 1729,
		 1738, 1743, 1749, 1754, 1761, 1766, 1769, 1776,
		 1786, 1792, 1796, 1802, 1810, 1816, 1819, 1823,
		 1829, 1837, 1841, 1844, 1852, 1861, 1868, 1870,
		 1875, 1883, 1889, 1893, 1899, 1901, 1905, 1912,
		 1921, 1926, 1929, 1937, 1943, 1950, 1959, 1963,
		 1971, 1974, 1981, 1982, 1988, 1997, 2007, 2016,
		 2019, 2026, 2033, 2041, 2047, 2051, 2056, 2064,
		 2073, 2081, 2082, 2086, 2095, 2108, 2115, 2117,
		 2114, 2121, 2136, 2147, 2149, 2149, 2158, 2168,
		 2178, 2178, 2179, 2181, 2191, 2197, 2204, 2214,
		 2221, 2223, 2224, 2238, 2239, 2244, 2243, 2249,
		 2251, 2262, 2274, 2281, 2277, 2276, 2281, 2289,
		 2291, 2295, 2305, 2317, 2330, 2331, 2334, 2342,
		 2347, 2351, 2364, 2370, 2367, 2370, 2379, 2384,
		 2392, 2399, 2400, 2405, 2414, 2418, 2426, 2435,
		 2438, 2438, 2456, 2463, 2458, 2467, 2472, 2479,
		 2489, 2516, 2524, 2528, 2516, 2534, 2554, 2566,
		 2556, 2600, 2611, 2622, 2632, 2643, 2654, 2665,
		 2675, 2686, 2697, 2708, 2718, 2729, 2740, 2750,
		/* G */
		 1024, 1026, 1034, 1041, 1047, 1053, 1060, 1069,
		 1076, 1081, 1086, 1093, 1098, 1105, 1111, 1117,
		 1122, 1126, 1131, 1136, 1142, 1147, 1152, 1156,
		 1162, 1168, 1173, 1178, 1183, 1189, 1194, 1198,
		 1204, 1209, 1215, 1218, 1223, 1229, 1236, 1241,
		 1246, 1250, 1255, 1259, 1265, 1270, 1276, 1281,
		 1286, 1290, 1294, 1301, 1306, 1311, 1316, 1320,
		 1325, 1330, 1335, 1340, 1346, 1351, 1357, 1362,
		 1367, 1373, 1377, 1382, 1387, 1392, 1396, 1400,
		 1404, 1411, 1415, 1420, 1424, 1431, 1434, 1438,
		 1443, 1448, 1452, 1458, 1464, 1469, 1476, 1481,
		 1483, 1484, 1488, 1492, 1495, 1498, 1504, 1508,
		 1513, 1517, 1521, 1522, 1526, 1531, 1536, 1538,
		 1544, 1548, 1552, 1556, 1562, 1566, 1569, 1575,
		 1581, 1582, 1585, 1591, 1597, 1602, 1606, 1609,
		 1613, 1617, 1620, 1624, 1630, 1636, 1641, 1645,
		 1648, 1652, 1657, 1661, 1666, 1670, 1673, 1676,
		 1681, 1685, 1690, 1695, 1700, 1702, 1708, 1712,
		 1718, 1721, 1728, 1731, 1737, 1742, 1749, 1754,
		 1758, 1764, 1770, 1777, 1781, 1785, 1789, 1794,
		 1800, 1807, 1810, 1813, 1816, 1823, 1828, 1832,
		 1834, 1838, 1845, 1850, 1854, 1858, 1865, 1872,
		 1875, 1874, 1878, 1882, 1889, 1894, 1898, 1903,
		 1905, 1909, 1915, 1926, 1926, 1927, 1927, 1933,
		 1936, 1943, 1948, 1954, 1954, 1958, 1963, 1969,
		 1971, 1977, 1982, 1989, 1997, 1999, 2003, 2006,
		 2007, 2009, 2018, 2023, 2022, 2025, 2030, 2032,
		 2035, 2041, 2049, 2055, 2063, 2065, 2070, 2072,
		 2077, 2080, 2090, 2094, 2096, 2108, 2115, 2120,
		 2123, 2136, 2138, 2141, 2143, 2164, 2186, 2198,
		 2198, 2220, 2231, 2242, 2253, 2264, 2275, 2286,
		 2297, 2308, 2319, 2330, 2340, 2351, 2362, 2373,
		/* B */
		 1024, 1022, 1030, 1038, 1043, 1048, 1055, 1063,
		 1071, 1076, 1079, 1085, 1091, 1098, 1103, 1109,
		 1114, 1118, 1122, 1126, 1130, 1136, 1142, 1145,
		 1150, 1155, 1160, 1163, 1168, 1174, 1178, 1181,
		 1186, 1191, 1195, 1200, 1206, 1212, 1218, 1223,
		 1228, 1230, 1233, 1237, 1244, 1250, 1255, 1260,
		 1262, 1266, 1270, 1277, 1282, 1286, 1290, 1295,
		 1300, 1303, 1307, 1312, 1319, 1324, 1329, 1333,
		 1337, 1344, 1348, 1352, 1356, 1362, 1365, 1368,
		 1372, 1378, 1382, 1385, 1390, 1397, 1400, 1404,
		 1409, 1412, 1415, 1422, 1426, 1431, 1438, 1444,
		 1445, 1445, 1447, 1450, 1453, 1458, 1465, 1469,
		 1470, 1472, 1476, 1479, 1484, 1487, 1492, 1494,
		 1500, 1503, 1506, 1510, 1515, 1519, 1524, 1529,
		 1533, 1533, 1535, 1539, 1545, 1551, 1554, 1557,
		 1559, 1566, 1570, 1571, 1574, 1580, 1586, 1590,
		 1591, 1595, 1597, 1602, 1609, 1614, 1616, 1620,
		 1622, 1622, 1626, 1633, 1638, 1640, 1647, 1650,
		 1657, 1660, 1665, 1665, 1669, 1674, 1680, 1685,
		 1689, 1695, 1705, 1711, 1713, 1716, 1722, 1729,
		 1734, 1739, 1742, 1746, 1749, 1755, 1760, 1765,
		 1766, 1769, 1775, 1781, 1783, 1783, 1791, 1800,
		 1806, 1806, 1811, 1814, 1818, 1821, 1827, 1834,
		 1836, 1837, 1841, 1855, 1857, 1858, 1853, 1857,
		 1860, 1871, 1879, 1884, 1882, 1883, 1889, 1898,
		 1901, 1907, 1911, 1917, 1922, 1924, 1926, 1929,
		 1925, 1925, 1940, 1951, 1947, 1945, 1950, 1952,
		 1957, 1964, 1966, 1969, 1978, 1983, 1984, 1986,
		 1994, 1998, 2002, 2000, 2003, 2015, 2021, 2031,
		 2040, 2050, 2047, 2045, 2053, 2071, 2096, 2118,
		 2131, 2119, 2130, 2141, 2152, 2163, 2174, 2185,
		 2196, 2207, 2217, 2228, 2239, 2250, 2261, 2272
	}
	},
	.linear_tbl = {
		/* R */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1344, 1360, 1376, 1392,
		 1408, 1424, 1440, 1456, 1472, 1488, 1504, 1520,
		 1536, 1552, 1568, 1584, 1600, 1616, 1632, 1648,
		 1664, 1680, 1696, 1712, 1728, 1744, 1760, 1776,
		 1792, 1808, 1824, 1840, 1856, 1872, 1888, 1904,
		 1920, 1936, 1952, 1968, 1984, 2000, 2016, 2032,
		 2048, 2064, 2080, 2096, 2112, 2128, 2144, 2160,
		 2176, 2192, 2208, 2224, 2240, 2256, 2272, 2288,
		 2304, 2320, 2336, 2352, 2368, 2384, 2400, 2416,
		 2432, 2448, 2464, 2480, 2496, 2512, 2528, 2544,
		 2560, 2576, 2592, 2608, 2624, 2640, 2656, 2672,
		 2688, 2704, 2720, 2736, 2752, 2768, 2784, 2800,
		 2816, 2832, 2848, 2864, 2880, 2896, 2912, 2928,
		 2944, 2960, 2976, 2992, 3008, 3024, 3040, 3056,
		 3072, 3088, 3104, 3120, 3136, 3152, 3168, 3184,
		 3200, 3216, 3232, 3248, 3264, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* G */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080,
		/* B */
		    0,   16,   32,   48,   64,   80,   96,  112,
		  128,  144,  160,  176,  192,  208,  224,  240,
		  256,  272,  288,  304,  320,  336,  352,  368,
		  384,  400,  416,  432,  448,  464,  480,  496,
		  512,  528,  544,  560,  576,  592,  608,  624,
		  640,  656,  672,  688,  704,  720,  736,  752,
		  768,  784,  800,  816,  832,  848,  864,  880,
		  896,  912,  928,  944,  960,  976,  992, 1008,
		 1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136,
		 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
		 1280, 1296, 1312, 1328, 1343, 1359, 1375, 1391,
		 1407, 1423, 1439, 1455, 1471, 1487, 1503, 1520,
		 1536, 1552, 1569, 1585, 1602, 1619, 1635, 1651,
		 1668, 1684, 1700, 1716, 1732, 1747, 1762, 1777,
		 1792, 1806, 1820, 1834, 1848, 1862, 1877, 1891,
		 1906, 1921, 1937, 1953, 1970, 1988, 2007, 2027,
		 2048, 2070, 2093, 2117, 2142, 2167, 2192, 2218,
		 2243, 2268, 2292, 2315, 2338, 2359, 2380, 2398,
		 2415, 2430, 2444, 2455, 2466, 2475, 2484, 2492,
		 2499, 2506, 2512, 2519, 2526, 2533, 2541, 2550,
		 2560, 2571, 2583, 2596, 2610, 2625, 2641, 2657,
		 2674, 2691, 2709, 2726, 2744, 2762, 2780, 2798,
		 2816, 2833, 2850, 2867, 2884, 2900, 2916, 2932,
		 2948, 2963, 2979, 2995, 3010, 3025, 3041, 3056,
		 3072, 3088, 3103, 3119, 3135, 3151, 3167, 3183,
		 3199, 3215, 3231, 3247, 3263, 3280, 3296, 3312,
		 3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440,
		 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
		 3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696,
		 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
		 3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952,
		 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080
	},
	.disc_tbl = {
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0,
		    0,    0,    0,    0,    0,    0,    0,    0
	},
	.isp_cem_table = {
		0x40, 0x3F, 0x48, 0x3F, 0x40, 0x47, 0x48, 0x47,
		0x50, 0x3F, 0x58, 0x3F, 0x50, 0x47, 0x58, 0x47,
		0x60, 0x3F, 0x68, 0x3F, 0x60, 0x47, 0x68, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x4F, 0x48, 0x4F, 0x40, 0x57, 0x48, 0x57,
		0x50, 0x4F, 0x58, 0x4F, 0x50, 0x57, 0x58, 0x57,
		0x60, 0x4F, 0x68, 0x4F, 0x60, 0x57, 0x68, 0x57,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x5F, 0x48, 0x5F, 0x40, 0x67, 0x48, 0x67,
		0x50, 0x5F, 0x58, 0x5F, 0x50, 0x67, 0x58, 0x67,
		0x60, 0x5F, 0x68, 0x5F, 0x60, 0x67, 0x68, 0x67,
		0x70, 0x5F, 0x78, 0x60, 0x70, 0x67, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x6F, 0x48, 0x6F, 0x40, 0x77, 0x48, 0x77,
		0x50, 0x6F, 0x58, 0x6F, 0x50, 0x77, 0x58, 0x77,
		0x60, 0x6F, 0x68, 0x6F, 0x60, 0x77, 0x68, 0x77,
		0x70, 0x6F, 0x78, 0x6F, 0x70, 0x77, 0x78, 0x77,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x7F, 0x48, 0x7F, 0x3F, 0x87, 0x47, 0x87,
		0x50, 0x7F, 0x58, 0x7F, 0x4F, 0x87, 0x57, 0x87,
		0x60, 0x7F, 0x68, 0x7F, 0x5F, 0x87, 0x67, 0x87,
		0x70, 0x7F, 0x78, 0x7F, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x8F, 0x88, 0x8F, 0x80, 0x97, 0x88, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0x9F, 0x88, 0x9F, 0x80, 0xA7, 0x88, 0xA7,
		0x90, 0x9F, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xAF, 0x88, 0xAF, 0x80, 0xB7, 0x88, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x90, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0x35, 0x54, 0x36, 0x4B, 0x3D, 0x53, 0x3E,
		0x5C, 0x38, 0x62, 0x3A, 0x5B, 0x40, 0x63, 0x41,
		0x66, 0x3D, 0x69, 0x3F, 0x67, 0x44, 0x6B, 0x46,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x99, 0x40, 0x90, 0x48, 0x99, 0x48,
		0xA2, 0x41, 0xAC, 0x42, 0xA3, 0x4A, 0xAC, 0x4B,
		0xB5, 0x44, 0xBD, 0x45, 0xB4, 0x4C, 0xBC, 0x4C,
		0xC4, 0x45, 0x00, 0x00, 0xC3, 0x4D, 0x00, 0x00,
		0x49, 0x44, 0x51, 0x46, 0x48, 0x4C, 0x50, 0x4E,
		0x59, 0x47, 0x61, 0x49, 0x58, 0x4F, 0x60, 0x51,
		0x68, 0x4B, 0x6C, 0x4D, 0x67, 0x52, 0x6C, 0x52,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x9A, 0x51, 0x93, 0x52, 0x9F, 0x52,
		0xA3, 0x52, 0xAD, 0x52, 0xAB, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xBE, 0x52, 0xC0, 0x52, 0xCB, 0x52,
		0xC7, 0x52, 0x00, 0x00, 0xD3, 0x52, 0x00, 0x00,
		0x47, 0x54, 0x4E, 0x56, 0x45, 0x5C, 0x4D, 0x5E,
		0x56, 0x57, 0x5B, 0x55, 0x55, 0x5F, 0x5B, 0x5F,
		0x60, 0x52, 0x69, 0x52, 0x5B, 0x58, 0x60, 0x52,
		0x6E, 0x52, 0x74, 0x52, 0x6C, 0x52, 0x71, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x99, 0x52, 0xA9, 0x52, 0xA3, 0x52, 0xB6, 0x52,
		0xB6, 0x52, 0xC3, 0x52, 0xC7, 0x52, 0xD7, 0x52,
		0xD0, 0x52, 0xDA, 0x52, 0xE5, 0x52, 0xEB, 0x54,
		0xE4, 0x52, 0x00, 0x00, 0xEB, 0x59, 0x00, 0x00,
		0x44, 0x64, 0x4B, 0x65, 0x42, 0x6C, 0x4A, 0x6D,
		0x53, 0x67, 0x5B, 0x68, 0x52, 0x6F, 0x5A, 0x70,
		0x5B, 0x64, 0x5B, 0x5C, 0x5B, 0x6F, 0x5B, 0x6B,
		0x5F, 0x51, 0x6D, 0x4F, 0x5A, 0x63, 0x6D, 0x64,
		0x80, 0x53, 0x97, 0x56, 0x80, 0x6C, 0x95, 0x6D,
		0xAF, 0x57, 0xC5, 0x58, 0xA8, 0x6D, 0xB6, 0x6E,
		0xD7, 0x58, 0xDC, 0x5D, 0xD1, 0x6B, 0xDB, 0x6D,
		0xE5, 0x5F, 0xEB, 0x61, 0xE5, 0x6F, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x75, 0x48, 0x76, 0x3F, 0x81, 0x47, 0x82,
		0x50, 0x77, 0x58, 0x79, 0x4F, 0x83, 0x57, 0x85,
		0x5B, 0x79, 0x5B, 0x79, 0x5B, 0x87, 0x5B, 0x8B,
		0x5C, 0x79, 0x6C, 0x7C, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x8D, 0x80, 0x80, 0x8F, 0x92, 0x92,
		0xA5, 0x80, 0xB6, 0x80, 0xA3, 0x91, 0xB6, 0x92,
		0xD1, 0x80, 0xDB, 0x80, 0xD1, 0x94, 0xDB, 0x92,
		0xE5, 0x80, 0xEB, 0x80, 0xE5, 0x90, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x3F, 0x8D, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5B, 0x91, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x5F, 0xA0, 0x6D, 0xA6, 0x5E, 0xB2, 0x71, 0xB6,
		0x83, 0xA7, 0x92, 0xA4, 0x86, 0xC7, 0x97, 0xBD,
		0xA6, 0xA6, 0xBC, 0xA8, 0xA5, 0xB8, 0xB4, 0xB4,
		0xCC, 0xA6, 0xD8, 0xA3, 0xC4, 0xB3, 0xD1, 0xB0,
		0xE5, 0xA1, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBE,
		0x5E, 0xC9, 0x70, 0xD6, 0x5D, 0xE2, 0x74, 0xD9,
		0x87, 0xD9, 0x95, 0xC6, 0x87, 0xD6, 0x94, 0xCF,
		0xA3, 0xC3, 0xB0, 0xC0, 0xA0, 0xCA, 0xAA, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB7, 0x5B, 0xCE, 0x5B, 0xC2, 0x5B, 0xDE,
		0x65, 0xE2, 0x77, 0xDD, 0x69, 0xE4, 0x79, 0xDC,
		0x87, 0xD6, 0x92, 0xD0, 0x87, 0xD6, 0x90, 0xD1,
		0x9C, 0xCB, 0xA5, 0xC7, 0x99, 0xCD, 0xA1, 0xC9,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA9, 0xC5, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xCD, 0x5D, 0xEA, 0x00, 0x00, 0x00, 0x00,
		0x6D, 0xE2, 0x7B, 0xDB, 0x00, 0x00, 0x00, 0x00,
		0x87, 0xD6, 0x8F, 0xD2, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xCE, 0x9F, 0xCA, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC7, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x55, 0x24, 0x5C, 0x24, 0x4D, 0x24, 0x55, 0x24,
		0x64, 0x24, 0x66, 0x24, 0x5D, 0x24, 0x66, 0x24,
		0x64, 0x24, 0x63, 0x24, 0x67, 0x24, 0x64, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8C, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x98, 0x24, 0xA5, 0x24, 0x9C, 0x24, 0xAD, 0x24,
		0xB7, 0x24, 0xC7, 0x29, 0xC3, 0x24, 0xC7, 0x37,
		0xC7, 0x3C, 0xC7, 0x45, 0xC7, 0x44, 0xC7, 0x4C,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x44, 0x24, 0x4C, 0x24, 0x38, 0x24, 0x41, 0x24,
		0x55, 0x24, 0x5F, 0x24, 0x4A, 0x24, 0x55, 0x24,
		0x67, 0x24, 0x64, 0x24, 0x60, 0x22, 0x66, 0x21,
		0x63, 0x24, 0x70, 0x24, 0x64, 0x22, 0x6D, 0x23,
		0x80, 0x24, 0x90, 0x24, 0x80, 0x25, 0x93, 0x26,
		0xA0, 0x24, 0xB7, 0x24, 0xA8, 0x27, 0xC4, 0x28,
		0xC7, 0x31, 0xC7, 0x42, 0xC4, 0x43, 0xC7, 0x4C,
		0xC7, 0x4C, 0xC7, 0x53, 0xC7, 0x54, 0xC7, 0x5A,
		0xC7, 0x58, 0x00, 0x00, 0xC7, 0x5D, 0x00, 0x00,
		0x37, 0x33, 0x37, 0x2A, 0x37, 0x42, 0x37, 0x3C,
		0x3C, 0x24, 0x48, 0x24, 0x37, 0x33, 0x37, 0x26,
		0x54, 0x22, 0x62, 0x1D, 0x42, 0x21, 0x53, 0x20,
		0x63, 0x1E, 0x68, 0x21, 0x6C, 0x35, 0x69, 0x36,
		0x80, 0x27, 0x96, 0x2A, 0x80, 0x3D, 0x96, 0x3F,
		0xB2, 0x2C, 0xBF, 0x44, 0xB0, 0x4A, 0xBC, 0x53,
		0xC4, 0x4E, 0xC7, 0x56, 0xC4, 0x5A, 0xC7, 0x5E,
		0xC7, 0x5C, 0xC7, 0x60, 0xC7, 0x62, 0xC7, 0x65,
		0xC7, 0x62, 0x00, 0x00, 0xC7, 0x67, 0x00, 0x00,
		0x37, 0x4F, 0x37, 0x4B, 0x37, 0x5B, 0x37, 0x5A,
		0x37, 0x46, 0x37, 0x3F, 0x37, 0x57, 0x37, 0x54,
		0x35, 0x31, 0x39, 0x1F, 0x36, 0x4E, 0x34, 0x44,
		0x66, 0x48, 0x73, 0x56, 0x54, 0x52, 0x6F, 0x62,
		0x80, 0x58, 0x96, 0x5A, 0x80, 0x70, 0x93, 0x70,
		0xA5, 0x64, 0xBC, 0x62, 0xA4, 0x71, 0xBA, 0x6D,
		0xC3, 0x64, 0xC7, 0x66, 0xC3, 0x6F, 0xC7, 0x71,
		0xC7, 0x69, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x7B, 0x37, 0x7D,
		0x37, 0x68, 0x37, 0x68, 0x37, 0x80, 0x37, 0x85,
		0x39, 0x68, 0x3F, 0x6A, 0x3E, 0x8B, 0x4D, 0x90,
		0x58, 0x73, 0x6B, 0x7B, 0x5E, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x92, 0x80, 0x81, 0x8F, 0x92, 0x92,
		0xA4, 0x80, 0xBA, 0x80, 0xA4, 0x92, 0xB9, 0x93,
		0xC3, 0x80, 0xC7, 0x80, 0xC3, 0x90, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x8D, 0x37, 0x92, 0x37, 0x9A, 0x37, 0x9E,
		0x37, 0x97, 0x37, 0x9C, 0x37, 0xA3, 0x37, 0xAB,
		0x3F, 0xA0, 0x4D, 0xA1, 0x3F, 0xB0, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA3, 0x5D, 0xB6, 0x72, 0xBC,
		0x86, 0xA9, 0x93, 0xA4, 0x8B, 0xC8, 0x98, 0xBC,
		0xA4, 0xA4, 0xBA, 0xA6, 0xA6, 0xB8, 0xB9, 0xB9,
		0xC3, 0xA1, 0xC7, 0x9C, 0xC3, 0xB2, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA3, 0x37, 0xA8, 0x37, 0xAC, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x3E, 0xC1, 0x4A, 0xC6, 0x3E, 0xD2, 0x51, 0xD3,
		0x5F, 0xCB, 0x75, 0xD4, 0x66, 0xD5, 0x79, 0xD8,
		0x8F, 0xDF, 0x9D, 0xD2, 0x8F, 0xDB, 0x9B, 0xD7,
		0xAA, 0xCD, 0xB8, 0xCB, 0xA8, 0xD5, 0xB4, 0xD4,
		0xC3, 0xC3, 0xC7, 0xB9, 0xC3, 0xD3, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x40, 0xDA,
		0x45, 0xDA, 0x59, 0xDA, 0x51, 0xDA, 0x61, 0xDA,
		0x6C, 0xDA, 0x7D, 0xDA, 0x71, 0xDA, 0x7F, 0xDA,
		0x8E, 0xDA, 0x99, 0xDA, 0x8E, 0xDA, 0x98, 0xDA,
		0xA5, 0xDA, 0xB1, 0xDA, 0xA2, 0xDA, 0xAC, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB6, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x49, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x58, 0xDA, 0x67, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x74, 0xDA, 0x81, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xDA, 0x96, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x9F, 0xDA, 0xA8, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xDA, 0xBA, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x37,
		0x68, 0x33, 0x6A, 0x32, 0x63, 0x35, 0x6A, 0x32,
		0x69, 0x32, 0x68, 0x33, 0x6A, 0x32, 0x69, 0x32,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8C, 0x21, 0x80, 0x27, 0x8E, 0x20,
		0x9B, 0x1A, 0xA3, 0x29, 0x9F, 0x18, 0xA3, 0x38,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3C, 0x58, 0x3A, 0x4D, 0x3F, 0x52, 0x3D,
		0x5E, 0x37, 0x64, 0x34, 0x57, 0x3B, 0x5E, 0x37,
		0x6B, 0x31, 0x69, 0x32, 0x66, 0x34, 0x6A, 0x32,
		0x68, 0x33, 0x72, 0x2E, 0x69, 0x32, 0x6F, 0x2F,
		0x80, 0x27, 0x91, 0x1F, 0x80, 0x2C, 0x92, 0x29,
		0xA1, 0x22, 0xA3, 0x45, 0xA1, 0x37, 0xA3, 0x52,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x46, 0x43, 0x4A, 0x41, 0x3E, 0x47, 0x41, 0x45,
		0x50, 0x3E, 0x56, 0x3B, 0x46, 0x43, 0x4D, 0x41,
		0x5E, 0x37, 0x68, 0x33, 0x56, 0x3F, 0x61, 0x3F,
		0x6A, 0x36, 0x6E, 0x39, 0x6D, 0x3B, 0x6C, 0x42,
		0x80, 0x36, 0x94, 0x33, 0x80, 0x45, 0x91, 0x4D,
		0xA1, 0x49, 0xA3, 0x5E, 0xA0, 0x5B, 0xA3, 0x66,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x33, 0x4C, 0x36, 0x4B, 0x26, 0x52, 0x28, 0x51,
		0x3E, 0x4B, 0x46, 0x4C, 0x30, 0x53, 0x3A, 0x55,
		0x4D, 0x4B, 0x58, 0x4A, 0x45, 0x58, 0x5C, 0x63,
		0x68, 0x4E, 0x73, 0x57, 0x68, 0x67, 0x73, 0x69,
		0x80, 0x5C, 0x91, 0x63, 0x80, 0x6F, 0x91, 0x73,
		0xA0, 0x68, 0xA3, 0x6E, 0xA0, 0x72, 0xA3, 0x74,
		0xA3, 0x71, 0xA8, 0x71, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x5C, 0x13, 0x5C, 0x13, 0x78, 0x13, 0x7C,
		0x1B, 0x5E, 0x26, 0x62, 0x1D, 0x80, 0x2B, 0x86,
		0x32, 0x66, 0x4B, 0x6E, 0x3B, 0x8C, 0x4F, 0x8F,
		0x5B, 0x75, 0x72, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x91, 0x80, 0x82, 0x92, 0x91, 0x91,
		0xA0, 0x80, 0xA3, 0x80, 0xA0, 0x90, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x93, 0x13, 0x9B, 0x13, 0xA7, 0x14, 0xAC,
		0x1F, 0x9F, 0x2E, 0x9F, 0x24, 0xAC, 0x34, 0xAC,
		0x3D, 0xA0, 0x4E, 0xA0, 0x44, 0xAC, 0x53, 0xAC,
		0x5E, 0xA1, 0x71, 0xA2, 0x63, 0xAC, 0x76, 0xAC,
		0x86, 0xA6, 0x93, 0xA2, 0x87, 0xAC, 0x92, 0xAC,
		0xA0, 0xA0, 0xA3, 0x97, 0x9E, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x24, 0xAC, 0x30, 0xAC, 0x37, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x67, 0xAC,
		0x6C, 0xAC, 0x7A, 0xAC, 0x72, 0xAC, 0x7C, 0xAC,
		0x87, 0xAC, 0x8F, 0xAC, 0x87, 0xAC, 0x8D, 0xAC,
		0x98, 0xAC, 0xA1, 0xAC, 0x95, 0xAC, 0x9C, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x91, 0x50, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x71, 0x61,
		0x75, 0x5D, 0x78, 0x60, 0x78, 0x64, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x5D, 0x6A,
		0x67, 0x66, 0x6E, 0x68, 0x64, 0x6D, 0x6C, 0x70,
		0x76, 0x6B, 0x7A, 0x6E, 0x73, 0x73, 0x7B, 0x75,
		0x80, 0x70, 0x89, 0x70, 0x80, 0x78, 0x89, 0x79,
		0x92, 0x72, 0x99, 0x73, 0x90, 0x79, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x3F, 0x7B, 0x47, 0x7E,
		0x52, 0x70, 0x5A, 0x73, 0x4F, 0x80, 0x57, 0x83,
		0x61, 0x75, 0x69, 0x78, 0x5F, 0x85, 0x67, 0x87,
		0x70, 0x7A, 0x78, 0x7D, 0x6F, 0x87, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x81, 0x87, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x3F, 0x8B, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x7A, 0x98,
		0x82, 0x8F, 0x88, 0x8F, 0x83, 0x97, 0x89, 0x97,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x45, 0x3B, 0x4C, 0x3B, 0x44, 0x43, 0x4C, 0x43,
		0x54, 0x3C, 0x5C, 0x3D, 0x54, 0x44, 0x5C, 0x45,
		0x62, 0x3E, 0x68, 0x3F, 0x63, 0x46, 0x69, 0x47,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA1, 0x40, 0xA9, 0x41, 0xA1, 0x48, 0xA9, 0x49,
		0xB2, 0x41, 0xBA, 0x42, 0xB2, 0x49, 0xB9, 0x49,
		0xC1, 0x42, 0x00, 0x00, 0xC1, 0x4A, 0x00, 0x00,
		0x43, 0x4B, 0x4B, 0x4B, 0x43, 0x53, 0x4B, 0x53,
		0x53, 0x4C, 0x5B, 0x4D, 0x53, 0x54, 0x5B, 0x55,
		0x63, 0x4D, 0x69, 0x4F, 0x63, 0x55, 0x6A, 0x56,
		0x70, 0x4F, 0x78, 0x50, 0x70, 0x57, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x99, 0x58,
		0xA1, 0x51, 0xA9, 0x51, 0xA1, 0x59, 0xA9, 0x59,
		0xB1, 0x51, 0xB9, 0x51, 0xB1, 0x59, 0xB9, 0x59,
		0xC0, 0x51, 0x00, 0x00, 0xC0, 0x59, 0x00, 0x00,
		0x42, 0x5B, 0x4A, 0x5B, 0x41, 0x63, 0x49, 0x63,
		0x52, 0x5C, 0x5A, 0x5D, 0x51, 0x64, 0x59, 0x65,
		0x62, 0x5D, 0x6A, 0x5E, 0x61, 0x65, 0x69, 0x66,
		0x71, 0x5F, 0x78, 0x60, 0x71, 0x66, 0x78, 0x67,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x99, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA1, 0x61, 0xA9, 0x61, 0xA0, 0x69, 0xA8, 0x68,
		0xB0, 0x61, 0xB8, 0x61, 0xB0, 0x68, 0xB8, 0x68,
		0xC0, 0x61, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x41, 0x6B, 0x49, 0x6B, 0x40, 0x73, 0x48, 0x73,
		0x51, 0x6C, 0x59, 0x6D, 0x50, 0x74, 0x58, 0x75,
		0x61, 0x6D, 0x69, 0x6E, 0x60, 0x75, 0x68, 0x76,
		0x6D, 0x6A, 0x75, 0x69, 0x6D, 0x75, 0x6E, 0x6B,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x6B, 0x94, 0x6C,
		0x98, 0x69, 0xA4, 0x69, 0xA7, 0x6D, 0xB8, 0x6D,
		0xAF, 0x69, 0xBA, 0x69, 0xC9, 0x6D, 0xDC, 0x6D,
		0xC5, 0x69, 0xCF, 0x69, 0xE8, 0x6E, 0xF5, 0x6F,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x7B, 0x48, 0x7C, 0x3F, 0x85, 0x47, 0x85,
		0x50, 0x7C, 0x58, 0x7D, 0x4F, 0x86, 0x57, 0x86,
		0x60, 0x7D, 0x68, 0x7E, 0x5F, 0x87, 0x67, 0x87,
		0x6D, 0x7E, 0x6E, 0x7E, 0x6D, 0x89, 0x6F, 0x90,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x93, 0x92, 0x92,
		0xA3, 0x80, 0xB7, 0x80, 0xA4, 0x92, 0xB2, 0x90,
		0xCA, 0x80, 0xD4, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x3F, 0x8F, 0x47, 0x8F, 0x3F, 0x97, 0x47, 0x97,
		0x4F, 0x8F, 0x57, 0x8F, 0x4F, 0x97, 0x57, 0x97,
		0x5F, 0x8F, 0x67, 0x8F, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x6F, 0xA2, 0x6D, 0x9B, 0x70, 0xB1,
		0x81, 0xAA, 0x91, 0xA1, 0x81, 0xAC, 0x8D, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA5, 0x74, 0xB2, 0x6D, 0xAF, 0x77, 0xB0,
		0x81, 0xAC, 0x8A, 0xA7, 0x81, 0xAC, 0x89, 0xA8,
		0x92, 0xA3, 0x98, 0xA0, 0x90, 0xA7, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6F, 0xB4, 0x79, 0xB0, 0x71, 0xB8, 0x79, 0xB8,
		0x81, 0xAF, 0x89, 0xAF, 0x81, 0xB7, 0x89, 0xB7,
		0x90, 0xAF, 0x98, 0xAF, 0x91, 0xB7, 0x98, 0xB7,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA0, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x69, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x71, 0xBF, 0x79, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x82, 0xBF, 0x89, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x54, 0x30, 0x5B, 0x33, 0x51, 0x38, 0x59, 0x3A,
		0x63, 0x35, 0x67, 0x38, 0x60, 0x3B, 0x67, 0x3B,
		0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B, 0x69, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x8A, 0x3B,
		0x92, 0x3B, 0x9B, 0x3B, 0x94, 0x3B, 0xA1, 0x3B,
		0xA8, 0x3B, 0xB5, 0x3B, 0xB0, 0x3B, 0xBF, 0x3B,
		0xC4, 0x3B, 0xCE, 0x3B, 0xCC, 0x3B, 0xD8, 0x3B,
		0xD7, 0x3B, 0x00, 0x00, 0xD9, 0x42, 0x00, 0x00,
		0x4C, 0x3B, 0x52, 0x3B, 0x49, 0x43, 0x49, 0x3B,
		0x5A, 0x3B, 0x61, 0x3B, 0x51, 0x3B, 0x5A, 0x3B,
		0x68, 0x3B, 0x69, 0x3B, 0x63, 0x3B, 0x69, 0x3B,
		0x6A, 0x3B, 0x74, 0x3B, 0x6A, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8E, 0x3B,
		0x98, 0x3B, 0xA8, 0x3B, 0x9E, 0x3B, 0xB2, 0x3B,
		0xBA, 0x3B, 0xCA, 0x3B, 0xC8, 0x3B, 0xD8, 0x3B,
		0xD8, 0x3B, 0xD9, 0x44, 0xD9, 0x45, 0xD9, 0x4E,
		0xD9, 0x4B, 0x00, 0x00, 0xD9, 0x52, 0x00, 0x00,
		0x49, 0x4E, 0x49, 0x48, 0x49, 0x57, 0x49, 0x53,
		0x49, 0x40, 0x4F, 0x3B, 0x49, 0x4E, 0x49, 0x45,
		0x5A, 0x3B, 0x65, 0x3A, 0x4C, 0x3B, 0x58, 0x39,
		0x68, 0x3B, 0x6E, 0x3B, 0x67, 0x36, 0x69, 0x37,
		0x80, 0x3C, 0x91, 0x3C, 0x80, 0x3D, 0x96, 0x40,
		0xA7, 0x3D, 0xC2, 0x3D, 0xB4, 0x42, 0xCD, 0x43,
		0xD6, 0x3D, 0xD7, 0x49, 0xCE, 0x52, 0xD4, 0x56,
		0xD9, 0x50, 0xD9, 0x55, 0xD9, 0x59, 0xD9, 0x5D,
		0xD9, 0x59, 0x00, 0x00, 0xD9, 0x60, 0x00, 0x00,
		0x46, 0x5F, 0x49, 0x5E, 0x44, 0x67, 0x49, 0x68,
		0x49, 0x5A, 0x49, 0x55, 0x49, 0x66, 0x49, 0x64,
		0x49, 0x4E, 0x48, 0x3E, 0x49, 0x60, 0x48, 0x59,
		0x58, 0x39, 0x71, 0x53, 0x48, 0x4C, 0x6F, 0x62,
		0x80, 0x57, 0x96, 0x58, 0x80, 0x6A, 0x97, 0x6C,
		0xB1, 0x5A, 0xBC, 0x60, 0xA4, 0x70, 0xBB, 0x6D,
		0xCD, 0x5F, 0xD4, 0x61, 0xCC, 0x6C, 0xD4, 0x6F,
		0xD9, 0x63, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x42, 0x6F, 0x49, 0x71, 0x3F, 0x7E, 0x47, 0x80,
		0x49, 0x71, 0x49, 0x71, 0x49, 0x82, 0x49, 0x85,
		0x49, 0x71, 0x4B, 0x72, 0x49, 0x8A, 0x4F, 0x8F,
		0x51, 0x73, 0x6B, 0x7A, 0x5D, 0x90, 0x6E, 0x91,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8A, 0x8F, 0x8F,
		0xA3, 0x80, 0xBB, 0x80, 0xA3, 0x91, 0xBA, 0x93,
		0xCC, 0x80, 0xD4, 0x80, 0xCB, 0x92, 0xD4, 0x90,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x3F, 0x8C, 0x47, 0x8E, 0x3F, 0x97, 0x47, 0x97,
		0x49, 0x91, 0x49, 0x95, 0x49, 0x9A, 0x49, 0xA0,
		0x49, 0x9A, 0x4F, 0xA0, 0x49, 0xA8, 0x4E, 0xB1,
		0x5C, 0xA3, 0x6E, 0xA7, 0x5A, 0xBA, 0x72, 0xB7,
		0x85, 0xAD, 0x93, 0xA5, 0x88, 0xBD, 0x95, 0xB6,
		0xA3, 0xA3, 0xBA, 0xA7, 0xA4, 0xB6, 0xBA, 0xBA,
		0xCB, 0xA5, 0xD4, 0xA1, 0xCC, 0xB9, 0xD4, 0xB2,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x4E, 0xC2, 0x49, 0xC3, 0x4D, 0xD9,
		0x5A, 0xD3, 0x72, 0xDA, 0x60, 0xE2, 0x75, 0xEB,
		0x8D, 0xE9, 0x9D, 0xD8, 0x8F, 0xF7, 0x9E, 0xE9,
		0xAB, 0xD0, 0xB9, 0xCC, 0xAB, 0xE0, 0xB6, 0xD8,
		0xCB, 0xCB, 0xD4, 0xC3, 0xC5, 0xD6, 0xD4, 0xD4,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCC,
		0x49, 0xD3, 0x50, 0xEA, 0x49, 0xE6, 0x57, 0xF0,
		0x66, 0xEA, 0x79, 0xEF, 0x6A, 0xF1, 0x7C, 0xF1,
		0x8F, 0xF4, 0x9C, 0xEE, 0x8E, 0xF2, 0x9B, 0xF1,
		0xAA, 0xE9, 0xB6, 0xE4, 0xA8, 0xF1, 0xB4, 0xED,
		0xC1, 0xE2, 0xCF, 0xDF, 0xBE, 0xE8, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD9, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0xF1, 0x5E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x6E, 0xF1, 0x7E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x8E, 0xF1, 0x99, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xF1, 0xB0, 0xEF, 0x00, 0x00, 0x00, 0x00,
		0xB9, 0xEA, 0xC1, 0xE6, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x4D, 0x13, 0x54, 0x0F, 0x46, 0x16, 0x4D, 0x13,
		0x5D, 0x0D, 0x60, 0x0D, 0x55, 0x0F, 0x60, 0x0D,
		0x5E, 0x0D, 0x5C, 0x0D, 0x60, 0x0D, 0x5E, 0x0D,
		0x63, 0x0D, 0x71, 0x0D, 0x5F, 0x0D, 0x6F, 0x0E,
		0x80, 0x0D, 0x8F, 0x0D, 0x80, 0x0E, 0x91, 0x0E,
		0x9E, 0x0D, 0xAE, 0x0D, 0xA2, 0x0E, 0xB5, 0x15,
		0xB5, 0x27, 0xB5, 0x3F, 0xB5, 0x37, 0xB5, 0x49,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x3D, 0x1A, 0x45, 0x17, 0x34, 0x1F, 0x3B, 0x1C,
		0x4D, 0x13, 0x57, 0x0E, 0x43, 0x18, 0x4C, 0x12,
		0x61, 0x0D, 0x5D, 0x0C, 0x58, 0x0C, 0x60, 0x0F,
		0x5B, 0x0C, 0x6D, 0x0E, 0x5D, 0x09, 0x69, 0x0D,
		0x80, 0x11, 0x93, 0x12, 0x80, 0x13, 0x96, 0x16,
		0xA6, 0x13, 0xB3, 0x2B, 0xAF, 0x18, 0xB3, 0x3E,
		0xB5, 0x44, 0xB5, 0x52, 0xB5, 0x50, 0xB5, 0x59,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x29, 0x24, 0x2F, 0x21, 0x25, 0x32, 0x25, 0x2B,
		0x37, 0x1D, 0x3F, 0x16, 0x29, 0x24, 0x30, 0x1E,
		0x4A, 0x0D, 0x5C, 0x0C, 0x38, 0x12, 0x55, 0x24,
		0x5E, 0x0D, 0x66, 0x19, 0x6C, 0x34, 0x6C, 0x42,
		0x80, 0x21, 0x98, 0x25, 0x80, 0x45, 0x94, 0x47,
		0xAC, 0x37, 0xB3, 0x50, 0xAB, 0x50, 0xB3, 0x5B,
		0xB5, 0x59, 0xB5, 0x60, 0xB5, 0x62, 0xB5, 0x67,
		0xB5, 0x65, 0xBA, 0x65, 0xB5, 0x6A, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x25, 0x43, 0x25, 0x3E, 0x25, 0x52, 0x25, 0x50,
		0x25, 0x38, 0x24, 0x2D, 0x25, 0x4D, 0x25, 0x49,
		0x23, 0x1E, 0x42, 0x2C, 0x23, 0x41, 0x34, 0x44,
		0x68, 0x4D, 0x71, 0x4F, 0x58, 0x55, 0x74, 0x6E,
		0x80, 0x55, 0x94, 0x5E, 0x80, 0x74, 0x93, 0x71,
		0xAA, 0x61, 0xB2, 0x67, 0xA9, 0x6F, 0xB2, 0x70,
		0xB5, 0x6A, 0xB5, 0x6C, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6F, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x62, 0x25, 0x62, 0x25, 0x79, 0x25, 0x7D,
		0x25, 0x62, 0x28, 0x63, 0x25, 0x80, 0x2C, 0x86,
		0x2C, 0x64, 0x3F, 0x6A, 0x3A, 0x8C, 0x4D, 0x90,
		0x5A, 0x73, 0x70, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x92, 0x80, 0x82, 0x96, 0x92, 0x92,
		0xA8, 0x80, 0xB2, 0x80, 0xA8, 0x94, 0xB2, 0x90,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x90, 0x25, 0x96, 0x25, 0xA0, 0x25, 0xA5,
		0x25, 0x9C, 0x2E, 0x9F, 0x25, 0xAC, 0x2E, 0xB0,
		0x3D, 0xA0, 0x4D, 0xA1, 0x3D, 0xB2, 0x4C, 0xB3,
		0x5E, 0xA1, 0x70, 0xA4, 0x5D, 0xB5, 0x72, 0xBB,
		0x87, 0xAB, 0x94, 0xA4, 0x8B, 0xC6, 0x98, 0xBB,
		0xA7, 0xA7, 0xB2, 0xA1, 0xA5, 0xB8, 0xB2, 0xB2,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAC, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x2F, 0xC0, 0x2E, 0xC3, 0x3C, 0xC3,
		0x3F, 0xC0, 0x4F, 0xC0, 0x49, 0xC3, 0x5A, 0xC3,
		0x63, 0xC1, 0x77, 0xC2, 0x6B, 0xC3, 0x7B, 0xC3,
		0x8B, 0xC3, 0x97, 0xC2, 0x8B, 0xC3, 0x95, 0xC3,
		0xA3, 0xC1, 0xB1, 0xC1, 0x9F, 0xC3, 0xAA, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x50, 0xC3,
		0x54, 0xC3, 0x62, 0xC3, 0x5C, 0xC3, 0x68, 0xC3,
		0x71, 0xC3, 0x7D, 0xC3, 0x74, 0xC3, 0x7F, 0xC3,
		0x8B, 0xC3, 0x93, 0xC3, 0x8B, 0xC3, 0x91, 0xC3,
		0x9C, 0xC3, 0xA5, 0xC3, 0x99, 0xC3, 0xA1, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA8, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x57, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x62, 0xC3, 0x6D, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC3, 0x81, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x8B, 0xC3, 0x91, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x97, 0xC3, 0x9E, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA5, 0xC3, 0xAB, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x59, 0x2D, 0x61, 0x30, 0x57, 0x35, 0x5E, 0x38,
		0x68, 0x33, 0x6C, 0x37, 0x66, 0x3A, 0x6D, 0x3D,
		0x6B, 0x3B, 0x6B, 0x3E, 0x6E, 0x41, 0x6E, 0x45,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x91, 0x40, 0x9A, 0x40, 0x91, 0x48, 0x9B, 0x49,
		0xA5, 0x42, 0xB0, 0x45, 0xA6, 0x4C, 0xB0, 0x4E,
		0xBA, 0x49, 0xC1, 0x4A, 0xB8, 0x50, 0xC0, 0x51,
		0xC8, 0x4A, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x54, 0x3D, 0x5B, 0x3F, 0x51, 0x44, 0x59, 0x47,
		0x63, 0x42, 0x6A, 0x45, 0x60, 0x49, 0x68, 0x4C,
		0x71, 0x48, 0x70, 0x4C, 0x6F, 0x4F, 0x73, 0x53,
		0x70, 0x4F, 0x78, 0x50, 0x73, 0x56, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x8A, 0x4F,
		0x91, 0x4F, 0x9B, 0x52, 0x91, 0x58, 0x9C, 0x5B,
		0xA6, 0x55, 0xAF, 0x57, 0xA6, 0x5E, 0xAD, 0x5E,
		0xB6, 0x58, 0xBE, 0x58, 0xB5, 0x5F, 0xBD, 0x60,
		0xC5, 0x59, 0x00, 0x00, 0xC3, 0x5F, 0x00, 0x00,
		0x4E, 0x4C, 0x56, 0x4E, 0x4C, 0x53, 0x53, 0x56,
		0x5D, 0x51, 0x65, 0x54, 0x5B, 0x59, 0x62, 0x5B,
		0x6C, 0x56, 0x74, 0x59, 0x6A, 0x5E, 0x6F, 0x5C,
		0x74, 0x5A, 0x76, 0x59, 0x75, 0x59, 0x74, 0x5A,
		0x80, 0x54, 0x8D, 0x4E, 0x80, 0x54, 0x90, 0x51,
		0x92, 0x61, 0x9D, 0x64, 0x93, 0x6A, 0x9B, 0x6C,
		0xA4, 0x65, 0xAC, 0x66, 0xA3, 0x6C, 0xAA, 0x6C,
		0xB3, 0x66, 0xBA, 0x65, 0xB1, 0x6B, 0xB9, 0x6A,
		0xC1, 0x65, 0x00, 0x00, 0xC0, 0x69, 0x00, 0x00,
		0x49, 0x5B, 0x50, 0x5D, 0x46, 0x63, 0x4D, 0x65,
		0x58, 0x60, 0x5F, 0x63, 0x55, 0x68, 0x57, 0x67,
		0x63, 0x62, 0x68, 0x5F, 0x5A, 0x66, 0x60, 0x66,
		0x6F, 0x5C, 0x75, 0x5C, 0x68, 0x67, 0x75, 0x69,
		0x80, 0x5D, 0x90, 0x65, 0x80, 0x6E, 0x90, 0x74,
		0x92, 0x72, 0x99, 0x73, 0x91, 0x78, 0x98, 0x78,
		0xA1, 0x72, 0xA8, 0x71, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x43, 0x6B, 0x4A, 0x6E, 0x31, 0x7A, 0x33, 0x7D,
		0x4A, 0x6E, 0x4F, 0x6F, 0x37, 0x80, 0x2F, 0x86,
		0x56, 0x72, 0x60, 0x75, 0x25, 0x90, 0x4D, 0x90,
		0x6B, 0x79, 0x74, 0x7D, 0x5F, 0x90, 0x6F, 0x90,
		0x80, 0x80, 0x90, 0x80, 0x82, 0x91, 0x90, 0x90,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x05, 0x96, 0x28, 0x96, 0x3F, 0x97, 0x47, 0x97,
		0x3B, 0x96, 0x47, 0x96, 0x4F, 0x97, 0x57, 0x97,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x76, 0x96, 0x70, 0x98, 0x7A, 0x98,
		0x83, 0x96, 0x8C, 0x96, 0x83, 0x97, 0x89, 0x97,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x97, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x3F, 0x9F, 0x47, 0x9F, 0x3F, 0xA7, 0x48, 0xA8,
		0x4F, 0x9F, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x69, 0xA8,
		0x71, 0xA0, 0x7B, 0xA0, 0x73, 0xA9, 0x7D, 0xA8,
		0x85, 0x9F, 0x8B, 0x9F, 0x86, 0xA7, 0x8C, 0xA6,
		0x91, 0x9F, 0x98, 0xA0, 0x92, 0xA6, 0x98, 0xA7,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x6A, 0xB1, 0x62, 0xB9, 0x6C, 0xB9,
		0x75, 0xB1, 0x7E, 0xB0, 0x76, 0xB9, 0x7F, 0xB8,
		0x87, 0xAF, 0x8D, 0xAE, 0x89, 0xB7, 0x8E, 0xB6,
		0x93, 0xAE, 0x99, 0xAF, 0x94, 0xB6, 0x9A, 0xB6,
		0xA0, 0xAF, 0xA8, 0xB0, 0xA1, 0xB7, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x59, 0xC0, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xC1, 0x6E, 0xC1, 0x00, 0x00, 0x00, 0x00,
		0x77, 0xC0, 0x80, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x8A, 0xBE, 0x8F, 0xBE, 0x00, 0x00, 0x00, 0x00,
		0x95, 0xBD, 0x9B, 0xBD, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xBE, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_cem_table1 = {
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x5B, 0x52, 0x64, 0x52,
		0x70, 0x50, 0x78, 0x50, 0x6D, 0x52, 0x76, 0x52,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x52, 0x89, 0x52,
		0x90, 0x50, 0x98, 0x50, 0x92, 0x52, 0x9B, 0x52,
		0xA0, 0x50, 0xA8, 0x50, 0xA4, 0x52, 0xAD, 0x52,
		0xB0, 0x50, 0xB8, 0x50, 0xB6, 0x52, 0xBF, 0x52,
		0xBF, 0x50, 0x00, 0x00, 0xC8, 0x52, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x5B, 0x5B, 0x5D, 0x52, 0x5B, 0x64, 0x5B, 0x5B,
		0x69, 0x52, 0x74, 0x52, 0x61, 0x52, 0x70, 0x52,
		0x80, 0x52, 0x8B, 0x52, 0x80, 0x52, 0x8F, 0x52,
		0x96, 0x52, 0xA2, 0x52, 0x9E, 0x52, 0xAD, 0x52,
		0xAD, 0x52, 0xB9, 0x52, 0xBC, 0x52, 0xCC, 0x52,
		0xC4, 0x52, 0xCF, 0x52, 0xDB, 0x52, 0xEA, 0x52,
		0xDA, 0x52, 0x00, 0x00, 0xEB, 0x57, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x5B, 0x6D, 0x5B, 0x67, 0x5B, 0x76, 0x5B, 0x73,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xDF, 0x60, 0xEB, 0x61, 0xDF, 0x70, 0xEB, 0x70,
		0xEB, 0x64, 0x00, 0x00, 0xEB, 0x72, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x5B, 0x80, 0x5B, 0x80, 0x5B, 0x89, 0x5B, 0x8C,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xDF, 0x80, 0xEB, 0x80, 0xDF, 0x8F, 0xEB, 0x8F,
		0xEB, 0x80, 0x00, 0x00, 0xEB, 0x8D, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x5B, 0x92, 0x5B, 0x98, 0x5B, 0x9B, 0x5B, 0xA4,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xDF, 0x9F, 0xEB, 0x9E, 0xDB, 0xAD, 0xE2, 0xAA,
		0xEB, 0x9B, 0x00, 0x00, 0xE8, 0xA7, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x5B, 0xA4, 0x5B, 0xB0, 0x5B, 0xAD, 0x5B, 0xBC,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xD0, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9E, 0xCB, 0xA9, 0xC5,
		0xBC, 0xBC, 0xC5, 0xB7, 0xB3, 0xC0, 0xBC, 0xBC,
		0xCE, 0xB4, 0xD5, 0xB0, 0xC4, 0xB8, 0xCA, 0xB5,
		0xDA, 0xAD, 0x00, 0x00, 0xD0, 0xB2, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x5B, 0xB6, 0x5B, 0xC8, 0x5B, 0xBF, 0x5B, 0xD4,
		0x60, 0xDF, 0x70, 0xDF, 0x62, 0xE7, 0x72, 0xE0,
		0x80, 0xD9, 0x8D, 0xD2, 0x80, 0xD9, 0x8B, 0xD3,
		0x99, 0xCD, 0xA4, 0xC8, 0x96, 0xCE, 0x9F, 0xCA,
		0xAD, 0xC3, 0xB5, 0xBF, 0xA8, 0xC6, 0xAF, 0xC2,
		0xBC, 0xBC, 0xC2, 0xB9, 0xB6, 0xBF, 0xBC, 0xBC,
		0xC8, 0xB6, 0x00, 0x00, 0xC1, 0xB9, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x5B, 0xC7, 0x5B, 0xE0, 0x00, 0x00, 0x00, 0x00,
		0x66, 0xE5, 0x74, 0xDF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xD9, 0x8A, 0xD4, 0x00, 0x00, 0x00, 0x00,
		0x94, 0xCF, 0x9C, 0xCB, 0x00, 0x00, 0x00, 0x00,
		0xA4, 0xC8, 0xAB, 0xC4, 0x00, 0x00, 0x00, 0x00,
		0xB1, 0xC1, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x37, 0x37, 0x37, 0x2D, 0x37, 0x40, 0x37, 0x37,
		0x3B, 0x24, 0x46, 0x24, 0x37, 0x2B, 0x3E, 0x24,
		0x52, 0x24, 0x5D, 0x24, 0x4B, 0x24, 0x58, 0x24,
		0x69, 0x24, 0x74, 0x24, 0x65, 0x24, 0x72, 0x24,
		0x80, 0x24, 0x8B, 0x24, 0x80, 0x24, 0x8D, 0x24,
		0x96, 0x24, 0xA2, 0x24, 0x9A, 0x24, 0xA7, 0x24,
		0xAD, 0x24, 0xB9, 0x24, 0xB4, 0x24, 0xC1, 0x24,
		0xC4, 0x24, 0xC7, 0x2E, 0xC7, 0x2C, 0xC7, 0x38,
		0xC7, 0x37, 0x00, 0x00, 0xC7, 0x40, 0x00, 0x00,
		0x37, 0x49, 0x37, 0x42, 0x37, 0x52, 0x37, 0x4C,
		0x37, 0x37, 0x37, 0x29, 0x37, 0x43, 0x37, 0x37,
		0x43, 0x24, 0x52, 0x24, 0x40, 0x30, 0x50, 0x30,
		0x61, 0x24, 0x70, 0x24, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x24, 0x8F, 0x24, 0x80, 0x30, 0x8F, 0x30,
		0x9E, 0x24, 0xAD, 0x24, 0x9F, 0x30, 0xAF, 0x30,
		0xBC, 0x24, 0xC7, 0x29, 0xBF, 0x30, 0xC7, 0x38,
		0xC7, 0x38, 0xC7, 0x42, 0xC7, 0x44, 0xC7, 0x4C,
		0xC7, 0x49, 0x00, 0x00, 0xC7, 0x52, 0x00, 0x00,
		0x37, 0x5B, 0x37, 0x56, 0x37, 0x64, 0x37, 0x61,
		0x37, 0x4F, 0x37, 0x46, 0x37, 0x5B, 0x37, 0x54,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xC7, 0x46, 0xBF, 0x50, 0xC7, 0x54,
		0xC7, 0x50, 0xC7, 0x57, 0xC7, 0x5C, 0xC7, 0x61,
		0xC7, 0x5B, 0x00, 0x00, 0xC7, 0x64, 0x00, 0x00,
		0x37, 0x6D, 0x37, 0x6B, 0x37, 0x76, 0x37, 0x75,
		0x37, 0x67, 0x37, 0x63, 0x37, 0x73, 0x37, 0x71,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xC7, 0x63, 0xBF, 0x70, 0xC7, 0x71,
		0xC7, 0x68, 0xC7, 0x6B, 0xC7, 0x74, 0xC7, 0x75,
		0xC7, 0x6D, 0x00, 0x00, 0xC7, 0x76, 0x00, 0x00,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x89, 0x37, 0x8A,
		0x37, 0x80, 0x37, 0x80, 0x37, 0x8C, 0x37, 0x8E,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xC7, 0x80, 0xBF, 0x8F, 0xC7, 0x8E,
		0xC7, 0x80, 0xC7, 0x80, 0xC7, 0x8B, 0xC7, 0x8A,
		0xC7, 0x80, 0x00, 0x00, 0xC7, 0x89, 0x00, 0x00,
		0x37, 0x92, 0x37, 0x94, 0x37, 0x9B, 0x37, 0x9E,
		0x37, 0x98, 0x37, 0x9C, 0x37, 0xA4, 0x37, 0xAB,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xC7, 0x9C, 0xBF, 0xAF, 0xC7, 0xAB,
		0xC7, 0x97, 0xC7, 0x94, 0xC7, 0xA3, 0xC7, 0x9E,
		0xC7, 0x92, 0x00, 0x00, 0xC7, 0x9B, 0x00, 0x00,
		0x37, 0xA4, 0x37, 0xA9, 0x37, 0xAD, 0x37, 0xB3,
		0x37, 0xB0, 0x37, 0xB9, 0x37, 0xBC, 0x37, 0xC8,
		0x40, 0xBF, 0x50, 0xBF, 0x40, 0xCF, 0x50, 0xCF,
		0x60, 0xBF, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xC7, 0xB9, 0xBF, 0xCF, 0xC7, 0xC7,
		0xC7, 0xAF, 0xC7, 0xA8, 0xC7, 0xBB, 0xC7, 0xB3,
		0xC7, 0xA4, 0x00, 0x00, 0xC7, 0xAD, 0x00, 0x00,
		0x37, 0xB6, 0x37, 0xBD, 0x37, 0xBF, 0x37, 0xC8,
		0x37, 0xC8, 0x37, 0xD6, 0x37, 0xD4, 0x3F, 0xDA,
		0x43, 0xDA, 0x52, 0xDA, 0x4C, 0xDA, 0x59, 0xDA,
		0x62, 0xDA, 0x71, 0xDA, 0x66, 0xDA, 0x73, 0xDA,
		0x80, 0xDA, 0x8F, 0xDA, 0x80, 0xDA, 0x8C, 0xDA,
		0x9E, 0xDA, 0xAD, 0xDA, 0x99, 0xDA, 0xA6, 0xDA,
		0xBC, 0xDA, 0xC7, 0xD6, 0xB3, 0xDA, 0xC0, 0xDA,
		0xC7, 0xC7, 0xC7, 0xBD, 0xC7, 0xD3, 0xC7, 0xC7,
		0xC7, 0xB6, 0x00, 0x00, 0xC7, 0xBF, 0x00, 0x00,
		0x37, 0xC7, 0x37, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0x3B, 0xDA, 0x47, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x52, 0xDA, 0x5E, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x69, 0xDA, 0x74, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xDA, 0x8B, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0x96, 0xDA, 0xA2, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xAD, 0xDA, 0xB9, 0xDA, 0x00, 0x00, 0x00, 0x00,
		0xC4, 0xDA, 0xC7, 0xD1, 0x00, 0x00, 0x00, 0x00,
		0xC7, 0xC7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x3E, 0x47, 0x44, 0x44,
		0x4F, 0x3F, 0x55, 0x3C, 0x4A, 0x41, 0x51, 0x3E,
		0x5C, 0x38, 0x63, 0x35, 0x58, 0x3A, 0x60, 0x36,
		0x6C, 0x31, 0x75, 0x2C, 0x69, 0x32, 0x74, 0x2D,
		0x80, 0x27, 0x8B, 0x22, 0x80, 0x27, 0x8D, 0x21,
		0x99, 0x1B, 0xA3, 0x21, 0x9D, 0x19, 0xA3, 0x2D,
		0xA3, 0x38, 0xA8, 0x40, 0xA3, 0x41, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x38, 0x4A, 0x3E, 0x47, 0x30, 0x4E, 0x36, 0x4B,
		0x44, 0x44, 0x4B, 0x41, 0x3C, 0x48, 0x44, 0x44,
		0x53, 0x3D, 0x5C, 0x38, 0x4D, 0x40, 0x56, 0x3B,
		0x66, 0x33, 0x72, 0x2E, 0x62, 0x36, 0x70, 0x30,
		0x80, 0x27, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xA3, 0x38, 0x9F, 0x30, 0xA3, 0x44,
		0xA3, 0x4A, 0xA8, 0x50, 0xA3, 0x53, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x26, 0x53, 0x2C, 0x50, 0x19, 0x59, 0x1F, 0x56,
		0x33, 0x4C, 0x3B, 0x48, 0x26, 0x53, 0x30, 0x50,
		0x44, 0x44, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xA3, 0x50, 0x9F, 0x50, 0xA3, 0x5C,
		0xA3, 0x5C, 0xA8, 0x60, 0xA3, 0x65, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x13, 0x64, 0x13, 0x61, 0x13, 0x72, 0x13, 0x70,
		0x20, 0x60, 0x30, 0x60, 0x20, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xA3, 0x68, 0x9F, 0x70, 0xA3, 0x74,
		0xA3, 0x6E, 0xA8, 0x70, 0xA3, 0x77, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x13, 0x80, 0x13, 0x80, 0x13, 0x8D, 0x13, 0x8F,
		0x20, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xA3, 0x80, 0x9F, 0x8F, 0xA3, 0x8B,
		0xA3, 0x80, 0xA8, 0x80, 0xA3, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x13, 0x9B, 0x13, 0x9E, 0x13, 0xA8, 0x17, 0xAC,
		0x20, 0x9F, 0x30, 0x9F, 0x26, 0xAC, 0x35, 0xAC,
		0x40, 0x9F, 0x50, 0x9F, 0x44, 0xAC, 0x53, 0xAC,
		0x60, 0x9F, 0x70, 0x9F, 0x62, 0xAC, 0x71, 0xAC,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8E, 0xAC,
		0x9F, 0x9F, 0xA3, 0x97, 0x9D, 0xAC, 0xA3, 0xA3,
		0xA3, 0x91, 0xA8, 0x90, 0xA3, 0x9A, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x26, 0xAC, 0x31, 0xAC, 0x38, 0xAC, 0x41, 0xAC,
		0x3C, 0xAC, 0x47, 0xAC, 0x4A, 0xAC, 0x53, 0xAC,
		0x53, 0xAC, 0x5E, 0xAC, 0x5C, 0xAC, 0x65, 0xAC,
		0x69, 0xAC, 0x74, 0xAC, 0x6E, 0xAC, 0x77, 0xAC,
		0x80, 0xAC, 0x8B, 0xAC, 0x80, 0xAC, 0x88, 0xAC,
		0x96, 0xAC, 0xA1, 0xAC, 0x91, 0xAC, 0x9A, 0xAC,
		0xA3, 0xA3, 0xA8, 0xA0, 0xA3, 0xAC, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x70, 0x70, 0x78, 0x70, 0x70, 0x78, 0x78, 0x78,
		0x80, 0x70, 0x88, 0x70, 0x80, 0x78, 0x88, 0x78,
		0x90, 0x70, 0x98, 0x70, 0x90, 0x78, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x70, 0x80, 0x78, 0x80, 0x70, 0x88, 0x78, 0x88,
		0x80, 0x80, 0x88, 0x80, 0x80, 0x88, 0x88, 0x88,
		0x90, 0x80, 0x98, 0x80, 0x90, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x70, 0x90, 0x78, 0x90, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x90, 0x88, 0x90, 0x80, 0x98, 0x88, 0x98,
		0x90, 0x90, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x58, 0x88, 0x58,
		0x90, 0x50, 0x98, 0x50, 0x90, 0x58, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x58, 0x68,
		0x60, 0x60, 0x68, 0x60, 0x60, 0x68, 0x68, 0x68,
		0x70, 0x60, 0x78, 0x60, 0x70, 0x68, 0x78, 0x68,
		0x80, 0x60, 0x88, 0x60, 0x80, 0x68, 0x88, 0x68,
		0x90, 0x60, 0x98, 0x60, 0x90, 0x68, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x50, 0x70, 0x58, 0x70, 0x50, 0x78, 0x58, 0x78,
		0x60, 0x70, 0x68, 0x70, 0x60, 0x78, 0x68, 0x78,
		0x6D, 0x6D, 0x74, 0x69, 0x6D, 0x76, 0x70, 0x70,
		0x80, 0x69, 0x8B, 0x69, 0x80, 0x70, 0x8F, 0x70,
		0x96, 0x69, 0xA2, 0x69, 0x9F, 0x70, 0xAF, 0x70,
		0xAD, 0x69, 0xB9, 0x69, 0xBF, 0x70, 0xCF, 0x70,
		0xC4, 0x69, 0xCF, 0x69, 0xDF, 0x70, 0xEF, 0x70,
		0xDA, 0x69, 0x00, 0x00, 0xFD, 0x70, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x50, 0x80, 0x58, 0x80, 0x50, 0x88, 0x58, 0x88,
		0x60, 0x80, 0x68, 0x80, 0x60, 0x88, 0x68, 0x88,
		0x6D, 0x80, 0x70, 0x80, 0x6D, 0x89, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBD, 0x8F, 0xC1, 0x8D,
		0xDD, 0x80, 0xDD, 0x80, 0xC5, 0x8B, 0xC7, 0x8A,
		0xDD, 0x80, 0x00, 0x00, 0xC9, 0x89, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x50, 0x90, 0x58, 0x90, 0x50, 0x98, 0x58, 0x98,
		0x60, 0x90, 0x68, 0x90, 0x60, 0x98, 0x68, 0x98,
		0x6D, 0x92, 0x70, 0xA0, 0x6D, 0x9B, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAC, 0x8C, 0xA6,
		0x9E, 0x9E, 0xA7, 0x9A, 0x96, 0xA1, 0x9E, 0x9E,
		0xAD, 0x96, 0xB2, 0x94, 0xA4, 0x9B, 0xA9, 0x98,
		0xB6, 0x92, 0xBA, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x6D, 0xA4, 0x73, 0xB2, 0x6D, 0xAD, 0x76, 0xB1,
		0x80, 0xAC, 0x89, 0xA7, 0x80, 0xAC, 0x88, 0xA8,
		0x92, 0xA4, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x6E, 0xB5, 0x77, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x4C, 0x3B, 0x55, 0x3B, 0x49, 0x40, 0x4F, 0x3B,
		0x5D, 0x3B, 0x66, 0x3B, 0x58, 0x3B, 0x62, 0x3B,
		0x6E, 0x3B, 0x77, 0x3B, 0x6C, 0x3B, 0x76, 0x3B,
		0x80, 0x3B, 0x88, 0x3B, 0x80, 0x3B, 0x89, 0x3B,
		0x91, 0x3B, 0x99, 0x3B, 0x93, 0x3B, 0x9D, 0x3B,
		0xA2, 0x3B, 0xAA, 0x3B, 0xA7, 0x3B, 0xB0, 0x3B,
		0xB3, 0x3B, 0xBB, 0x3B, 0xBA, 0x3B, 0xC4, 0x3B,
		0xC3, 0x3B, 0x00, 0x00, 0xCD, 0x3B, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x49, 0x49, 0x49, 0x3E, 0x49, 0x52, 0x49, 0x49,
		0x52, 0x3B, 0x5D, 0x3B, 0x49, 0x3C, 0x56, 0x3B,
		0x69, 0x3B, 0x74, 0x3B, 0x64, 0x3B, 0x72, 0x3B,
		0x80, 0x3B, 0x8B, 0x3B, 0x80, 0x3B, 0x8D, 0x3B,
		0x96, 0x3B, 0xA2, 0x3B, 0x9B, 0x3B, 0xA9, 0x3B,
		0xAD, 0x3B, 0xB9, 0x3B, 0xB6, 0x3B, 0xC4, 0x3B,
		0xC4, 0x3B, 0xCF, 0x3B, 0xD2, 0x3B, 0xD9, 0x3F,
		0xD9, 0x3C, 0x00, 0x00, 0xD9, 0x47, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x49, 0x5B, 0x49, 0x54, 0x49, 0x64, 0x49, 0x5F,
		0x49, 0x49, 0x50, 0x40, 0x49, 0x57, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xBF, 0x40, 0xCF, 0x40, 0xBF, 0x50, 0xCF, 0x50,
		0xD9, 0x44, 0xD9, 0x4C, 0xD9, 0x53, 0xD9, 0x59,
		0xD9, 0x52, 0x00, 0x00, 0xD9, 0x5E, 0x00, 0x00,
		0x40, 0x70, 0x48, 0x70, 0x40, 0x78, 0x48, 0x78,
		0x49, 0x6D, 0x49, 0x6A, 0x49, 0x76, 0x49, 0x75,
		0x49, 0x64, 0x50, 0x60, 0x49, 0x72, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xBF, 0x60, 0xCF, 0x60, 0xBF, 0x70, 0xCF, 0x70,
		0xD9, 0x62, 0xD9, 0x66, 0xD9, 0x71, 0xD9, 0x73,
		0xD9, 0x69, 0x00, 0x00, 0xD9, 0x74, 0x00, 0x00,
		0x40, 0x80, 0x48, 0x80, 0x40, 0x88, 0x48, 0x88,
		0x49, 0x80, 0x49, 0x80, 0x49, 0x89, 0x49, 0x8A,
		0x49, 0x80, 0x50, 0x80, 0x49, 0x8D, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xBF, 0x80, 0xCF, 0x80, 0xBF, 0x8F, 0xCF, 0x8F,
		0xD9, 0x80, 0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x8C,
		0xD9, 0x80, 0x00, 0x00, 0xD9, 0x8B, 0x00, 0x00,
		0x40, 0x90, 0x48, 0x90, 0x40, 0x98, 0x48, 0x98,
		0x49, 0x92, 0x49, 0x95, 0x49, 0x9B, 0x49, 0xA0,
		0x49, 0x9B, 0x50, 0x9F, 0x49, 0xA8, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0xA0, 0x60, 0xB0, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xBF, 0x9F, 0xCF, 0x9F, 0xBF, 0xAF, 0xCF, 0xAF,
		0xD9, 0x9D, 0xD9, 0x99, 0xD9, 0xAC, 0xD9, 0xA6,
		0xD9, 0x96, 0x00, 0x00, 0xD9, 0xA1, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x49, 0xA4, 0x49, 0xAB, 0x49, 0xAD, 0x49, 0xB6,
		0x49, 0xB6, 0x50, 0xBF, 0x49, 0xC3, 0x50, 0xD0,
		0x60, 0xC0, 0x70, 0xBF, 0x60, 0xCF, 0x70, 0xCF,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xCF, 0x8F, 0xCF,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9F, 0xCF, 0xAF, 0xCF,
		0xBF, 0xBF, 0xCF, 0xBF, 0xBF, 0xCF, 0xCF, 0xCF,
		0xD9, 0xBB, 0xD9, 0xB3, 0xD9, 0xCA, 0xD9, 0xC0,
		0xD9, 0xAD, 0x00, 0x00, 0xD9, 0xB8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x49, 0xB6, 0x49, 0xC1, 0x49, 0xBF, 0x49, 0xCB,
		0x49, 0xD1, 0x50, 0xE0, 0x49, 0xDF, 0x50, 0xEF,
		0x60, 0xDF, 0x70, 0xDF, 0x60, 0xEF, 0x70, 0xEF,
		0x80, 0xDF, 0x8F, 0xDF, 0x80, 0xEF, 0x8F, 0xEF,
		0x9F, 0xDF, 0xAF, 0xDF, 0x9F, 0xEF, 0xAF, 0xEF,
		0xBF, 0xDF, 0xCF, 0xDF, 0xBC, 0xE9, 0xC7, 0xE4,
		0xD9, 0xD9, 0xD9, 0xCC, 0xD1, 0xDF, 0xD9, 0xD9,
		0xD9, 0xC3, 0x00, 0x00, 0xD9, 0xCF, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xC7, 0x49, 0xD6, 0x00, 0x00, 0x00, 0x00,
		0x49, 0xEB, 0x55, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x63, 0xF1, 0x71, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xF1, 0x8E, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0x9C, 0xF1, 0xAA, 0xF1, 0x00, 0x00, 0x00, 0x00,
		0xB6, 0xEC, 0xC0, 0xE7, 0x00, 0x00, 0x00, 0x00,
		0xCA, 0xE2, 0xD3, 0xDE, 0x00, 0x00, 0x00, 0x00,
		0xD9, 0xD9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x26, 0x26, 0x2E, 0x22, 0x25, 0x30, 0x26, 0x26,
		0x36, 0x1E, 0x40, 0x19, 0x2F, 0x21, 0x39, 0x1D,
		0x4A, 0x14, 0x55, 0x0F, 0x44, 0x17, 0x50, 0x11,
		0x63, 0x0D, 0x71, 0x0D, 0x60, 0x10, 0x70, 0x10,
		0x80, 0x0D, 0x8E, 0x0D, 0x80, 0x10, 0x8F, 0x10,
		0x9C, 0x0D, 0xAA, 0x0D, 0x9F, 0x10, 0xAF, 0x10,
		0xB5, 0x14, 0xB5, 0x2A, 0xB5, 0x22, 0xB5, 0x34,
		0xB5, 0x38, 0xB8, 0x40, 0xB5, 0x41, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x25, 0x3C, 0x25, 0x32, 0x25, 0x47, 0x25, 0x3F,
		0x26, 0x26, 0x30, 0x21, 0x25, 0x34, 0x30, 0x30,
		0x40, 0x20, 0x50, 0x20, 0x40, 0x30, 0x50, 0x30,
		0x60, 0x20, 0x70, 0x20, 0x60, 0x30, 0x70, 0x30,
		0x80, 0x20, 0x8F, 0x20, 0x80, 0x30, 0x8F, 0x30,
		0x9F, 0x20, 0xAF, 0x20, 0x9F, 0x30, 0xAF, 0x30,
		0xB5, 0x2F, 0xB5, 0x3F, 0xB5, 0x3C, 0xB5, 0x4A,
		0xB5, 0x4A, 0xB8, 0x50, 0xB5, 0x53, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x25, 0x52, 0x25, 0x4C, 0x25, 0x5E, 0x25, 0x59,
		0x25, 0x43, 0x30, 0x40, 0x25, 0x52, 0x30, 0x50,
		0x40, 0x40, 0x50, 0x40, 0x40, 0x50, 0x50, 0x50,
		0x60, 0x40, 0x70, 0x40, 0x60, 0x50, 0x70, 0x50,
		0x80, 0x40, 0x8F, 0x40, 0x80, 0x50, 0x8F, 0x50,
		0x9F, 0x40, 0xAF, 0x40, 0x9F, 0x50, 0xAF, 0x50,
		0xB5, 0x4A, 0xB5, 0x55, 0xB5, 0x57, 0xB5, 0x5F,
		0xB5, 0x5C, 0xB8, 0x60, 0xB5, 0x65, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x25, 0x69, 0x25, 0x66, 0x25, 0x74, 0x25, 0x73,
		0x25, 0x61, 0x30, 0x60, 0x25, 0x70, 0x30, 0x70,
		0x40, 0x60, 0x50, 0x60, 0x40, 0x70, 0x50, 0x70,
		0x60, 0x60, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x9F, 0x60, 0xAF, 0x60, 0x9F, 0x70, 0xAF, 0x70,
		0xB5, 0x65, 0xB5, 0x6A, 0xB5, 0x72, 0xB5, 0x75,
		0xB5, 0x6E, 0xB8, 0x70, 0xB5, 0x77, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x25, 0x8B, 0x25, 0x8C,
		0x25, 0x80, 0x30, 0x80, 0x25, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x9F, 0x80, 0xAF, 0x80, 0x9F, 0x8F, 0xAF, 0x8F,
		0xB5, 0x80, 0xB5, 0x80, 0xB5, 0x8D, 0xB5, 0x8A,
		0xB5, 0x80, 0xB8, 0x80, 0xB5, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x25, 0x96, 0x25, 0x99, 0x25, 0xA1, 0x25, 0xA6,
		0x25, 0x9E, 0x30, 0x9F, 0x25, 0xAD, 0x30, 0xAF,
		0x40, 0x9F, 0x50, 0x9F, 0x40, 0xAF, 0x50, 0xAF,
		0x60, 0x9F, 0x70, 0x9F, 0x60, 0xAF, 0x70, 0xAF,
		0x80, 0x9F, 0x8F, 0x9F, 0x80, 0xAF, 0x8F, 0xAF,
		0x9F, 0x9F, 0xAF, 0x9F, 0x9F, 0xAF, 0xAF, 0xAF,
		0xB5, 0x9A, 0xB5, 0x95, 0xB5, 0xA8, 0xB5, 0xA0,
		0xB5, 0x91, 0xB8, 0x90, 0xB5, 0x9A, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x25, 0xAD, 0x25, 0xB3, 0x25, 0xB8, 0x25, 0xC0,
		0x25, 0xBC, 0x30, 0xBF, 0x2E, 0xC3, 0x3C, 0xC3,
		0x40, 0xBF, 0x50, 0xBF, 0x49, 0xC3, 0x57, 0xC3,
		0x60, 0xBF, 0x70, 0xBF, 0x65, 0xC3, 0x72, 0xC3,
		0x80, 0xBF, 0x8F, 0xBF, 0x80, 0xC3, 0x8D, 0xC3,
		0x9F, 0xBF, 0xAF, 0xBF, 0x9B, 0xC3, 0xA8, 0xC3,
		0xB5, 0xB5, 0xB5, 0xAA, 0xB5, 0xC3, 0xB5, 0xB5,
		0xB5, 0xA3, 0xB8, 0xA0, 0xB5, 0xAC, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x25, 0xC3, 0x30, 0xC3, 0x32, 0xC3, 0x3C, 0xC3,
		0x3C, 0xC3, 0x47, 0xC3, 0x45, 0xC3, 0x4F, 0xC3,
		0x52, 0xC3, 0x5E, 0xC3, 0x59, 0xC3, 0x63, 0xC3,
		0x69, 0xC3, 0x74, 0xC3, 0x6C, 0xC3, 0x76, 0xC3,
		0x80, 0xC3, 0x8B, 0xC3, 0x80, 0xC3, 0x89, 0xC3,
		0x96, 0xC3, 0xA1, 0xC3, 0x93, 0xC3, 0x9D, 0xC3,
		0xAD, 0xC3, 0xB5, 0xC0, 0xA6, 0xC3, 0xB0, 0xC3,
		0xB5, 0xB5, 0xB8, 0xB0, 0xB5, 0xBE, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x3B, 0xC3, 0x44, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x4C, 0xC3, 0x55, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x5D, 0xC3, 0x66, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x6F, 0xC3, 0x77, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xC3, 0x88, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0x91, 0xC3, 0x99, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xA2, 0xC3, 0xAA, 0xC3, 0x00, 0x00, 0x00, 0x00,
		0xB3, 0xC3, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x40, 0x40, 0x48, 0x40, 0x40, 0x48, 0x48, 0x48,
		0x50, 0x40, 0x58, 0x40, 0x50, 0x48, 0x58, 0x48,
		0x60, 0x40, 0x68, 0x40, 0x60, 0x48, 0x68, 0x48,
		0x70, 0x40, 0x78, 0x40, 0x70, 0x48, 0x78, 0x48,
		0x80, 0x40, 0x88, 0x40, 0x80, 0x48, 0x88, 0x48,
		0x90, 0x40, 0x98, 0x40, 0x90, 0x48, 0x98, 0x48,
		0xA0, 0x40, 0xA8, 0x40, 0xA0, 0x48, 0xA8, 0x48,
		0xB0, 0x40, 0xB8, 0x40, 0xB0, 0x48, 0xB8, 0x48,
		0xBF, 0x40, 0x00, 0x00, 0xBF, 0x48, 0x00, 0x00,
		0x40, 0x50, 0x48, 0x50, 0x40, 0x58, 0x48, 0x58,
		0x50, 0x50, 0x58, 0x50, 0x50, 0x58, 0x58, 0x58,
		0x60, 0x50, 0x68, 0x50, 0x60, 0x58, 0x68, 0x58,
		0x70, 0x50, 0x78, 0x50, 0x70, 0x58, 0x78, 0x58,
		0x80, 0x50, 0x88, 0x50, 0x80, 0x54, 0x89, 0x4F,
		0x91, 0x4C, 0x98, 0x50, 0x91, 0x54, 0x98, 0x58,
		0xA0, 0x50, 0xA8, 0x50, 0xA0, 0x58, 0xA8, 0x58,
		0xB0, 0x50, 0xB8, 0x50, 0xB0, 0x58, 0xB8, 0x58,
		0xBF, 0x50, 0x00, 0x00, 0xBF, 0x58, 0x00, 0x00,
		0x40, 0x60, 0x48, 0x60, 0x40, 0x68, 0x48, 0x68,
		0x50, 0x60, 0x58, 0x60, 0x50, 0x68, 0x57, 0x67,
		0x60, 0x60, 0x68, 0x60, 0x5C, 0x65, 0x62, 0x62,
		0x6E, 0x5D, 0x76, 0x59, 0x6A, 0x5F, 0x73, 0x5A,
		0x80, 0x54, 0x8C, 0x4E, 0x80, 0x54, 0x8F, 0x50,
		0x91, 0x5D, 0x98, 0x60, 0x91, 0x65, 0x98, 0x68,
		0xA0, 0x60, 0xA8, 0x60, 0xA0, 0x68, 0xA8, 0x68,
		0xB0, 0x60, 0xB8, 0x60, 0xB0, 0x68, 0xB8, 0x68,
		0xBF, 0x60, 0x00, 0x00, 0xBF, 0x68, 0x00, 0x00,
		0x40, 0x70, 0x47, 0x6F, 0x38, 0x77, 0x3A, 0x76,
		0x4A, 0x6E, 0x4E, 0x6C, 0x3D, 0x74, 0x40, 0x73,
		0x53, 0x69, 0x5A, 0x66, 0x44, 0x71, 0x50, 0x70,
		0x62, 0x62, 0x70, 0x60, 0x60, 0x70, 0x70, 0x70,
		0x80, 0x60, 0x8F, 0x60, 0x80, 0x70, 0x8F, 0x70,
		0x91, 0x6E, 0x98, 0x70, 0x91, 0x77, 0x98, 0x78,
		0xA0, 0x70, 0xA8, 0x70, 0xA0, 0x78, 0xA8, 0x78,
		0xB0, 0x70, 0xB8, 0x70, 0xB0, 0x78, 0xB8, 0x78,
		0xBF, 0x70, 0x00, 0x00, 0xBF, 0x78, 0x00, 0x00,
		0x25, 0x80, 0x25, 0x80, 0x06, 0x8F, 0x10, 0x8F,
		0x25, 0x80, 0x30, 0x80, 0x20, 0x8F, 0x30, 0x8F,
		0x40, 0x80, 0x50, 0x80, 0x40, 0x8F, 0x50, 0x8F,
		0x60, 0x80, 0x70, 0x80, 0x60, 0x8F, 0x70, 0x8F,
		0x80, 0x80, 0x8F, 0x80, 0x80, 0x8F, 0x8F, 0x8F,
		0x91, 0x80, 0x98, 0x80, 0x91, 0x88, 0x98, 0x88,
		0xA0, 0x80, 0xA8, 0x80, 0xA0, 0x88, 0xA8, 0x88,
		0xB0, 0x80, 0xB8, 0x80, 0xB0, 0x88, 0xB8, 0x88,
		0xBF, 0x80, 0x00, 0x00, 0xBF, 0x88, 0x00, 0x00,
		0x27, 0x96, 0x32, 0x96, 0x40, 0x98, 0x48, 0x98,
		0x3D, 0x96, 0x48, 0x96, 0x50, 0x98, 0x58, 0x98,
		0x53, 0x96, 0x5E, 0x96, 0x60, 0x98, 0x68, 0x98,
		0x69, 0x96, 0x74, 0x96, 0x70, 0x98, 0x78, 0x98,
		0x80, 0x96, 0x8B, 0x96, 0x80, 0x98, 0x88, 0x98,
		0x91, 0x91, 0x98, 0x90, 0x90, 0x98, 0x98, 0x98,
		0xA0, 0x90, 0xA8, 0x90, 0xA0, 0x98, 0xA8, 0x98,
		0xB0, 0x90, 0xB8, 0x90, 0xB0, 0x98, 0xB8, 0x98,
		0xBF, 0x90, 0x00, 0x00, 0xBF, 0x98, 0x00, 0x00,
		0x40, 0xA0, 0x48, 0xA0, 0x40, 0xA8, 0x48, 0xA8,
		0x50, 0xA0, 0x58, 0xA0, 0x50, 0xA8, 0x58, 0xA8,
		0x60, 0xA0, 0x68, 0xA0, 0x60, 0xA8, 0x68, 0xA8,
		0x70, 0xA0, 0x78, 0xA0, 0x70, 0xA8, 0x78, 0xA8,
		0x80, 0xA0, 0x88, 0xA0, 0x80, 0xA8, 0x88, 0xA8,
		0x90, 0xA0, 0x98, 0xA0, 0x90, 0xA8, 0x98, 0xA8,
		0xA0, 0xA0, 0xA8, 0xA0, 0xA0, 0xA8, 0xA8, 0xA8,
		0xB0, 0xA0, 0xB8, 0xA0, 0xB0, 0xA8, 0xB8, 0xA8,
		0xBF, 0xA0, 0x00, 0x00, 0xBF, 0xA8, 0x00, 0x00,
		0x40, 0xB0, 0x48, 0xB0, 0x40, 0xB8, 0x48, 0xB8,
		0x50, 0xB0, 0x58, 0xB0, 0x50, 0xB8, 0x58, 0xB8,
		0x60, 0xB0, 0x68, 0xB0, 0x60, 0xB8, 0x68, 0xB8,
		0x70, 0xB0, 0x78, 0xB0, 0x70, 0xB8, 0x78, 0xB8,
		0x80, 0xB0, 0x88, 0xB0, 0x80, 0xB8, 0x88, 0xB8,
		0x90, 0xB0, 0x98, 0xB0, 0x90, 0xB8, 0x98, 0xB8,
		0xA0, 0xB0, 0xA8, 0xB0, 0xA0, 0xB8, 0xA8, 0xB8,
		0xB0, 0xB0, 0xB8, 0xB0, 0xB0, 0xB8, 0xB8, 0xB8,
		0xBF, 0xB0, 0x00, 0x00, 0xBF, 0xB8, 0x00, 0x00,
		0x40, 0xBF, 0x48, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x50, 0xBF, 0x58, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x60, 0xBF, 0x68, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x70, 0xBF, 0x78, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x80, 0xBF, 0x88, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0x90, 0xBF, 0x98, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xA0, 0xBF, 0xA8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xB0, 0xBF, 0xB8, 0xBF, 0x00, 0x00, 0x00, 0x00,
		0xBF, 0xBF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_pltm_table = {
		/* pltm - H */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - V */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - P */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		/* pltm - F */
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	.isp_wdr_table = { 0 }
};
struct isp_cfg_pt gc2355_mipi_isp_cfg = {
	.isp_test_settings = &gc2355_mipi_isp_test_settings,
	.isp_3a_settings = &gc2355_mipi_isp_3a_settings,
	.isp_tunning_settings = &gc2355_mipi_isp_tuning_settings,
	.isp_iso_settings = &gc2355_mipi_isp_iso_settings
};

#endif /* end of _GC2355_MIPI_H_V100_ */
