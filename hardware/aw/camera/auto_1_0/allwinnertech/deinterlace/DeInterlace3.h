/*
 * =====================================================================================
 *
 *       Filename:  DeInterlace3.h
 *
 *    Description:  
 *
 *        Version:  1.0
 *        Created:  2020年03月10日 09时56分48秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  YOUR NAME (), 
 *        Company:  
 *
 * =====================================================================================
 */
#ifndef DIPROCESS_3_H
#define DIPROCESS_3_H

#include "deinterlace3.h"
#include "DIInterFace.h"
#include <pthread.h>
#define DI_DEVICE_NAME "/dev/deinterlace"
#define ALIGN(x,y) (((y) + (x-1)) & ~(x-1))

namespace android {
struct RectCrop {
    int left;
    int top;
    int right;
    int bottom;
};

struct DIParam {
    int filmDetect;
    int tnrOpen; //3D tnr
    int contrastOpen;
    RectCrop contrast;
    DIMode mode;
    int cropOpen;
    RectCrop crop;
};

class DeInterlace3 : public DeInterlace {

public:
    DeInterlace3();
    ~DeInterlace3();
    virtual void init(void) override;
	virtual void release(void) override;
	virtual int setParameter(void* param) override;
	virtual int process(InputData* input,OutputData* output) override;
	virtual void reset(void) override;

private:
    int mFd;
    int mFilmDetect;
    int mTnrOpen; //3D tnr
    int mContrastOpen;
    RectCrop mContrast;
    DIMode mMode;
    int mCropOpen;
    RectCrop mCrop;
    pthread_mutex_t   mDIMutex;
    int openDevice(char *path);
    int closeDevice(void);
    int checkFormat(InputData* input,OutputData* output,DIMode mode);
    int setBufAddr(struct di_fb* fb, DIFrame frame);
    int configBufAndStart(InputData* input,OutputData* output);
};

};

#endif